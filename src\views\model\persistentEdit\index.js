import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import lwFieldsEdit from './components/lwFieldsEdit/index.vue'
import lwIdentityEdit from './components/lwIdentityEdit/index.vue'

export default {
  name: 'modelPersistentEdit',
  components: {
    lwFieldsEdit,
    lwIdentityEdit
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    let persistentType = {
      consumer: t('modelPersistentEdit.keHu'),
      behavior: t('modelPersistentEdit.xingWei'),
      normal: t('modelPersistentEdit.puTong')
    }
    const dataForm = ref({
      name: '',
      aliasName: '',
      useForTag: true,
      enable360: true,
      dataPersistentInfo: { persistentType: 'consumer' }
    })
    const dataFormRef = ref(null)
    const loading = ref(false)

    // 获取数据角色
    const BAR_DATA = $tool.data.get('BAR_DATA') || {}
    const roleList = ref(BAR_DATA.dataRoles || [])

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('modelPersistentEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('modelPersistentEdit.moXingLeiXing'),
            name: 'dataPersistentInfo.persistentType',
            value: 'consumer',
            span: 12,
            component: 'radio',
            options: {
              items: Object.keys(persistentType).map((item) => {
                return {
                  label: `${persistentType[item]}`,
                  value: item
                }
              }),
              disabled: !!id.value
            },
            rules: [{ required: true, message: t('modelPersistentEdit.qingXuanZe'), trigger: 'change' }]
          },
          {
            label: t('modelPersistentEdit.yongYuBiaoQian'),
            name: 'useForTag',
            value: true,
            span: 3,
            component: 'switch',
            options: {
              activeText: t('modelPersistentEdit.yunXu'),
              inactiveText: t('modelPersistentEdit.jinZhi')
            },
            hideHandle: '$.dataPersistentInfo.persistentType == "normal"'
          },
          {
            label: t('modelPersistentEdit.yongYuShiTu'),
            name: 'enable360',
            value: true,
            span: 3,
            component: 'switch',
            options: {
              activeText: t('modelPersistentEdit.yunXu'),
              inactiveText: t('modelPersistentEdit.jinZhi')
            },
            hideHandle: '$.dataPersistentInfo.persistentType != "behavior"'
          },
          {
            label: t('modelPersistentEdit.shiFouWeiWaiBuBiao'),
            name: 'externalModel',
            value: true,
            span: 3,
            component: 'switch',
            options: {
              activeText: t('modelPersistentEdit.shi'),
              inactiveText: t('modelPersistentEdit.fou')
            },
            hideHandle: '$.dataPersistentInfo.persistentType == "normal"'
          },

          {
            label: t('modelPersistentEdit.moXingMingCheng'),
            name: 'name',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelPersistentEdit.qingShuRu'),
              disabled: !!id.value
            },
            tips: t('modelPersistentEdit.MXDYWMCJZCYW'),
            rules: [{ required: true, message: t('modelPersistentEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPersistentEdit.moXingZhongWen'),
            name: 'aliasName',
            value: '',
            span: 12,
            component: 'input',
            options: {
              maxlength: 50,
              placeholder: t('modelPersistentEdit.qingShuRu')
            },
            tips: t('modelPersistentEdit.MXDZWMC'),
            rules: [{ required: true, message: t('modelPersistentEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPersistentEdit.shuJuJueSe'),
            name: 'dataRoles',
            value: [],
            span: 12,
            component: 'cascader',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              props: {
                children: 'children',
                label: 'label',
                value: 'value',
                emitPath: false,
                checkStrictly: true,
                multiple: true,
                checkStrictly: true
              },
              items: roleList.value
            },
            hideHandle: '$.dataPersistentInfo.persistentType == "consumer"'
          },
          {
            label: t('modelPersistentEdit.kuoZhanXinXi'),
            name: 'options',
            value: [],
            span: 24,
            component: 'optionsTable'
          },
          { label: t('modelPersistentEdit.gouJianMoXing'), component: 'divider' },
          { component: 'lwFieldsEdit' },
          {
            label: t('modelPersistentEdit.shenFenBiaoShi'),
            component: 'divider',
            hideHandle: '$.dataPersistentInfo.persistentType == "normal"'
          },
          { component: 'lwIdentityEdit', hideHandle: '$.dataPersistentInfo.persistentType == "normal"' }
        ]
      }
    })

    const configTags = computed(() => {
      return {
        rowKey: 'numberKey',
        formItems: [
          {
            label: t('modelPersistentEdit.mingCheng'),
            name: 'label',
            value: '',
            minWidth: '160',
            component: 'input',
            options: {
              placeholder: t('modelPersistentEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('modelPersistentEdit.buNengWeiKong'), trigger: 'blur' }]
          },
          {
            label: t('modelPersistentEdit.peiZhi'),
            name: 'value',
            value: '',
            minWidth: '160',
            component: 'input',
            options: {
              placeholder: t('modelPersistentEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('modelPersistentEdit.buNengWeiKong'), trigger: 'blur' }]
          },
          {
            label: t('modelPersistentEdit.caoZuo'),
            component: 'operation',
            width: '80',
            fixed: 'right',
            options: {
              addDelete: [
                {
                  type: 'delete',
                  label: t('modelPersistentEdit.shanChu')
                }
              ]
            }
          }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.modelPersistent.info({ id: id.value })
      res.options = Object.keys(res.options).map((key) => {
        return {
          label: res.options[key],
          value: key
        }
      })
      dataForm.value = res
    }

    onMounted(() => {
      if (id.value) {
        fetchDetail()
      }
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { options, ...params } = JSON.parse(JSON.stringify(dataForm.value))

          params.options = options.reduce((pre, cur) => {
            pre[cur.value] = cur.label
            return pre
          }, {})

          await $api.modelPersistent[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('modelPersistentEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/model/persistent', 'modelPersistent')
    }

    // 设置数据标识&时间戳
    const setFieldDataFlag = ({ item, type }) => {
      if (type == 'data') {
        ElMessage({
          type: 'success',
          message: t('modelPersistentEdit.shuJuBiaoShi') + t('modelPersistentEdit.sheZhiChengGong')
        })
        dataForm.value.dataPersistentInfo.dataIdentifyFieldName = item.identifyName
      } else {
        ElMessage({
          type: 'success',
          message: t('modelPersistentEdit.shiJianChuo') + t('modelPersistentEdit.sheZhiChengGong')
        })
        dataForm.value.dataPersistentInfo.timestampFieldName = item.identifyName
      }
    }

    const identifyValueFieldNames = computed(() => {
      return (
        dataForm.value.dataPersistentInfo?.consumerBehaviorIdentifies?.map((item) => item.identifyValueFieldName) || []
      )
    })

    return {
      dataForm,
      dataFormRef,
      config,
      loading,
      isView,
      configTags,
      identifyValueFieldNames,
      setFieldDataFlag,
      save,
      close
    }
  }
}
