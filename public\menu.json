[{"name": "dashboard", "path": "/dashboard", "sort": 0, "component": "dashboard", "meta": {"icon": "el-icon-stopwatch", "type": "menu", "title": "menu.dashboard", "zh-cn": "工作台", "en-us": "Dashboard", "code": "cdp"}}, {"name": "dataInsight", "path": "/dataInsight", "sort": 3, "component": "dataInsight", "meta": {"icon": "el-icon-coin", "type": "menu", "title": "menu.dataInsight.name", "zh-cn": "数据洞察", "en-us": "Data Insights", "code": "cdp"}}, {"name": "user", "path": "/user", "sort": 3, "meta": {"icon": "el-icon-user", "type": "menu", "title": "menu.user.name", "zh-cn": "用户洞察", "en-us": "User Insights", "code": "cdp"}, "children": [{"name": "userPortrait", "path": "/user/portrait", "component": "user/portrait", "sort": 1, "meta": {"icon": "el-icon-postcard", "type": "menu", "title": "menu.user.portrait", "zh-cn": "用户画像", "en-us": "User Profile", "code": "cdp"}, "children": [{"name": "userPortraitEdit", "path": "/user/portraitEdit", "component": "user/portraitEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "hidden": true, "active": "/user/portrait", "title": "menu.user.portraitEdit", "zh-cn": "用户画像详情", "en-us": "Profile Details", "code": "cdp"}}]}, {"name": "userTag", "path": "/user/tag", "component": "user/tag", "sort": 1, "meta": {"icon": "el-icon-collection-tag", "type": "menu", "title": "menu.user.tag", "zh-cn": "标签管理", "en-us": "Tag Mgmt", "code": "cdp"}, "children": [{"name": "userTagEdit", "path": "/user/tagEdit", "component": "user/tagEdit", "sort": 1, "meta": {"icon": "el-icon-edit", "type": "menu", "hidden": true, "active": "/user/tag", "title": "menu.user.tagEdit", "zh-cn": "标签编辑", "en-us": "Edit Tag", "code": "cdp"}}, {"name": "userTagStatistics", "path": "/user/tagStatistics", "component": "user/tagStatistics", "sort": 1, "meta": {"icon": "el-icon-edit", "type": "menu", "hidden": true, "active": "/user/tag", "title": "menu.user.tagStatistics", "zh-cn": "标签统计", "en-us": "Tag Stats", "code": "cdp"}}, {"name": "userTagHistory", "path": "/user/tagHistory", "component": "user/tagHistory", "sort": 1, "meta": {"icon": "el-icon-edit", "type": "menu", "hidden": true, "active": "/user/tag", "title": "menu.user.tagHistory", "zh-cn": "标签快照", "en-us": "Tag Snapshot", "code": "cdp"}}]}, {"name": "userMarking", "path": "/user/marking", "component": "user/marking", "sort": 1, "meta": {"icon": "el-icon-pointer", "type": "menu", "title": "menu.user.marking", "zh-cn": "手动打标", "en-us": "Manual Tagging", "code": "cdp"}, "children": [{"name": "userMarkingEdit", "path": "/user/markingEdit", "component": "user/markingEdit", "sort": 1, "meta": {"icon": "el-icon-edit", "type": "menu", "hidden": true, "active": "/user/marking", "title": "menu.user.markingEdit", "zh-cn": "手动打标编辑", "en-us": "Edit Tagging", "code": "cdp"}}]}, {"name": "userCircle", "path": "/user/circle", "component": "user/circle", "sort": 1, "meta": {"icon": "el-icon-notification", "type": "menu", "title": "menu.user.circle", "zh-cn": "人群圈选", "en-us": "Crowd Select", "code": "cdp"}, "children": [{"name": "userCircleEdit", "path": "/user/circleEdit", "component": "user/circleEdit", "sort": 1, "meta": {"icon": "el-icon-edit", "type": "menu", "hidden": true, "active": "/user/circle", "title": "menu.user.circleEdit", "zh-cn": "人群圈选编辑", "en-us": "Edit Crowd", "code": "cdp"}}, {"name": "userCircleHistory", "path": "/user/circleHistory", "component": "user/circleHistory", "sort": 1, "meta": {"icon": "el-icon-edit", "type": "menu", "hidden": true, "active": "/user/circle", "title": "menu.user.circleHistory", "zh-cn": "人群快照", "en-us": "Crowd Snapshot", "code": "cdp"}}, {"name": "userCircleStatistics", "path": "/user/circleStatistics", "component": "user/circleStatistics", "sort": 1, "meta": {"icon": "el-icon-edit", "type": "menu", "hidden": true, "active": "/user/circle", "title": "menu.user.circleStatistics", "zh-cn": "人群统计", "en-us": "Crowd Stats", "code": "cdp"}}]}, {"name": "userOther", "path": "/user/other/:id", "component": "user/other", "sort": 1, "meta": {"icon": "el-icon-price-tag", "type": "menu", "hidden": true, "title": "menu.user.userOther", "zh-cn": "洞察", "en-us": "Insights", "code": "cdp"}}]}, {"name": "collect", "path": "/collect", "sort": 4, "meta": {"icon": "el-icon-cpu", "type": "menu", "title": "menu.collect.name", "zh-cn": "数据采集", "en-us": "Collection", "code": "cdp"}, "children": [{"name": "collectApi", "path": "/collect/api", "component": "collect/api", "sort": 0, "meta": {"icon": "el-icon-sort", "type": "menu", "title": "menu.collect.api", "zh-cn": "API", "en-us": "API", "code": "cdp"}, "children": [{"name": "collectApiEdit", "path": "/collect/apiEdit", "component": "collect/apiEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.collect.apiEdit", "active": "/collect/api", "hidden": true, "zh-cn": "API编辑", "en-us": "Edit API", "code": "cdp"}}]}, {"name": "collectDatabase", "path": "/collect/database", "component": "collect/database", "sort": 1, "meta": {"icon": "el-icon-coin", "type": "menu", "title": "menu.collect.database", "zh-cn": "数据库", "en-us": "DB", "code": "cdp"}, "children": [{"name": "collectDatabaseEdit", "path": "/collect/databaseEdit", "component": "collect/databaseEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.collect.databaseEdit", "active": "/collect/database", "hidden": true, "zh-cn": "数据库编辑", "en-us": "Edit DB", "code": "cdp"}}]}, {"name": "collectFile", "path": "/collect/file", "component": "collect/file", "sort": 2, "meta": {"icon": "el-icon-document", "type": "menu", "title": "menu.collect.file", "zh-cn": "文件", "en-us": "File", "code": "cdp"}, "children": [{"name": "collectFileEdit", "path": "/collect/fileEdit", "component": "collect/fileEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.collect.fileEdit", "active": "/collect/file", "hidden": true, "zh-cn": "文件编辑", "en-us": "Edit File", "code": "cdp"}}]}]}, {"name": "arrange", "path": "/arrange", "sort": 5, "meta": {"icon": "el-icon-set-up", "type": "menu", "title": "menu.arrange.name", "zh-cn": "数据编排", "en-us": "Orchestration", "code": "cdp"}, "children": [{"name": "arrangeRedirect", "path": "/arrange/redirect", "component": "arrange/redirect", "sort": 0, "meta": {"icon": "el-icon-operation", "type": "menu", "title": "menu.arrange.redirect", "zh-cn": "数据转储", "en-us": "Data Redirect", "code": "cdp"}, "children": [{"name": "arrangeRedirectEdit", "path": "/arrange/redirectEdit", "component": "arrange/redirectEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.arrange.redirectEdit", "active": "/arrange/redirect", "hidden": true, "zh-cn": "数据转储编辑", "en-us": "Edit Redirect", "code": "cdp"}}]}]}, {"name": "subscribe", "path": "/subscribe", "sort": 6, "meta": {"icon": "el-icon-collection", "type": "menu", "title": "menu.subscribe.name", "zh-cn": "数据订阅", "en-us": "Subscription", "code": "cdp"}, "children": [{"name": "subscribeApi", "path": "/subscribe/api", "component": "subscribe/api", "sort": 0, "meta": {"icon": "el-icon-sort", "type": "menu", "title": "menu.subscribe.api", "zh-cn": "API", "en-us": "API", "code": "cdp"}, "children": [{"name": "subscribeApiEdit", "path": "/subscribe/apiEdit", "component": "subscribe/apiEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.subscribe.apiEdit", "active": "/subscribe/api", "hidden": true, "zh-cn": "API编辑", "en-us": "Edit API", "code": "cdp"}}]}, {"name": "subscribeDatabase", "path": "/subscribe/database", "component": "subscribe/database", "sort": 1, "meta": {"icon": "el-icon-coin", "type": "menu", "title": "menu.subscribe.database", "zh-cn": "数据库", "en-us": "DB", "code": "cdp"}, "children": [{"name": "subscribeDatabaseEdit", "path": "/subscribe/databaseEdit", "component": "subscribe/databaseEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.subscribe.databaseEdit", "active": "/subscribe/database", "hidden": true, "zh-cn": "数据库编辑", "en-us": "Edit DB", "code": "cdp"}}]}]}, {"name": "model", "path": "/model", "sort": 7, "meta": {"icon": "el-icon-files", "type": "menu", "title": "menu.model.name", "zh-cn": "数据建模", "en-us": "Models", "code": "cdp"}, "children": [{"name": "modelPersistent", "path": "/model/persistent", "component": "model/persistent", "sort": 0, "meta": {"icon": "el-icon-coin", "type": "menu", "title": "menu.model.persistent", "zh-cn": "存储模型", "en-us": "Storage Model", "code": "cdp"}, "children": [{"name": "modelPersistentEdit", "path": "/model/persistentEdit", "component": "model/persistentEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.model.persistentEdit", "active": "/model/persistent", "hidden": true, "zh-cn": "存储模型编辑", "en-us": "Edit Storage", "code": "cdp"}}]}, {"name": "modelTransmission", "path": "/model/transmission", "component": "model/transmission", "sort": 1, "meta": {"icon": "el-icon-switch", "type": "menu", "title": "menu.model.transmission", "zh-cn": "传输模型", "en-us": "Transfer Model", "code": "cdp"}, "children": [{"name": "modelTransmissionEdit", "path": "/model/transmissionEdit", "component": "model/transmissionEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.model.transmissionEdit", "active": "/model/transmission", "hidden": true, "zh-cn": "传输模型编辑", "en-us": "Edit Transfer", "code": "cdp"}}]}, {"name": "modelExport", "path": "/model/export", "component": "model/export", "sort": 2, "meta": {"icon": "el-icon-download", "type": "menu", "title": "menu.model.export", "zh-cn": "导出模型", "en-us": "Export Model", "code": "cdp"}, "children": [{"name": "modelExportEdit", "path": "/model/exportEdit", "component": "model/exportEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.model.exportEdit", "active": "/model/export", "hidden": true, "zh-cn": "导出模型编辑", "en-us": "Edit Export", "code": "cdp"}}]}, {"name": "modelPortrait", "path": "/model/portrait", "component": "model/portrait", "sort": 3, "meta": {"icon": "el-icon-picture", "type": "menu", "title": "menu.model.portrait", "zh-cn": "画像模型", "en-us": "Profile Model", "code": "cdp"}, "children": [{"name": "modelPortraitEdit", "path": "/model/portraitEdit", "component": "model/portraitEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.model.portraitEdit", "active": "/model/portrait", "hidden": true, "zh-cn": "画像模型编辑", "en-us": "Edit Profile", "code": "cdp"}}]}]}, {"name": "task", "path": "/task", "sort": 8, "meta": {"icon": "el-icon-tickets", "type": "menu", "title": "menu.task.name", "zh-cn": "任务中心", "en-us": "Tasks", "code": "cdp"}, "children": [{"name": "taskDeploy", "path": "/task/deploy", "component": "task/deploy", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.task.deploy", "zh-cn": "任务配置", "en-us": "Config", "code": "cdp"}, "children": [{"name": "taskDeployEdit", "path": "/task/deployEdit", "component": "task/deployEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.task.deployEdit", "active": "/task/deploy", "hidden": true, "zh-cn": "任务配置编辑", "en-us": "Edit Config", "code": "cdp"}}]}, {"name": "taskRecord", "path": "/task/record", "component": "task/record", "sort": 1, "meta": {"icon": "el-icon-document-checked", "type": "menu", "title": "menu.task.record", "zh-cn": "执行记录", "en-us": "Records", "code": "cdp"}, "children": [{"name": "taskRecordEdit", "path": "/task/recordEdit", "component": "task/recordEdit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.task.recordEdit", "active": "/task/record", "hidden": true, "zh-cn": "执行记录编辑", "en-us": "Edit Record", "code": "cdp"}}]}]}, {"name": "searchList", "path": "/search/list", "sort": 9, "meta": {"icon": "el-icon-search", "type": "menu", "title": "menu.search.name", "zh-cn": "数据查询", "en-us": "Query", "code": "cdp"}, "component": "search/list", "children": [{"name": "searchEdit", "path": "/search/edit", "component": "search/edit", "sort": 0, "meta": {"icon": "el-icon-edit", "type": "menu", "title": "menu.search.edit", "active": "/search/list", "hidden": true, "zh-cn": "数据查询编辑", "en-us": "Edit Query", "code": "cdp"}}]}, {"name": "tags", "path": "/tags", "sort": 10, "component": "tags", "meta": {"icon": "el-icon-discount", "type": "menu", "title": "menu.tags", "zh-cn": "标签分类", "en-us": "Tag Categories", "code": "cdp"}}]