<template>
  <el-container>
    <el-main>
      <div class="top-body">
        <el-page-header @back="goBack" icon="el-icon-arrow-left">
          <template #content>
            <span class="text-large"> {{ tagName }} </span>
          </template>
        </el-page-header>
      </div>
      <div class="table-block">
        <lw-table
          :hideTool="false"
          :loading="loading"
          :table-data="tableData"
          :tableColumns="tableHeaders"
          height="calc(100vh - 230px)"
          :search-params="searchParams"
          :isShowPagination="true"
          :total-count="totalCount"
          @getTableData="getTableData" />
      </div>
    </el-main>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
