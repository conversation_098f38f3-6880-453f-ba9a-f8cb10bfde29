<template>
  <el-container>
    <el-main>
      <lwFormMini ref="dataFormRef" :config="config" :isView="isView" v-model="dataForm" :loading="loading">
        <template #h5Url>
          <div class="h5-url">
            <span>{{ locationOrigin }}{{ copyUrlText }}</span>
            <el-button type="primary" v-copy="locationOrigin + copyUrlText" size="small">{{$t('modelPortraitEdit.fuZhi')}}</el-button>
          </div>
        </template>
        <template #lwFieldsEdit>
          <lwFieldsEdit v-model="dataForm.consumerSetting.fields" :consumerList="consumerList" />
        </template>
        <template v-for="item in behaviorList" :key="item.id" v-slot:[item.id]>
          <lwBehaviorEdit
            v-if="dataForm.behaviors.includes(item.id)"
            v-model="dataForm.behaviorSetting"
            :behaviorId="item.id"
            :behaviorList="behaviorList" />
        </template>
      </lwFormMini>
    </el-main>
    <el-footer class="footer-body">
      <el-button @click="close">{{ $t('btn.close') }}</el-button>
      <el-button v-if="!isView" type="primary" @click="save">{{ $t('btn.save') }}</el-button>
    </el-footer>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
