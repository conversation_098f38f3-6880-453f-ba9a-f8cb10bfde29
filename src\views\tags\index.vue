<template>
  <el-container>
    <el-main>
      <lw-search
        :options="searchOptions"
        v-model="searchParams"
        :columnNumber="layout.grid"
        :expandNumber="4"
        :labelWidth="layout.labelWidth + 'px'"
        :hideLabel="layout.hideLabel"
        :labelAlign="layout.labelAlign"
        @search="search"
        @reset="reset" />
      <div class="table-block">
        <div class="btn-container">
          <el-button
            v-auth="['cdp.tags.create']"
            type="primary"
            icon="el-icon-plus"
            @click="dialogVisible = true"></el-button>
        </div>
        <lw-table
          :hideTool="false"
          :loading="loading"
          :table-data="tableData"
          :tableColumns="tableHeaders"
          :search-params="searchParams"
          height="calc(100vh - 265px)"
          maxHeight="calc(100vh - 265px)"
          :isShowPagination="false"
          :total-count="totalCount"
          @getTableData="getTableData" />
      </div>
    </el-main>

    <!-- 新增修改 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dataForm.id ? $t('tags.xiuGai') : $t('tags.xinZeng')"
      width="600"
      :destroy-on-close="true"
      :before-close="handleClose">
      <lwFormMini v-model="dataForm" ref="dataFormRef" :config="formConfig">
        <template #sceneListSelect>
          <div class="input-item">
            <el-select v-model="dataForm.sceneId" clearable :placeholder="$t('tags.qingXuanZe')">
              <el-option
                v-for="option in sceneList"
                :key="option.id"
                :label="option.name"
                :value="option.id"></el-option>
            </el-select>
            <el-popover v-if="mobScene" placement="right-start" :width="300" trigger="click">
              <template #reference>
                <el-button type="primary" text bg>{{ $t('tags.chaKanHuanYingYu') }}</el-button>
              </template>
              <div class="mobile-body">
                <div class="mobile-html" v-html="mobScene"></div>
              </div>
            </el-popover>
          </div>
        </template>
        <span></span>
      </lwFormMini>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">{{ $t('tags.guanBi') }}</el-button>
          <el-button type="primary" @click="save"> {{ $t('tags.baoCun') }} </el-button>
        </div>
      </template>
    </el-dialog>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
