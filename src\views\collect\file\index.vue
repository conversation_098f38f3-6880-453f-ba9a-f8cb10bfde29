<template>
  <el-container>
    <el-main>
      <lw-search
        :options="searchOptions"
        v-model="searchParams"
        :columnNumber="layout.grid"
        :expandNumber="4"
        :labelWidth="layout.labelWidth + 'px'"
        :hideLabel="layout.hideLabel"
        :labelAlign="layout.labelAlign"
        @search="search"
        @reset="reset" />
      <div class="table-block">
        <div class="btn-container">
          <el-button type="primary" icon="el-icon-plus" @click="edit"></el-button>
        </div>
        <lw-table
          :hideTool="false"
          :loading="loading"
          :table-data="tableData"
          :tableColumns="tableHeaders"
          :search-params="searchParams"
          :isShowPagination="true"
          :total-count="totalCount"
          @getTableData="getTableData" />
      </div>
    </el-main>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
