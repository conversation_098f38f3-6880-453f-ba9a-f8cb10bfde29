import config from '@/config'
import i18n from '@/locales'
import tool from '@/utils/tool'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { createRouter, createWebHashHistory } from 'vue-router'
import routesList from './route'
import { afterEach, beforeEach } from './scrollBehavior'
import systemRouter from './systemRouter'

// 系统路由
const routes = systemRouter
let userRoutes = routesList

// 静态加载所有的 Vue 组件（除了login组件），构建时合并为一个文件
const modules = import.meta.glob(
  ['../views/**/index.vue', '!../views/login/index.vue', '!../views/initialization/index.vue'],
  {
    eager: true
  }
)

// 系统特殊路由
const routes_404 = {
  path: '/:pathMatch(.*)*',
  hidden: true,
  component: () => import('@/layout/other/404.vue')
}
let routes_404_r = () => {}

const router = createRouter({
  history: createWebHashHistory(),
  routes,
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 })
})

// 设置标题
document.title = config.APP_NAME
const key = config.APP_NAME.toLowerCase()

let isGetRouter = false
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  document.title = to.meta.title ? `${to.meta.title} - ${config.APP_NAME}` : config.APP_NAME

  const isLoginPage = to.path === '/passport-login'
  const userAuthInfo = tool.data.get('userAuthInfo')

  if (isLoginPage) {
    router.addRoute(routes[0])
    routes_404_r()
    isGetRouter = false
    next()
    return
  }

  if (!userAuthInfo && to.path !== '/passport-login') {
    next({ path: '/passport-login' })
    return
  }

  if (to.meta.fullpage) {
    to.matched = [to.matched[to.matched.length - 1]]
  }

  if (!isGetRouter) {
    getAsyncRouter(to)
  }

  beforeEach(to, from)
  next()
})

router.afterEach((to, from) => {
  afterEach(to, from)
  NProgress.done()
})

// 处理路由
function getAsyncRouter(to) {
  let menu = getMenu()
  let menuRouter = flatAsyncRoutes(filterAsyncRouter(menu))
  let initPath = ''
  menuRouter.forEach((item, index) => {
    if (window.self !== window.top) {
      router.addRoute(item)
    } else {
      router.addRoute('layout', item)
    }
    if (index === 0) {
      initPath = item.path
      config.DASHBOARD_URL = item.path
    }
  })

  routes_404_r = router.addRoute(routes_404)
  const isRoute = router.getRoutes().find((route) => route.path === to.path)
  if (!to.matched.length) {
    if (isRoute) {
      router.push(to.fullPath)
    } else {
      tool.data.remove('RouteTags')
      router.push(initPath)
    }
  }
}

// 转换
function filterAsyncRouter(routerMap) {
  return routerMap.map((item) => {
    let { meta = {}, path, name, redirect, children } = item
    if (meta.type === 'iframe') {
      meta.url = path
      path = `/i/${name}`
    }
    return {
      path,
      name,
      meta,
      redirect,
      children: children ? filterAsyncRouter(children) : null,
      component: loadComponent(item.component)
    }
  })
}

function loadComponent(component) {
  if (!component) {
    return () => import('@/layout/other/empty.vue')
  }

  // 转换路径格式，移除前导斜杠
  const normalizedComponent = component.startsWith('/') ? component.slice(1) : component
  // return modules[`../views/${normalizedComponent}/index.vue`].default
   return modules[`../views/${normalizedComponent}/index.vue`]?.default

}

// 路由扁平化
function flatAsyncRoutes(routes, breadcrumb = []) {
  return routes.reduce((acc, route) => {
    const { children, path, meta } = route
    const tmpBreadcrumb = [...breadcrumb, { path, meta }]

    if (children) {
      const childrenRoutes = flatAsyncRoutes(children, tmpBreadcrumb)
      acc.push({ ...route, meta: { ...meta, breadcrumb: tmpBreadcrumb }, children: undefined }, ...childrenRoutes)
    } else {
      acc.push({ ...route, meta: { ...meta, breadcrumb: tmpBreadcrumb } })
    }
    return acc
  }, [])
}

// 过滤树
function treeFilter(tree, func) {
  return tree
    .map((node) => {
      let title = node?.meta?.[i18n.global.locale] || (node?.meta?.title ? i18n.global.t(node?.meta?.title) : '按钮')
      if (node.meta?.code && node.meta?.code != key) {
        let lang = tool.data.get('APP_LANG') || config.LANG
        title = node.meta[lang]
        node.meta.type = 'iframe'
        delete node.component
        node.path = `${window.location.origin}/${node.meta.code}/#${node.path}`
        return {
          ...node,
          meta: { ...node.meta, title: title },
          children: node.children && treeFilter(node.children, func)
        }
      }

      return {
        ...node,
        meta: { ...node.meta, title: title },
        children: node.children && treeFilter(node.children, func)
      }
    })
    .filter((node) => func(node))
}

// 获取菜单
function getMenu() {
  const { menus, combined, menuContent } = tool.data.get('userAuthInfo')
  let MENU_LIST = tool.data.get(`MENU_LIST`) || {}
  if (combined && menuContent && window.self == window.top) {
    isGetRouter = false
    let menuList = JSON.parse(menuContent)

    if (menuList[key]) {
      userRoutes = [...menuList[key]]
    }
    MENU_LIST[`${config.APP_NAME}_online`] = userRoutes
  } else if (config.MENU_DYNAMIC && MENU_LIST?.menuDynamic && MENU_LIST?.DYNAMIC_MENU?.[key]) {
    userRoutes = MENU_LIST?.DYNAMIC_MENU?.[config.APP_NAME.toLowerCase()]
    MENU_LIST[`${config.APP_NAME}_local`] = userRoutes
  } else {
    MENU_LIST[`${config.APP_NAME}_local`] = userRoutes
  }

  isGetRouter = true
  tool.data.set(`MENU_LIST`, MENU_LIST)
  
  let userMenu = treeFilter(
    userRoutes,
    (node) => (!node.meta.roles || node.meta.roles.some((role) => menus.includes(role))) && node.meta?.type === 'menu'
  )

  return userMenu
}

// 入侵追加自定义方法、对象
router.sc_getMenu = getMenu

export default router
