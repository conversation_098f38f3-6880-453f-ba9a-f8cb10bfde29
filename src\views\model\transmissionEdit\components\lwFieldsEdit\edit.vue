<template>
  <div class="field-edit">
    <lwFormMini
      v-if="dataForm?.id"
      ref="dataFormRef"
      :config="config"
      :isView="isView"
      v-model="dataForm"
      :loading="loading">
    </lwFormMini>

    <el-empty v-else :description="$t('modelTransmissionEdit.QXZYBJDZD')" />
  </div>
</template>

<script>
import { ref, computed, onMounted, getCurrentInstance, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { ElMessageBox } from 'element-plus'

export default {
  props: {
    modelValue: {
      type: Object,
      default: () => {}
    }
  },
  setup(props, { emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, t }
    } = getCurrentInstance()

    // 表单数据
    const dataForm = ref({})
    const dataFormRef = ref(null)
    const isView = computed(() => !!route.query.isView)
    const loading = ref(false)

    // 表单配置
    const config = computed(() => {
      return {
        labelWidth: '60px',
        labelPosition: 'top',
        size: 'small',
        formItems: [
          {
            label: t('modelTransmissionEdit.mingCheng'),
            name: 'name',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelTransmissionEdit.ziDuanMingCheng')
            },
            rules: [{ required: true, message: t('modelTransmissionEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelTransmissionEdit.zhongWenMing'),
            name: 'aliasName',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelTransmissionEdit.zhongWenMing')
            },
            rules: [{ required: true, message: t('modelTransmissionEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelTransmissionEdit.leiXing'),
            name: 'type',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('modelTransmissionEdit.qingXuanZe'),
              items: options.value
            },
            rules: [{ required: true, message: t('modelTransmissionEdit.qingXuanZe'), trigger: 'change' }]
          },
          {
            label: t('modelTransmissionEdit.zhuJian'),
            name: 'primaryKey',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('modelTransmissionEdit.qingXuanZeZhuJian'),
              items: primaryList.value
            },
            hideHandle: '$.type != "NESTED"'
          },
          {
            label: t('modelTransmissionEdit.ziDuanJiaoYanFangShi'),
            name: 'dataFieldValidType',
            value: 'NONE',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('modelTransmissionEdit.ziDuanJiaoYanFangShi'),
              items: [
                { label: t('modelTransmissionEdit.wuXuJiaoYan'), value: 'NONE' },
                { label: t('modelTransmissionEdit.shouJiJiaoYan'), value: 'PHONE' },
                { label: t('modelTransmissionEdit.zhengZeJiaoYan'), value: 'REGEX' }
              ]
            }
          },
          {
            label: t('modelTransmissionEdit.zhengZeBiaoDaShi'),
            name: 'regExp',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelTransmissionEdit.zhengZeBiaoDaShi')
            },
            hideHandle: '$.dataFieldValidType != "REGEX"'
          },
          {
            label: t('modelTransmissionEdit.yunXuWeiKong'),
            name: 'allowNull',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('modelTransmissionEdit.shiFouShuZu'),
            name: 'arrayType',
            value: false,
            span: 6,
            component: 'switch'
          }
        ]
      }
    })

    const primaryList = ref([])
    const init = () => {
      dataForm.value = props.modelValue
      primaryList.value = props.modelValue?.fields.map((item) => ({ label: item.aliasName, value: item.name })) || []
    }

    // 获取类型
    const options = ref([])
    const getTypes = async () => {
      const res = await $api.modelPersistent.fieldTypes()
      options.value = res.map((item) => ({ label: item, value: item }))
    }

    watch(
      () => dataForm.value,
      (val) => {
        emit('update:modelValue', val)
      },
      { deep: true }
    )

    onMounted(() => {
      getTypes()
    })

    return {
      dataForm,
      dataFormRef,
      isView,
      loading,
      config,
      options,
      init
    }
  }
}
</script>
<style lang="scss" scoped>
.field-edit {
  :deep(.lw-form-mini-card) {
    border: 0 !important;
  }
  .dict-list {
    display: flex;
    align-items: center;
    width: 100%;
    .dict-select {
      width: 100%;
    }
  }
  .el-button-group {
    display: flex;
    align-items: center;
  }
}
</style>
