export default {
  collectApiEdit: {
    jiBenXinXi: 'Basic Information',
    jieKouMingCheng: 'API Name',
    qingShuRu: 'Please enter',
    jie<PERSON>ouBianMa: 'API Code',
    ZNWXXZMSZHXH: 'Only lowercase letters, numbers and underscores allowed',
    CDBNCGGZF: 'Maximum 20 characters',
    jieKouShiQu: 'API Timezone',
    qingXuanZe: 'Please select',
    chuanShuMoXing: 'Transmission Model',
    KXZCSMXZYJLD: 'Select from existing data tables in transmission models',
    shiFouJiLuRiZhi: 'Enable Logging',
    jiLu: 'Enable',
    buJiLu: 'Disable',
    SFXYJLRZKQHJ: 'When enabled, system will record logs',
    shuJuShiF<PERSON><PERSON>un<PERSON>hu: 'Data Storage',
    cunChu: 'Store',
    bu<PERSON><PERSON><PERSON>hu: "Don't Store",
    KQHXXZCCMXHZ: 'When enabled, must select storage model for aggregated data tables',
    cunChuMoXing: 'Storage Model',
    KXZCCMXZYJLD: 'Select from existing data tables in storage models',
    beiZhu: 'Remarks',
    ying<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Field Mapping',
    bao<PERSON>unChengGong: 'Saved successfully'
  }
}
