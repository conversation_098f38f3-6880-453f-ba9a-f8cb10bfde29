import config from '@/config'
import tool from '@/utils/tool'

// 使用 import.meta.glob 进行静态导入
const modules = import.meta.glob('./modules/*.js', { eager: true })

// 从缓存或配置中获取已有的路由
let MENU_LIST = tool.data.get(`MENU_LIST`) || {}
const routes = MENU_LIST[`${config.APP_NAME}_local`] || []

// 修改后的 upsertRoute 函数，如果存在则替换原有数据
const upsertRoute = (route) => {
  const index = routes.findIndex(
    (existingRoute) => existingRoute.name === route.name || existingRoute.path === route.path
  )
  if (index !== -1) {
    routes[index] = route // 覆盖原有数据
  } else {
    routes.push(route) // 添加新的数据
  }
}

// 遍历导入的模块并合并到路由中
for (const path in modules) {
  const module = modules[path]
  if (Array.isArray(module.default)) {
    module.default.forEach((route) => {
      upsertRoute(route)
    })
  } else if (typeof module.default === 'object') {
    upsertRoute(module.default)
  }
}

// 按 sort 属性进行排序
routes.sort((a, b) => {
  const sortA = a.sort !== undefined ? a.sort : 1000
  const sortB = b.sort !== undefined ? b.sort : 1000
  return sortA - sortB
})
// 导出最终的路由数组
export default routes
