<template>
  <lwLayout :isInitialized="false" :tagShowID="true" appName="WAHLAP CDP" appSimpleName="CDP">
    <template #logo>
      <img src="@/assets/images/logo-hl.png" alt="华立科技" style="width: 100%" />
    </template>

    <!-- 路由视图 -->
    <template #routerView>
      <router-view v-slot="{ Component }">
        <keep-alive :include="store.state.keepAlive.keepLiveRoute">
          <component :is="Component" :key="route.fullPath" v-if="!store.state.keepAlive.routeShow" />
        </keep-alive>
      </router-view>
    </template>

    <!-- 用户状态切换 -->
    <template #userbarActionBox>
      <div class="store-block">
        <el-dropdown @command="changeStatus" class="panel-item">
          <div class="bu-list">
            <span class="status" :class="{ online: $store.state.user.status }" />
            {{ $store.state.user.status ? $t('layout.online') : $t('layout.configuring') }}
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="online">
                {{ $t('layout.online') }}
              </el-dropdown-item>
              <el-dropdown-item command="configuring">
                {{ $t('layout.configuring') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>

    <!-- 数据库切换 -->
    <template #userbarActionData>
      <div class="store-block">
        <el-dropdown @command="changeDb" class="panel-item">
          <div class="bu-list">
            <el-icon><el-icon-coin /></el-icon>
            {{ dbTitle }}
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in dbList" :key="item.id" :command="item.id">
                {{ item.aliasName }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </lwLayout>
</template>

<script setup>
import { getCurrentInstance, onBeforeMount, ref, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'

const store = useStore()
const route = useRoute()
const router = useRouter()
const {
  proxy: { $api, $bus, $tool }
} = getCurrentInstance()

// 用户在线状态管理
const changeStatus = async (type) => {
  await $api.auth.editStatus(type)
  store.commit('setStatus', type === 'online')
}

const getStatus = async () => {
  const res = await $api.auth.getStatus()
  store.commit('setStatus', res === 'online')
}

// 数据库管理相关
const dbList = ref([])
const dbTitle = ref('')
let BAR_DATA = $tool.data.get('BAR_DATA') || {}

const setDbName = () => {
  BAR_DATA = {
    ...BAR_DATA,
    ...$tool.data.get('BAR_DATA')
  }
  const selectedDb = dbList.value.find((item) => item.id === BAR_DATA.collectionId)
  const defaultDb = dbList.value[0]

  if (selectedDb) {
    dbTitle.value = selectedDb.aliasName
  } else {
    dbTitle.value = defaultDb?.aliasName || '暂无'
    BAR_DATA.collectionId = defaultDb?.id || ''
    BAR_DATA.collectionName = defaultDb?.name || ''
  }

  $tool.data.set('BAR_DATA', BAR_DATA)
  changeDb(BAR_DATA.collectionId)
}

const refreshView = () => {
  store.commit('setRouteShow', true)
  nextTick(() => {
    store.commit('clearKeepLive')
    store.commit('setRouteShow', false)
  })
}

const changeDb = async (id) => {
  const selectedDb = dbList.value.find((item) => item.id === id)
  if (selectedDb) {
    BAR_DATA = {
      ...BAR_DATA,
      ...$tool.data.get('BAR_DATA')
    }
    dbTitle.value = selectedDb.aliasName
    BAR_DATA.collectionId = selectedDb.id
    BAR_DATA.collectionName = selectedDb.name
    BAR_DATA.collectionList = dbList.value.map((item) => ({ label: item.aliasName, value: item.id }))
    $tool.data.set('BAR_DATA', BAR_DATA)
    refreshView()
  }
}

// 监听业务单元变更
$bus.$on('changeBu', async ({ buCode }) => {
  const res = await $api.auth.getDb({
    sort: 'lastModifiedDate DESC',
    expression: `buCode eq ${buCode}`
  })
  dbList.value = res
  setDbName()

  getRoleData()
  getInsightList()
})

// 获取数据角色数据
const getRoleData = async () => {
  BAR_DATA = {
    ...BAR_DATA,
    ...$tool.data.get('BAR_DATA')
  }
  let res = await $api.auth.getRoleData()

  BAR_DATA.dataRoles = res.map((item) => ({ label: item.name, value: item.id })) || []
  BAR_DATA.dataRoleIds = ['0', ...res.map((item) => item.id)]

  $tool.data.set('BAR_DATA', BAR_DATA)
}

// 获取洞察列表
const getInsightList = async () => {
  BAR_DATA = {
    ...BAR_DATA,
    ...$tool.data.get('BAR_DATA')
  }
  let res = await $api.auth.getInsightList()
  BAR_DATA.insightList =
    res?.map((item) => ({
      label: item.name,
      value: item.id,
      code: item.code,
      fields: item.consumerSetting?.fields || []
    })) || []
  $tool.data.set('BAR_DATA', BAR_DATA)

  // 新增菜单
  let menu = router.sc_getMenu()
  menu.forEach((item) => {
    if (item.name == 'user') {
      res?.forEach((x) => {
        item.children.push({
          name: 'userOther',
          path: `/user/other/${x.code}`,
          component: 'user/other',
          sort: 1,
          meta: {
            icon: 'el-icon-price-tag',
            type: 'menu',
            title: x.name
          }
        })
      })
    }
  })
  $bus.$emit('setMenu', menu)
}

onBeforeMount(() => {
  getRoleData()
  getInsightList()
  getStatus()
})
</script>

<style lang="scss" scoped>
.store-block {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  :deep(.el-dropdown) {
    height: 100%;
  }

  .panel-item {
    height: 100%;
    padding: 0 10px;
    display: flex;
    align-items: center;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background: rgba(0, 0, 0, 0.1);
    }

    .bu-list {
      height: 100%;
      display: flex;
      align-items: center;
      gap: 5px;
      white-space: nowrap;
    }

    .status {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #ccc;
      box-shadow: 0 0 5px #ccc;

      &.online {
        background: #fc3434;
        box-shadow: 0 0 5px #fc3434;
      }
    }
  }
}
</style>
