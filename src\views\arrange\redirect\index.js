import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'arrangeRedirect',
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()
    const BAR_DATA = $tool.data.get('BAR_DATA')
    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        sourceCollectionId_eq: BAR_DATA.collectionId,
        destCollectionId_eq: BAR_DATA.collectionId
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('arrangeRedirect.mingCheng'),
          prop: 'name_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('arrangeRedirect.mingCheng'), dataIndex: 'name', minWidth: '160' },
        { title: t('arrangeRedirect.yuanShuJuCangKu'), dataIndex: 'sourceCollectionName', width: '160' },
        { title: t('arrangeRedirect.yuanShuJuMoXing'), dataIndex: 'sourcePersistentModelName', minWidth: '160' },
        { title: t('arrangeRedirect.muBiaoShuJuMoXing'), dataIndex: 'destPersistentModelName', minWidth: '160' },
        { title: t('arrangeRedirect.miaoShu'), dataIndex: 'description', minWidth: '160' }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '180',
        fixed: 'right',
        ellipsis: true,
        operation: [
          { clickFun: (item) => edit(item, 'view'), label: t('btn.view'), auth: ['cdp.arrange_redirect.locate'] },
          { clickFun: (item) => edit(item, 'edit'), label: t('btn.edit'), auth: ['cdp.arrange_redirect_edit'] },
          { clickFun: del, label: t('btn.delete'), auth: ['cdp.arrange_redirect.delete'] }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        sort: 'createTime,DESC',
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.arrangeRedirect
        .page(params)
        .then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = {}
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/arrange/redirectEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.arrangeRedirect.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      edit
    }
  }
}
