import { computed, getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'userCircleHistory',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()
    const tagName = computed(() => route.query.tagName || t('userCircleHistory.kuaiZhaoLiShi'))

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        audienceId_eq: route.query.id || ''
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 确认导出
    const confirmExport = async (item) => {
      let tenantId = $tool.data.get('tenantId')
      let params = {
        tenantId,
        fields: 'consumerCdpId',
        expression: $expression({
          audienceSnapshotId_eq: item.id
        })
      }
      let res = await $api.userCircle.exportSnapshot(params)
      if (res) {
        const link = document.createElement('a')
        link.href = res
        link.download = `${tagName.value}.csv`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        exportDialogVisible.value = false
      } else {
        ElMessage({ type: 'error', message: t('userCircleHistory.DCSBQSHZS') })
      }
    }

    onBeforeMount(() => {
      state.tableHeaders = [
        { title: t('userCircleHistory.chuangJianShiJian'), dataIndex: 'createDate', minwidth: '180', date: true },
        { title: t('userCircleHistory.gengXinShiJian'), dataIndex: 'cdp_update_time', minwidth: '180', date: true },
        { title: t('userCircleHistory.shuLiang'), dataIndex: 'num', minwidth: '180' },
        {
          title: t('userCircleHistory.zhuangTai'),
          dataIndex: 'status',
          minwidth: '180',
          options: [
            {
              label: t('userCircleHistory.chuangJian'),
              value: 'created'
            },
            {
              label: t('userCircleHistory.zhiXing'),
              value: 'running'
            },
            {
              label: t('userCircleHistory.wanCheng'),
              value: 'finish'
            },
            {
              label: t('userCircleHistory.quXiao'),
              value: 'cancel'
            },
            {
              label: t('userCircleHistory.yiChang'),
              value: 'error'
            }
          ]
        }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '80',
        fixed: 'right',
        ellipsis: true,
        operation: [{ clickFun: confirmExport, label: t('userCircleHistory.daoChu') }]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.userCircle
        .snapshotStatistics(params)
        .then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    // 返回
    const goBack = () => {
      store.state.viewTags.closeTagAndJump('/user/circle')
    }

    return {
      ...toRefs(state),
      goBack,
      getTableData,
      tagName
    }
  }
}
