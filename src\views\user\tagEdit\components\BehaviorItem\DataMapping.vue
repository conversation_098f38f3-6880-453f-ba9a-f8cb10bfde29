<template>
  <div class="sort-dataForm-select">
    <el-cascader
      v-model="cascaderValue"
      :options="modelList"
      :disabled="modelList.length == 0"
      :props="{
        value: 'name',
        label: 'aliasName',
        children: 'fields'
      }"
      :placeholder="$t('userTagEdit.yuanZiDuan')"
      filterable
      class="field-select" />
    <div class="mapping-type">
      <span class="left-right"></span>
    </div>
    <el-cascader
      v-model="consumerValue"
      :options="consumerList"
      :disabled="modelList.length == 0"
      :props="{
        value: 'name',
        label: 'aliasName',
        children: 'fields'
      }"
      :placeholder="$t('userTagEdit.muBiaoZiDuan')"
      filterable
      class="field-select" />
    <!-- 按钮组 -->
    <div class="btn-group">
      <el-button-group>
        <el-tooltip effect="dark" :content="$t('userTagEdit.shanChu')" placement="top">
          <el-button type="danger" text @click="removeCondition" icon="el-icon-delete"> </el-button>
        </el-tooltip>
      </el-button-group>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, inject, watch } from 'vue'

const {
  proxy: { $api, $tool, $bus, t }
} = getCurrentInstance()

// 定义响应式数据模型
const dataForm = defineModel({
  type: Object,
  default: { conditions: [] }
})
const props = defineProps({
  dataPersistentModelId: {
    type: String,
    default: ''
  }
})

const consumerValue = computed({
  get() {
    if (!dataForm.value.destFieldName) return []
    return dataForm.value.destFieldName.split('.')
  },
  set(val) {
    dataForm.value.destFieldName = val.join('.')
  }
})

const cascaderValue = computed({
  get() {
    if (!dataForm.value.sourceFieldName) return []
    // 将 "parent.child" 格式转换为数组
    return dataForm.value.sourceFieldName.split('.')
  },
  set(val) {
    // 将数组转换为 "parent.child" 格式
    dataForm.value.sourceFieldName = val.join('.')
  }
})

const options = inject('optionsPersistent')
// 客户字段数据
const consumerList = computed(() => {
  return options.value?.consumerMap?.fields || []
})

// 行为字段数据
const behaviorList = computed(() => {
  if (!options.value?.behaviorList) return []
  return options.value.behaviorList
})

const modelList = computed(() => {
  let item = behaviorList.value.find((x) => x.id == props.dataPersistentModelId)
  return item?.fields || []
})

// 删除条件
const emits = defineEmits(['remove'])
const removeCondition = () => {
  emits('remove')
}
</script>
<style lang="scss" scoped>
.sort-dataForm-select {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
  .btn-group {
    display: flex;
    align-items: center;
    min-width: 175px;
  }
  .mapping-type {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100px;
    .left-right {
      width: 100%;
      height: 2px;
      background: var(--el-color-primary);
      position: relative;
      &:before {
        content: '';
        position: absolute;
        bottom: 0;
        right: -2px;
        border-top: 8px solid transparent;
        border-left: 12px solid var(--el-color-primary);
        width: 0;
        height: 0;
      }
    }
  }
}
</style>
