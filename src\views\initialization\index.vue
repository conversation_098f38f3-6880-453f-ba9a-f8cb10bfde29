<template>
  <lwLayoutInit @save="save">
    <lwFormMini v-model="dataForm"
                ref="dataFormRef"
                :config="formConfig">
    </lwFormMini>
  </lwLayoutInit>
</template>
<script>
import {getCurrentInstance, ref, toRefs, computed} from 'vue'
import dayjs from 'dayjs'
import {ElMessage} from 'element-plus'
import {useRouter} from 'vue-router'

export default {
  setup(props) {
    const {
      proxy: {$api, $expression, t, $tool, $bus}
    } = getCurrentInstance()
    const router = useRouter()

    const dataForm = ref({
      zonedDevices: {},
      infraSettings: {
        storageSetting: {
          type: '',
          nfsDriveSetting: {},
          ossBucketSetting: {},
          stsSetting: {}
        }
      }
    })
    const dataFormRef = ref(null)
    const formConfig = computed(() => {
      return (
        props?.config || {
          labelWidth: '110px',
          labelPosition: 'top',
          formItems: [
            {label: t('initialization.jiChuSheZhi'), component: 'divider'},
            {
              label: t('initialization.mingCheng'),
              name: 'name',
              value: '',
              component: 'input',
              span: 24,
              options: {
                maxlength: '20',
                placeholder: t('initialization.qingShuRu')
              },
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {
              label: t('initialization.miaoShu'),
              name: 'description',
              value: '',
              component: 'input',
              span: 24,
              options: {
                type: 'textarea',
                placeholder: t('initialization.qingShuRu')
              }
            },
            {label: t('initialization.cunChuShangChuanSheZhi'), component: 'divider'},
            {
              label: t('initialization.cunChuLeiXing'),
              name: 'infraSettings.storageSetting.type',
              value: 'nfs',
              component: 'select',
              span: 12,
              options: {
                items: [
                  {value: 'nfs', label: t('initialization.benDiYingPan')},
                  {value: 'aws', label: t('initialization.yaMaXunYun')},
                  {value: 'oss', label: t('initialization.aLiYun')},
                  {value: 'minio', label: 'MinIO'}
                ],
                placeholder: t('initialization.qingXuanZe')
              },
              rules: [{required: true, message: t('initialization.qingXuanZe'), trigger: 'blur'}]
            },
            {label: t('initialization.yingPanSheZhi'), component: 'divider', hideHandle: '$.infraSettings.storageSetting.type !== "nfs"'},
            {
              label: t('initialization.cunChuGenMuLu'),
              name: 'infraSettings.storageSetting.nfsDriveSetting.localPrefix',
              value: '',
              component: 'input',
              span: 12,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type !== "nfs"',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {
              label: t('initialization.daiLiMuLu'),
              name: 'infraSettings.storageSetting.nfsDriveSetting.proxyPrefix',
              value: '',
              component: 'input',
              span: 12,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type !== "nfs"',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {label: t('initialization.peiZhi'), component: 'divider', hideHandle: '$.infraSettings.storageSetting.type == "nfs"'},
            {
              label: 'endpoint',
              name: 'infraSettings.storageSetting.ossBucketSetting.endpoint',
              value: '',
              component: 'input',
              span: 6,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type == "nfs"',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {
              label: 'accessKey',
              name: 'infraSettings.storageSetting.ossBucketSetting.accessKey',
              value: '',
              component: 'input',
              span: 6,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type == "nfs"',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {
              label: 'secretKey',
              name: 'infraSettings.storageSetting.ossBucketSetting.secretKey',
              value: '',
              component: 'input',
              span: 6,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type == "nfs"',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {
              label: 'bucketName',
              name: 'infraSettings.storageSetting.ossBucketSetting.bucketName',
              value: '',
              component: 'input',
              span: 6,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type == "nfs"',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {
              label: t('initialization.genMuLu'),
              name: 'infraSettings.storageSetting.ossBucketSetting.basePath',
              value: '',
              component: 'input',
              span: 6,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type == "nfs"',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {label: t('initialization.peiZhi'), component: 'divider', hideHandle: '$.infraSettings.storageSetting.type == "nfs"'},
            {
              label: t('initialization.shiFouQiYong'),
              name: 'infraSettings.storageSetting.stsSetting.enable',
              value: false,
              component: 'switch',
              span: 24,
              hideHandle: '$.infraSettings.storageSetting.type == "nfs"'
            },
            {
              label: 'region',
              name: 'infraSettings.storageSetting.stsSetting.region',
              value: '',
              component: 'input',
              span: 6,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type == "nfs" || !$.infraSettings.storageSetting.stsSetting.enable',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {
              label: 'endpoint',
              name: 'infraSettings.storageSetting.stsSetting.endpoint',
              value: '',
              component: 'input',
              span: 6,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type == "nfs" || !$.infraSettings.storageSetting.stsSetting.enable',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {
              label: 'roleArn',
              name: 'infraSettings.storageSetting.stsSetting.roleArn',
              value: '',
              component: 'input',
              span: 6,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type == "nfs" || !$.infraSettings.storageSetting.stsSetting.enable',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
            {
              label: 'roleSessionName',
              name: 'infraSettings.storageSetting.stsSetting.roleSessionName',
              value: '',
              component: 'input',
              span: 6,
              options: {
                placeholder: t('initialization.qingShuRu')
              },
              hideHandle: '$.infraSettings.storageSetting.type == "nfs" || !$.infraSettings.storageSetting.stsSetting.enable',
              rules: [{required: true, message: t('initialization.qingShuRu'), trigger: 'blur'}]
            },
          ]
        }
      )
    })

    const save = () => {
      dataFormRef.value.validate((valid, obj) => {
        if (valid) {
          $api.auth.setInitialized(dataForm.value).then(() => {
            ElMessage.success(t('initialization.chuShiHuaChengGong'))
            $tool.data.set('zoneInitialized', 'true')
            router.push('/')
          })
        }
      })
    }

    return {
      save,
      dataFormRef,
      formConfig,
      dataForm
    }
  }
}
</script>
