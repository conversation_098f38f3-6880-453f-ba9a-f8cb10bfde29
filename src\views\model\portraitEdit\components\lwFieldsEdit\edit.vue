<template>
  <div class="field-edit">
    <lwFormMini
      v-if="dataForm?.fieldName"
      ref="dataFormRef"
      :config="config"
      :isView="isView"
      v-model="dataForm"
      :loading="loading">
    </lwFormMini>

    <el-empty v-else :description="$t('modelPortraitEdit.QXZYBJDZD')" />
  </div>
</template>

<script>
import { ref, computed, onMounted, getCurrentInstance, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import lwDictEdit from '@/components/lwDictEdit/index.vue'

export default {
  components: {
    lwDictEdit
  },
  props: {
    modelValue: {
      type: Object,
      default: () => {}
    }
  },
  setup(props, { emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, t }
    } = getCurrentInstance()

    // 表单数据
    const dataForm = ref({})
    const dataFormRef = ref(null)
    const isView = computed(() => !!route.query.isView)
    const loading = ref(false)

    const init = () => {
      dataForm.value = props.modelValue
    }

    // 获取类型
    const options = ref([])
    const getTypes = async () => {
      const res = await $api.modelPersistent.fieldTypes()
      options.value = res.map((item) => ({ label: item, value: item.toLowerCase() }))
    }

    watch(
      () => dataForm.value,
      (val) => {
        emit('update:modelValue', val)
      },
      { deep: true }
    )

    const config = computed(() => {
      return {
        labelWidth: '60px',
        labelPosition: 'top',
        size: 'small',
        formItems: [
          {
            label: t('modelPortraitEdit.ziDuan'),
            name: 'fieldName',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('modelPortraitEdit.ziDuan')
            },
            rules: [{ required: true, message: t('modelPortraitEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPortraitEdit.xianShiZhongWen'),
            name: 'viewFieldNameCn',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('modelPortraitEdit.zhongWenMing')
            },
            rules: [{ required: true, message: t('modelPortraitEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPortraitEdit.xianShiYingWen'),
            name: 'viewFieldNameEn',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('modelPortraitEdit.yingWenMing')
            },
            rules: [{ required: true, message: t('modelPortraitEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: 'piiData',
            name: 'piiData',
            value: false,
            span: 8,
            component: 'switch'
          },
          // 可视化设置
          {
            label: t('modelPortraitEdit.xiangQingXianShi'),
            name: 'visualSetting.detailDisplay',
            value: false,
            span: 8,
            component: 'switch'
          },
          {
            label: t('modelPortraitEdit.lieBiaoXianShi'),
            name: 'visualSetting.listDisplay',
            value: false,
            span: 8,
            component: 'switch'
          },
          {
            label: t('modelPortraitEdit.zhanShi'),
            name: 'piiDataSetting.piiDataDisplayType',
            value: '',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('modelPortraitEdit.qingXuanZe'),
              items: [
                { label: t('modelPortraitEdit.zhiJieZhanShi'), value: 'ORIGINAL' },
                { label: t('modelPortraitEdit.buZhanShi'), value: 'NONE' },
                { label: t('modelPortraitEdit.yanMaZhanShi'), value: 'MASK' },
                { label: t('modelPortraitEdit.zhanShi'), value: 'MD5' },
                { label: t('modelPortraitEdit.zhanShi'), value: 'SHA' }
              ]
            },
            hideHandle: '!$.piiData'
          },
          {
            label: t('modelPortraitEdit.yanMaLeiXing'),
            name: 'piiDataSetting.dataMaskSetting.dataMaskType',
            value: '',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('modelPortraitEdit.qingXuanZe'),
              items: [
                { label: t('modelPortraitEdit.shouJiYanMa'), value: 'MOBILE' },
                { label: t('modelPortraitEdit.zuoJiYanMa'), value: 'PHONE' },
                { label: t('modelPortraitEdit.diZhiYanMa'), value: 'ADDRESS' },
                { label: t('modelPortraitEdit.ziDingYiYanMa'), value: 'USER_DEFINED' }
              ]
            },
            hideHandle: '!($.piiData && $.piiDataSetting.piiDataDisplayType === "MASK")'
          },
          {
            label: t('modelPortraitEdit.yanMaQiShiWeiZhi'),
            name: 'piiDataSetting.dataMaskSetting.startIndex',
            value: '',
            span: 8,
            component: 'number',
            options: {
              placeholder: t('modelPortraitEdit.yanMaQiShiWeiZhi')
            },
            hideHandle:
              '!($.piiData && $.piiDataSetting.piiDataDisplayType === "MASK" && $.piiDataSetting.dataMaskSetting.dataMaskType === "USER_DEFINED")'
          },
          {
            label: t('modelPortraitEdit.yanMaWeiShu'),
            name: 'piiDataSetting.dataMaskSetting.maskLength',
            value: '',
            span: 8,
            component: 'number',
            options: {
              placeholder: t('modelPortraitEdit.yanMaWeiShu')
            },
            hideHandle:
              '!($.piiData && $.piiDataSetting.piiDataDisplayType === "MASK" && $.piiDataSetting.dataMaskSetting.dataMaskType === "USER_DEFINED")'
          },

          {
            label: t('modelPortraitEdit.yongYuSouSuo'),
            name: 'visualSetting.useForSearch',
            value: '',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('modelPortraitEdit.qingShuRu'),
              items: [
                { label: t('modelPortraitEdit.yunXuSouSuo'), value: true },
                { label: t('modelPortraitEdit.jinZhiSouSuo'), value: false }
              ]
            }
          },
          {
            label: t('modelPortraitEdit.souSuoFangShi'),
            name: 'visualSetting.operator',
            value: 'eq',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('modelPortraitEdit.qingXuanZe'),
              items: [{ label: t('modelPortraitEdit.wanQuanPiPei'), value: 'eq' }]
            },
            hideHandle:
              '!$.visualSetting?.useForSearch || ["text", "long", "date", "integet", "short", "double", "integer"].includes($.fieldType)'
          },
          {
            label: t('modelPortraitEdit.souSuoFangShi'),
            name: 'visualSetting.operator',
            value: 'eq',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('modelPortraitEdit.qingXuanZe'),
              items: [
                { label: t('modelPortraitEdit.wanQuanPiPei'), value: 'eq' },
                { label: t('modelPortraitEdit.moHuSouSuo'), value: 'like' }
              ]
            },
            hideHandle: '!($.visualSetting?.useForSearch && $.fieldType == "TEXT")'
          },
          {
            label: t('modelPortraitEdit.souSuoFangShi'),
            name: 'visualSetting.operator',
            value: 'eq',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('modelPortraitEdit.qingXuanZe'),
              items: [
                { label: t('modelPortraitEdit.wanQuanPiPei'), value: 'eq' },
                { label: t('modelPortraitEdit.xiaoYuShuRuZhi'), value: 'lt' },
                { label: t('modelPortraitEdit.daYuShuRuZhi'), value: 'gt' },
                { label: t('modelPortraitEdit.quJian'), value: 'bt' }
              ]
            },
            hideHandle:
              '!($.visualSetting?.useForSearch && ["text", "long", "date", "integet", "short", "double", "integer"].includes($.fieldType))'
          },
          {
            label: t('modelPortraitEdit.xianShiLeiXing'),
            name: 'visualSetting.displayType',
            value: '',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('modelPortraitEdit.qingXuanZe'),
              items: options.value
            },
            hideHandle: '!($.visualSetting.detailDisplay || $.visualSetting.listDisplay)'
          },
          {
            label: t('modelPortraitEdit.shiFouHuanHang'),
            name: 'visualSetting.newLine',
            value: false,
            span: 8,
            component: 'select',
            options: {
              placeholder: t('modelPortraitEdit.qingXuanZe'),
              items: [
                { label: t('modelPortraitEdit.yunXuHuanHang'), value: true },
                { label: t('modelPortraitEdit.jinZhiHuanHang'), value: false }
              ]
            },
            hideHandle: '!($.visualSetting.detailDisplay || $.visualSetting.listDisplay)'
          },
          {
            label: t('modelPortraitEdit.riQiGeShi'),
            name: 'visualSetting.dateFormat',
            value: '',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('modelPortraitEdit.qingXuanZe'),
              items: [
                { label: 'YYYY-MM-DD hh:mm:ss', value: 'YYYY-MM-DD hh:mm:ss' },
                { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' }
              ]
            },
            hideHandle:
              '!(($.visualSetting.detailDisplay || $.visualSetting.listDisplay) && $.visualSetting.displayType == "DATE")'
          }
        ]
      }
    })

    onMounted(() => {
      getTypes()
    })

    return {
      dataForm,
      dataFormRef,
      isView,
      loading,
      config,
      options,
      init
    }
  }
}
</script>
<style lang="scss" scoped>
.field-edit {
  :deep(.lw-form-mini-card) {
    border: 0 !important;
  }
}
</style>
