import api from './api'
import config from './config'
import bus from './utils/bus'
import { expression } from './utils/common'
import http from './utils/request'
import tool from './utils/tool'

import '@/utils/generateJson'
import { registerSW } from 'virtual:pwa-register'
import 'virtual:svg-icons-register'

import * as elIcons from '@element-plus/icons'
// 挂载在线组件库  需要放在全局对象后 便于注入全局状态
import lwCdpUi, { registerServiceWorker } from 'lw-cdp-ui'
import 'lw-cdp-ui/style.css'
// 挂载本地组件库
// import '@/lw-ui/src/assets/scss/index'
// import lwCdpUi, { registerServiceWorker } from '@/lw-ui/src/index'

export default {
  install(app) {
    //挂载全局对象
    app.config.globalProperties.$config = config
    app.config.globalProperties.$tool = tool
    app.config.globalProperties.$http = http
    app.config.globalProperties.$api = api
    app.config.globalProperties.$bus = bus
    app.config.globalProperties.$expression = expression

    // 注册私有组件库
    app.use(lwCdpUi)

    //统一注册el-icon图标
    for (let icon in elIcons) {
      app.component(`ElIcon${icon}`, elIcons[icon])
    }

    // 注册service worker
    registerServiceWorker(registerSW)
  }
}
