<template>
  <el-container>
    <el-main>
      <lwFormMini ref="dataFormRef" :config="config" :isView="isView" v-model="dataForm" :loading="loading">
        <template #fileUserTag>
          <el-button class="import-btn" type="primary" icon="el-icon-upload">
            {{$t('userMarkingEdit.xuanZe')}} {{ dataForm.fieldName }} {{$t('userMarkingEdit.wenJian')}}
            <input type="file" @change="handleImport" accept=".csv" />
          </el-button>
          <p class="p-item">
            {{$t('userMarkingEdit.ZNSCDGWJ')}} [ {{$t('userMarkingEdit.suoXuanZiDuan')}} ]{{$t('userMarkingEdit.deZhi')}}
            {{ dataForm.fieldName ? `${t('userMarkingEdit.suoXuanZiDuanMingCheng')}${dataForm.fieldName}` : '' }}
          </p>
        </template>
      </lwFormMini>
    </el-main>
    <el-footer class="footer-body">
      <el-button @click="close">{{ $t('btn.close') }}</el-button>
      <el-button v-if="!isView" type="primary" @click="save">{{ $t('btn.save') }}</el-button>
    </el-footer>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
