const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

const packageJsonPath = path.join(__dirname, 'package.json')
const tempFilePath = path.join(__dirname, 'temp-deps.json')

// 读取 package.json
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'))

// 备份 dependencies 和 devDependencies
const backup = {
  dependencies: packageJson.dependencies,
  devDependencies: packageJson.devDependencies
}

// 检查 Git 工作目录是否干净
function isGitClean() {
  try {
    const status = execSync('git status --porcelain').toString().trim()
    return status === ''
  } catch (error) {
    console.error('Failed to check Git status:', error)
    return false
  }
}

// 执行发布命令
try {
  console.log('Starting release process...')

  // 检查 Git 状态
  if (!isGitClean()) {
    console.error('Git working directory is not clean. Please commit or stash changes before release.')
    process.exit(1)
  }


  // 1. 备份并删除字段
  fs.writeFileSync(tempFilePath, JSON.stringify(backup, null, 2))
  delete packageJson.dependencies
  delete packageJson.devDependencies
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))

  // 2. 构建和发布
  execSync('npm run build && npm publish', { stdio: 'inherit' })
  console.log('Release completed successfully.')
} catch (error) {
  console.error('Release failed:', error)
} finally {
  // 恢复备份
  const restoredPackageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'))
  restoredPackageJson.dependencies = backup.dependencies
  restoredPackageJson.devDependencies = backup.devDependencies
  fs.writeFileSync(packageJsonPath, JSON.stringify(restoredPackageJson, null, 2))
  fs.unlinkSync(tempFilePath) // 删除临时文件
  console.log('Dependencies restored.')
}
