import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'userMarkingEdit',
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)

    // 获取标签列表
    const tagList = ref([])
    const getTagList = async () => {
      let res = await $api.userTag.list()
      tagList.value = res.filter((item) => item.tagType === 'hand')
    }

    // 获取客户列表
    const fieldList = ref([])
    const consumerMainBodyIdentifies = ref([])
    const getFieldList = async () => {
      let res = await $api.modelPersistent.list()
      let consumer = res.filter((item) => {
        return item.dataPersistentInfo.persistentType === 'consumer'
      })?.[0]
      fieldList.value = consumer?.fields
      consumerMainBodyIdentifies.value = consumer.dataPersistentInfo.consumerMainBodyIdentifies || []
    }

    const config = computed(() => {
      return {
        labelWidth: '110px',
        labelPosition: 'right',
        size: 'default',
        formItems: [
          {
            label: t('userMarkingEdit.biaoQian'),
            name: 'tagCode',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('userMarkingEdit.qingXuanZe'),
              items: tagList.value.map((item) => {
                return {
                  label: item.name,
                  value: item.code
                }
              })
            },
            rules: [{ required: true, message: t('userMarkingEdit.qingXuanZe'), trigger: 'change' }]
          },
          {
            label: t('userMarkingEdit.ziDuanLeiXing'),
            name: 'tagAssignFieldType',
            value: '',
            span: 24,
            component: 'select',
            options: {
              placeholder: t('userMarkingEdit.qingXuanZe'),
              items: [
                { label: t('userMarkingEdit.keHuMoXingZiDuan'), value: 'enable_search_field' },
                { label: t('userMarkingEdit.shenFenBiaoShiZiDuan'), value: 'fixed_identify' },
                { label: 'cdp_id', value: 'cdp_id' }
              ]
            },
            rules: [{ required: true, message: t('userMarkingEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('userMarkingEdit.ziDuan'),
            name: 'fieldName',
            value: '',
            span: 24,
            component: 'input',
            options: {
              placeholder: t('userMarkingEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('userMarkingEdit.qingShuRu'), trigger: 'blur' }],
            hideHandle: '$.tagAssignFieldType != "cdp_id"'
          },
          {
            label: t('userMarkingEdit.keHuMoXingZiDuan'),
            name: 'fieldName',
            value: [],
            span: 24,
            component: 'cascader',
            options: {
              placeholder: t('userMarkingEdit.qingXuanZe'),
              items: fieldList.value,
              props: {
                children: 'fields',
                label: 'aliasName',
                value: 'name'
              }
            },
            rules: [{ required: true, message: t('userMarkingEdit.qingXuanZe'), trigger: 'blur' }],
            hideHandle: '$.tagAssignFieldType != "enable_search_field"'
          },
          {
            label: t('userMarkingEdit.shenFenBiaoShiZiDuan'),
            name: 'fieldName',
            value: '',
            span: 24,
            component: 'select',
            options: {
              placeholder: t('userMarkingEdit.qingXuanZe'),
              items: consumerMainBodyIdentifies.value.map((item) => {
                return {
                  label: item.identifyValueFieldName,
                  value: item.identifyValueFieldName
                }
              })
            },
            rules: [{ required: true, message: t('userMarkingEdit.qingXuanZe'), trigger: 'blur' }],
            hideHandle: '$.tagAssignFieldType != "fixed_identify"'
          },
          {
            label: t('userMarkingEdit.qingChuLiShi'),
            name: 'cleanTagHistory',
            value: false,
            span: 12,
            component: 'switch'
          },
          {
            label: t('userMarkingEdit.wenJian'),
            span: 24,
            component: 'fileUserTag'
          }
        ]
      }
    })

    // 查询详情
    const dataForm = ref({})
    const dataFormRef = ref(null)
    const loading = ref(false)
    const fetchDetail = async () => {
      let res = await $api.userMarking.info({ id: id.value })
      dataForm.value = res
    }

    onMounted(() => {
      getTagList()
      getFieldList()
      if (id.value) {
        fetchDetail()
      }
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { file, ...query } = dataForm.value
          if (query.tagAssignFieldType == 'enable_search_field') {
            query.fieldName = query.fieldName.join('.')
          }

          let params = `tagCode=${query.tagCode}&tagAssignFieldType=${query.tagAssignFieldType}&fieldName=${query.fieldName}&cleanTagHistory=${query.cleanTagHistory}`

          await $api.userMarking[id.value ? 'edit' : 'add'](params, file)
          ElMessage({ type: 'success', message: t('userMarkingEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/user/marking', 'userMarking')
    }

    // 批量导入
    const handleImport = async (event) => {
      const file = event.target.files[0]
      const maxSize = 20 * 1024 * 1024
      if (!file) return

      if (file.size > maxSize) {
        ElMessage({ type: 'warning', message: '文件大小不能超过20MB' })
        event.target.value = ''
        return
      }

      const data = new FormData()
      data.append('file', file)
      dataForm.value.file = data
    }

    return {
      dataForm,
      dataFormRef,
      config,
      loading,
      isView,
      handleImport,
      save,
      close
    }
  }
}
