import vue from '@vitejs/plugin-vue'
import path from 'path'
import { defineConfig } from 'vite'
import { VitePWA } from 'vite-plugin-pwa'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import config from './src/config'

export default defineConfig({
  base: `/${config.APP_NAME.toLowerCase()}/`,
  plugins: [
    vue(),
    createSvgIconsPlugin({
      // 要缓存的图标文件夹
      iconDirs: [path.resolve(process.cwd(), 'src/assets/svgIcon')],
      // 执行 icon name 的格式
      symbolId: '[name]'
    }),
    VitePWA({
      registerType: 'prompt',
      filename: `sw-${config.APP_NAME}.js`,
      manifest: {
        name: `联蔚数字科技营销云-${config.APP_NAME}`,
        short_name: `联蔚-${config.APP_NAME}`,
        description: `联蔚数字科技营销云 PWA App -${config.APP_NAME}`,
        theme_color: '#84ADEA',
        icons: [
          {
            src: 'icons/logo.svg',
            sizes: '192x192',
            type: 'image/svg+xml'
          },
          {
            src: 'icons/logo.svg',
            sizes: '512x512',
            type: 'image/svg+xml'
          }
        ]
      },
      devOptions: {
        enabled: false // 开发环境中禁用 PWA
      },
      workbox: {
        maximumFileSizeToCacheInBytes: 8 * 1024 * 1024,
        globPatterns: ['**/*.{js,css,html,png,jpg,svg}']
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    },
    extensions: ['.scss', '.js', '.css', '.json', '.vue']
  },
  server: {
    port: 5000,
    proxy: {
      '/api': {
        target: 'https://test-cdp.wahlap.net',
        changeOrigin: true,
        secure: false
      },
      '/mock': {
        target: 'https://apifoxmock.com/m2/1562089-0-default',
        changeOrigin: true
      }
    }
  },
  build: {
    target: 'esnext',
    outDir: 'dist',
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            if (id.includes('echarts')) {
              return 'echarts'
            }
            if (id.includes('lw-cdp-ui')) {
              return 'lw-cdp-ui'
            }
            return 'vendor'
          }
        }
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        silenceDeprecations: ['legacy-js-api'],
        additionalData: `@import "@/assets/scss/variables.scss";`
      }
    }
  }
})
