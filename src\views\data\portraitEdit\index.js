import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import TabTable from './components/TabTable.vue'
import TagsView from './components/TagsView.vue'
import UserHistory from './components/UserHistory.vue'

export default {
  name: 'userPortraitEdit',
  components: {
    TagsView,
    UserHistory,
    TabTable
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const code = computed(() => route.query.code)
    const name = ref('')
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const config = ref({
      labelWidth: '130px',
      labelPosition: 'right',
      size: 'default',
      formItems: []
    })

    // 处理标签数据
    const transformToElTreeData = (data) => {
      // 第一层：按 categoryId 分组
      const categoryMap = new Map()

      data.forEach((item) => {
        // 构建 category 节点
        if (!categoryMap.has(item.categoryId)) {
          categoryMap.set(item.categoryId, {
            id: item.categoryId,
            label: item.categoryName,
            parentId: item.categoryParentId,
            children: new Map()
          })
        }

        const categoryNode = categoryMap.get(item.categoryId)

        // 第二层：按 tagGroupId 分组
        if (!categoryNode.children.has(item.tagGroupId)) {
          categoryNode.children.set(item.tagGroupId, {
            id: item.tagGroupId,
            label: item.tagGroupName,
            children: []
          })
        }

        const tagGroupNode = categoryNode.children.get(item.tagGroupId)

        // 第三层：tagCode 节点
        tagGroupNode.children.push({
          id: item.tagCode,
          label: item.tagName
        })
      })

      // 将 Map 转换为数组结构
      const result = Array.from(categoryMap.values()).map((category) => ({
        id: category.id,
        label: category.label,
        children: Array.from(category.children.values()).map((tagGroup) => ({
          id: tagGroup.id,
          label: tagGroup.label,
          children: tagGroup.children
        }))
      }))

      return result
    }

    // 获取字段配置
    const fieldList = ref([])
    const tagList = ref([])
    const getFieldConfig = async () => {
      let list = await $api.modelPersistent.list()
      fieldList.value = list.filter((item) => {
        return !!item?.enable360
      })
      let infoItem = list.find((item) => item.dataPersistentInfo.persistentType === 'consumer')
      name.value = infoItem.name
      let res = await $api.userPortrait.fields({ id: infoItem.id })

      // 过滤字段
      res = res.filter((item) => {
        return item.dataModelVisualSetting.detailDisplay
      })

      config.value.formItems = []
      // 基础信息
      let formItems = [{ label: t('userPortraitEdit.jiChuXinXi'), component: 'divider' }]
      res.forEach((item) => {
        let aliasName = item.aliasName.split('.')?.[1] || item.aliasName
        let fieldType = item.fieldType.toLowerCase() === 'date' ? 'date' : 'input'

        if (item.fieldName == 'basicInfo.avatarUrl') {
          formItems.splice(1, 0, {
            label: aliasName,
            name: item.fieldName,
            span: 6,
            component: 'upload',
            options: {
              items: [
                {
                  label: t('userPortraitEdit.shangChuanTuPian'),
                  name: item.fieldName,
                  value: ''
                }
              ]
            }
          })
        } else {
          formItems.push({
            label: aliasName,
            name: item.fieldName,
            span: 6,
            component: fieldType,
            options: {
              placeholder: t('userPortraitEdit.qingShuRu')
            }
          })
        }
      })

      // 用户标签
      formItems.push({ label: t('userPortraitEdit.yongHuBiaoQian'), component: 'divider' })
      formItems.push({ component: 'tagsView' })
      let tagRes = await $api.userPortrait.tags({ id: id.value })
      tagList.value = transformToElTreeData(tagRes)

      // 其他事件
      fieldList.value.forEach((item) => {
        formItems.push({ label: item.aliasName, component: 'divider' })
        formItems.push({ component: item.name })
      })

      config.value.formItems = formItems
    }

    // 查询详情
    const dataForm = ref({ source: t('userPortraitEdit.shouDongXinZeng'), status: 'on' })
    const dataFormRef = ref(null)
    const loading = ref(false)
    const identity = ref('')
    const fetchDetail = async () => {
      let res = await $api.userPortrait.customer({ id: id.value, name: name.value })
      identity.value = res?.cdp_identity || ''
      dataForm.value = res
    }

    onMounted(() => {
      loading.value = true
      Promise.all([getFieldConfig()]).then(() => {
        if (id.value) {
          fetchDetail()
          loading.value = false
        }
      })
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { time, updateTime, ...params } = JSON.parse(JSON.stringify(dataForm.value))

          params.levels = params.levels.join(',')
          params.giftType = params.giftType.join(',')

          if (time?.length) {
            params.activeTime = time[0]
            params.expireTime = time[1]
          }

          await $api.userPortrait[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('userPortraitEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      let path = '/user/portrait'
      if (code.value) {
        path = '/user/portrait?id=' + code.value
      }
      store.state.viewTags.closeTagAndJump(path)
    }

    return {
      dataForm,
      dataFormRef,
      config,
      loading,
      tagList,
      isView,
      identity,
      fieldList,
      name,
      save,
      close
    }
  }
}
