import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, provide, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import ConsumerItem from '../tagEdit/components/ConsumerItem/index.vue'
import OtherFilter from './components/OtherFilter.vue'

export default {
  name: 'userCircleEdit',
  components: { ConsumerItem, OtherFilter },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    // 数据角色
    const BAR_DATA = $tool.data.get('BAR_DATA') || {}
    const roleList = ref([{ label: t('userCircleEdit.moRen'), value: '0' }, ...BAR_DATA.dataRoles])

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('userCircleEdit.jiChuXinXi'), component: 'divider' },
          {
            label: t('userCircleEdit.renQunMingCheng'),
            name: 'name',
            value: '',
            span: 12,
            component: 'input',
            options: {
              maxlength: 20,
              placeholder: t('userCircleEdit.qingShuRu')
            },
            rules: [
              { required: true, message: t('userCircleEdit.qingShuRu'), trigger: 'blur' },
              { pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/, message: t('userCircleEdit.ZNSRZWZMSZHX'), trigger: 'blur' }
            ]
          },
          {
            label: t('userCircleEdit.shuJuJueSe'),
            name: 'dataRoles',
            value: [],
            span: 12,
            component: 'select',
            options: {
              placeholder: t('userCircleEdit.qingXuanZe'),
              multiple: true,
              items: roleList.value
            }
          },
          {
            label: t('userCircleEdit.renQunMiaoShu'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              maxlength: 200,
              type: 'textarea',
              placeholder: t('userCircleEdit.qingShuRu')
            }
          },
          { label: t('userCircleEdit.guoLüXinXi'), component: 'divider', options: { component: 'ConsumerListBtn' } },
          { component: 'ConsumerList' },
          { label: t('userCircleEdit.qiTaTiaoJian'), component: 'divider' },
          { component: 'OtherList' }
        ]
      }
    })

    // 查询详情
    const dataForm = ref({
      queryCondition: {
        conditions: [],
        aggregation: '',
        valueSource: 'fixed_value',
        rangeType: 'default',
        relation: 'and',
        rangeDataList: [],
        sortOrderList: []
      }
    })
    const dataFormRef = ref(null)
    const loading = ref(false)
    const fetchDetail = async () => {
      let res = await $api.userCircle.info({ id: id.value })
      dataForm.value = res
    }

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { time, updateTime, ...params } = JSON.parse(JSON.stringify(dataForm.value))

          params.levels = params?.levels?.join(',') || ''
          params.giftType = params?.giftType?.join(',') || ''

          if (time?.length) {
            params.activeTime = time[0]
            params.expireTime = time[1]
          }

          await $api.userCircle[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('userCircleEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/user/circle', 'userCircle')
    }

    // 添加条件
    const optionsPersistent = ref({
      consumerMap: {},
      typeGroup: '',
      tagList: []
    })
    const add = () => {
      if (!dataForm.value.queryCondition) {
        dataForm.value.queryCondition = []
      }
      dataForm.value.queryCondition.conditions.push({
        dataPersistentModelId: optionsPersistent.value.consumerMap.id || '',
        fieldName: '',
        fieldType: '',
        aggregation: '',
        aggregationList: [],
        operator: '',
        value: '',
        valueSource: 'fixed_value',
        consumerFieldName: '',
        targetFieldName: '',
        rangeType: 'default',
        rangeFieldName: '',
        relation: 'and',
        timeStr: '',
        conditions: []
      })
    }
    const getConsumer = async () => {
      optionsPersistent.value.consumerMap = await $api.modelPersistent.consumer()
      dataForm.value.queryCondition.dataPersistentModelId = optionsPersistent.value.consumerMap.id || ''
    }
    const getTag = async () => {
      let res = await $api.userTag.view()
      optionsPersistent.value.tagList = res.reduce((acc, current) => {
        const existing = acc.find((item) => item.tagId === current.tagId)
        if (!existing) {
          acc.push(current)
        }
        return acc
      }, [])
    }

    // 传参
    provide('optionsPersistent', optionsPersistent)

    onMounted(() => {
      loading.value = true
      Promise.all([getTag(), getConsumer()])
        .then(() => {
          if (id.value) {
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    return {
      dataForm,
      dataFormRef,
      config,
      loading,
      isView,
      add,
      save,
      close
    }
  }
}
