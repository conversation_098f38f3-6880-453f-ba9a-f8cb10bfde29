<template>
  <el-container>
    <el-main>
      <lw-search
        :options="searchOptions"
        v-model="searchParams"
        :columnNumber="layout.grid"
        :expandNumber="4"
        :labelWidth="layout.labelWidth + 'px'"
        :hideLabel="layout.hideLabel"
        :labelAlign="layout.labelAlign"
        @search="search"
        @reset="reset" />
      <div class="table-block">
        <div class="btn-container">
          <el-button type="primary" v-auth="['cdp.user_tag.create']" icon="el-icon-plus" @click="edit"></el-button>
          <el-popconfirm
            v-auth="['cdp.user_tag_history']"
            class="box-item"
            :title="$t('userTag.QRZXQLKZM')"
            placement="top"
            @confirm="batchSnapshot('*')">
            <template #reference>
              <el-button type="primary">
                {{ $t('userTag.quanLiangKuaiZhao') }}
              </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm
            v-auth="['cdp.user_tag_history']"
            class="box-item"
            :title="$t('userTag.QRZXQLJSM')"
            placement="top"
            @confirm="batchCalculate('*')">
            <template #reference>
              <el-button type="primary">
                {{ $t('userTag.quanLiangJiSuan') }}
              </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm
            v-auth="['cdp.user_tag_history']"
            class="box-item"
            :title="$t('userTag.QRZXPLKZM')"
            placement="top"
            @confirm="batchSnapshot()">
            <template #reference>
              <el-button type="primary" :disabled="selection.length == 0">
                {{ $t('userTag.piLiangKuaiZhao') }}
              </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm
            v-auth="['cdp.user_tag_history']"
            class="box-item"
            :title="$t('userTag.QRZXPLJSM')"
            placement="top"
            @confirm="batchCalculate()">
            <template #reference>
              <el-button type="primary" :disabled="selection.length == 0">
                {{ $t('userTag.piLiangJiSuan') }}
              </el-button>
            </template>
          </el-popconfirm>
        </div>
        <lw-table
          :hideTool="false"
          :loading="loading"
          rowKey="id"
          :table-data="tableData"
          :tableColumns="tableHeaders"
          :search-params="searchParams"
          :isShowPagination="true"
          :rowSelection="true"
          :total-count="totalCount"
          @getTableData="getTableData"
          @multipleSelection="onSelection" />
        <!-- 导出弹窗 -->
        <el-dialog
          v-model="exportDialogVisible"
          :title="$t('userTag.daoChu')"
          width="450px"
          :close-on-click-modal="false"
          custom-class="export-dialog">
          <div style="margin-bottom: 20px">{{ $t('userTag.QXZDCMX') }}</div>
          <el-select v-model="exportModelName" :placeholder="$t('userTag.QXZDCMX')" style="width: 100%" clearable>
            <el-option
              v-for="option in exportModel"
              :key="option.name"
              :label="option.aliasName"
              :value="option.name" />
          </el-select>
          <template #footer>
            <el-button @click="cancelExport">{{ $t('userTag.quXiao') }}</el-button>
            <el-button type="primary" @click="confirmExport">{{ $t('userTag.queDing') }}</el-button>
          </template>
        </el-dialog>
      </div>
    </el-main>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
