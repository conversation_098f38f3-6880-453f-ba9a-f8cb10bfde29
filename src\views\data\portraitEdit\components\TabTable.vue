<template>
  <lw-search
    :options="searchOptions"
    v-model="searchParams"
    :columnNumber="layout.grid"
    :expandNumber="4"
    :labelWidth="layout.labelWidth + 'px'"
    :hideLabel="layout.hideLabel"
    :labelAlign="layout.labelAlign"
    @search="search"
    @reset="reset" />
  <lw-table
    :hideTool="false"
    :loading="loading"
    :table-data="tableData"
    :tableColumns="tableHeaders"
    :search-params="searchParams"
    height="calc(100vh - 305px)"
    :isShowPagination="true"
    :total-count="totalCount"
    @getTableData="getTableData" />
</template>

<script>
import { getCurrentInstance, onMounted, reactive, computed, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'TabTable',
  props: {
    id: {
      type: String,
      required: true
    },
    fields: {
      type: Array,
      default: () => []
    },
    identity: {
      type: Array,
      default: []
    }
  },
  setup(props) {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        cdp_identity_Q_eq: props.identity.toString()
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 获取显示字段
    const getFieldList = async (res = props.fields) => {
      // 过滤字段
      res = res.filter((item) => {
        return item.visualSetting.listDisplay
      })

      // 搜索条件&表头
      state.searchOptions = []
      state.tableHeaders = []
      res.forEach((item) => {
        let aliasName = item.aliasName.split('.')?.[1] || item.aliasName
        if (item.visualSetting.useForSearch) {
          let config = {
            label: aliasName,
            prop: `${item.name}_Q_${item.visualSetting.operator}`,
            renderType: item.type.toLowerCase() === 'date' ? 'dateRange' : 'input'
          }
          if (item.type.toLowerCase() === 'date') {
            config.prop = `${item.name}_Q_ge_lt`
            config.span = 12
            config.startPlaceholder = `${aliasName}${t('userPortraitEdit.kaiShiShiJian')}`
            config.endPlaceholder = `${aliasName}${t('userPortraitEdit.jieShuShiJian')}`
          }
          state.searchOptions.push(config)
        }
        state.tableHeaders.push({
          title: aliasName,
          dataIndex: item.name,
          minWidth: item.type == 'DATE' ? '180' : 60 + aliasName.length * 12,
          tooltip: true,
          date: item.type == 'DATE'
        })
      })
    }

    onMounted(() => {
      state.loading = true
      Promise.all([getFieldList()])
        .then(() => {
          getTableData()
        })
        .finally(() => {
          state.loading = false
        })
    })

    const getTableData = async (page = 0) => {
      let params = {
        pageIndex: page,
        pageSize: state.searchParams.size,
        sort: 'updateTime,desc',
        expression: $expression(state.searchParams)
      }

      const res = await $api.userPortrait.tabTable(props.id, params)
      state.tableData = res.content
      state.currentPage = page
      state.totalCount = res.totalElements
      state.searchParams.page = page + 1
    }

    const reset = () => {
      state.searchParams = {
        page: 1,
        size: 10,
        cdp_identity_Q_eq: props.identity.toString()
      }
      getTableData(0)
    }

    const search = () => {
      getTableData()
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.cdp_id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/user/portraitEdit', query })
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      search,
      edit
    }
  }
}
</script>
