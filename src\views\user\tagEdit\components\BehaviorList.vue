<template>
  <div class="consumer-ist">
    <div class="top-title">
      <strong>{{ $t('userTagEdit.yongHuXingWeiManZu') }}</strong>
      <el-button v-if="!isView" type="primary" plain @click="add" size="small">{{
        $t('userTagEdit.xinZeng')
      }}</el-button>
    </div>
    <div class="condition-list">
      <BehaviorItem v-model="dataForm" :isTop="true" />
      <el-alert
        v-if="dataForm.conditions.length == 0"
        center
        :title="$t('userTagEdit.zanWuShuJu')"
        type="info"
        class="info-tips"
        :closable="false" />
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, inject } from 'vue'
import BehaviorItem from './BehaviorItem/index.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const {
  proxy: { $api, $tool, $bus, t }
} = getCurrentInstance()

const isView = computed(() => !!route.query.isView)
const dataForm = defineModel({
  type: Object,
  default: () => ({})
})
const add = () => {
  if (!dataForm.value.conditions) {
    dataForm.value.conditions = []
  }
  dataForm.value.conditions.push({
    dataPersistentModelId: '',
    fieldName: '',
    fieldType: '',
    aggregation: '',
    aggregationList: [],
    operator: '',
    value: '',
    valueSource: 'fixed_value',
    consumerFieldName: '',
    targetFieldName: '',
    rangeType: 'default',
    rangeFieldName: '',
    relation: 'and',
    conditions: []
  })
}
</script>

<style lang="scss" scoped>
.consumer-ist {
  width: 100%;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    strong {
      font-size: 14px;
    }
  }
  .info-tips {
    margin-top: 5px;
    padding: 5px;
  }
}
</style>
