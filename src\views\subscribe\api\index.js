import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'subscribeApi',
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()
    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        targetType_eq: 'interface'
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 修改状态
    const changeNeedRetry = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.subscribeApi.edit({
        ...item,
        needRetry: value
      })
    }
    const changeNeedLogging = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.collectDatabase.edit({
        ...item,
        needLogging: value
      })
    }

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('subscribeApi.bianMa'),
          prop: 'name_like',
          renderType: 'input'
        },
        {
          label: t('subscribeApi.mingCheng'),
          prop: 'aliasName_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('subscribeApi.bianMa'), dataIndex: 'name', width: '120', tooltip: true },
        { title: t('subscribeApi.mingCheng'), dataIndex: 'aliasName', width: '120', tooltip: true },
        { title: t('subscribeApi.luJing'), dataIndex: 'fullPath', minWidth: '220', tooltip: true, copy: true },
        {
          title: t('subscribeApi.tuiSongLeiXing'),
          dataIndex: 'subscribeModelType',
          width: '160',
          options: [
            { label: t('subscribeApi.chiJiuShuJuTuiSong'), value: 'persistent' },
            { label: t('subscribeApi.chuanShuShuJuTuiSong'), value: 'transfer' }
          ]
        },
        {
          title: t('subscribeApi.shiFouChongShi'),
          dataIndex: 'needRetry',
          width: '100',
          switch: true,
          clickFun: changeNeedRetry
        },
        {
          title: t('subscribeApi.chongShiShiJian'),
          dataIndex: 'retryDelaySeconds',
          width: '180',
          eleRender: (item) => {
            return item.retryDelaySeconds ? item.retryDelaySeconds + t('subscribeApi.miao') : '--'
          }
        },
        {
          title: t('subscribeApi.shiFouRiZhi'),
          dataIndex: 'needLogging',
          width: '100',
          switch: true,
          clickFun: changeNeedLogging
        },
        { title: t('subscribeApi.miaoShu'), dataIndex: 'description', minWidth: '160' }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '180',
        fixed: 'right',
        ellipsis: true,
        operation: [
          { clickFun: (item) => edit(item, 'view'), label: t('btn.view'), auth: ['cdp.subscribe_api.locate'] },
          { clickFun: (item) => edit(item, 'edit'), label: t('btn.edit'), auth: ['cdp.subscribe_api_edit'] },
          { clickFun: del, label: t('btn.delete'), auth: ['cdp.subscribe_api.delete'] }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        sort: 'createTime,DESC',
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.subscribeApi
        .page(params)
        .then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = {}
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/subscribe/apiEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.subscribeApi.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      edit
    }
  }
}
