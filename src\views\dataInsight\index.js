import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, reactive, ref, toRefs, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export default {
  name: 'dataInsight',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const type = computed(() => route?.query?.type || '')

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 获取显示字段
    const fieldList = ref([])
    const getFieldList = async (name) => {
      let list = fieldList.value
      let curId = list?.[0]?.id
      if (name) {
        curId = list.find((item) => item.name == name)?.id
      }
      let curName = name || list?.[0]?.name
      let res = list.find((item) => item.name == curName)?.fields

      // 搜索条件&表头
      state.searchOptions = [
        {
          label: t('dataInsight.shuJuShiTu'),
          prop: 'modelType',
          renderType: 'select',
          clearable: false,
          options: list.map((x) => ({ label: x.aliasName, value: x.name }))
        }
      ]
      state.searchParams.modelType = curName
      state.tableHeaders = []
      res?.forEach((item) => {
        let aliasName = item.aliasName.split('.')?.[1] || item.aliasName
        if (item.visualSetting.useForSearch) {
          let config = {
            label: aliasName,
            prop: `${item.name}_Q_${item.visualSetting.operator}`,
            renderType: item.type.toLowerCase() === 'date' ? 'dateRange' : 'input'
          }
          if (item.type.toLowerCase() === 'date') {
            config.prop = `${item.name}_Q_ge_lt`
            config.span = 12
            config.startPlaceholder = `${aliasName}${t('dataInsight.kaiShiShiJian')}`
            config.endPlaceholder = `${aliasName}${t('dataInsight.jieShuShiJian')}`
          }
          state.searchOptions.push(config)
        }
        state.tableHeaders.push({
          title: aliasName,
          dataIndex: item.name,
          align: 'left',
          minWidth: item.type == 'DATE' ? '180' : 60 + aliasName.length * 12,
          tooltip: true,
          copy: item.name.includes('id') || item.name.includes('Id'),
          date: item.type == 'DATE'
        })
      })

      state.loading = false
    }

    watch(
      () => state.searchParams.modelType,
      (val, old) => {
        if (val && old && val != old) {
          state.loading = true
          Promise.all([getFieldList(val)])
            .then(() => {
              getTableData()
            })
            .finally(() => {
              state.loading = false
            })
        }
      }
    )

    onMounted(async () => {
      state.loading = true
      fieldList.value = await $api.modelPersistent.list({ expression: 'delete ne true' })
      Promise.all([getFieldList(), getExportModel()])
        .then(() => {
          getTableData()
        })
        .finally(() => {
          state.loading = false
        })
    })

    const getTableData = (page = 0) => {
      state.searchParams.page = page + 1
      let params = {
        page: page,
        size: state.searchParams.size,
        expression: $expression(state.searchParams)
      }
      $api.dataInsight.page(params, state.searchParams.modelType).then((res) => {
        state.tableData = res.content
        state.currentPage = page
        state.totalCount = res.totalElements
      })
    }

    const reset = () => {
      state.searchParams = { page: 1, size: 10 }
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 导出弹窗相关
    const exportDialogVisible = ref(false)
    const exportModel = ref([])
    const exportData = ref({ name: '', expression: '' })
    const exportTargetItem = ref(null)
    const getExportModel = async () => {
      let res = await $api.modelExport.list()
      exportModel.value = res.filter((x) => x.enableExport)
    }
    const openExportDialog = (item) => {
      exportTargetItem.value = item
      exportData.value = { name: '', expression: '' }
      exportDialogVisible.value = true
    }
    const confirmExport = async () => {
      if (!exportData.value.name) {
        ElMessage({ type: 'warning', message: t('dataInsight.QXZDCMX') })
        return
      }
      let res = await $api.dataInsight.export(exportData.value.name, {
        expression: exportData.value.expression
      })
      if (res) {
        const link = document.createElement('a')
        link.href = res
        link.download = `${exportTargetItem.value.name}.csv`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        exportDialogVisible.value = false
      } else {
        ElMessage({ type: 'error', message: t('dataInsight.DCSBQSHZS') })
      }
    }
    const cancelExport = () => {
      exportDialogVisible.value = false
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      search,
      fieldList,
      exportDialogVisible,
      exportModel,
      exportData,
      exportTargetItem,
      getExportModel,
      openExportDialog,
      confirmExport,
      cancelExport
    }
  }
}
