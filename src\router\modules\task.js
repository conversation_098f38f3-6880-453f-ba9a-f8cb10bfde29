export default {
  name: 'task',
  path: '/task',
  sort: 8,
  meta: {
    icon: 'el-icon-tickets',
    type: 'menu',
    title: 'menu.task.name',
    roles: ['cdp.task']
  },
  redirect: '/task/deploy',
  children: [
    {
      name: 'taskDeploy',
      path: '/task/deploy',
      component: 'task/deploy',
      sort: 0,
      meta: {
        icon: 'el-icon-edit',
        type: 'menu',
        title: 'menu.task.deploy',
        roles: ['cdp.task_deploy']
      },
      children: [
        {
          name: 'taskDeployEdit',
          path: '/task/deployEdit',
          component: 'task/deployEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.task.deployEdit',
            active: '/task/deploy',
            hidden: true
          }
        }
      ]
    },
    {
      name: 'taskRecord',
      path: '/task/record',
      component: 'task/record',
      sort: 1,
      meta: {
        icon: 'el-icon-document-checked',
        type: 'menu',
        title: 'menu.task.record',
        roles: ['cdp.task_record']
      }
    }
  ]
}
