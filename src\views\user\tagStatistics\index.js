import dayjs from 'dayjs'
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'tagStatistics',
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $expression, t }
    } = getCurrentInstance()
    console.log(route)
    const id = computed(() => route.query.id)
    const tagName = computed(() => route.query.tagName || t('tagStatistics.biaoQianTongJi'))
    const showTime = ref(true)
    const chartOption = ref({})
    const activeTimeRange = ref('lastMonth')

    // 查询详情
    const dataForm = ref({
      dayNum: 0,
      averageNum: 0
    })

    // 监听时间选择器变化
    const handleTimeChange = (val) => {
      if (val && val.length === 2) {
        activeTimeRange.value = 'custom'
        fetchDetail()
      }
    }
    const dataFormRef = ref(null)
    const chartTime = ref([])
    const loading = ref(false)

    // 设置时间范围
    const setChartTime = (type) => {
      const now = dayjs()
      const end = now.endOf('day')
      let start = now.startOf('day')

      switch (type) {
        case 'lastWeek':
          start = now.subtract(7, 'day').startOf('day')
          break
        case 'lastMonth':
          start = now.subtract(1, 'month').startOf('day')
          break
        case 'lastThreeMonth':
          start = now.subtract(3, 'month').startOf('day')
          break
      }
      activeTimeRange.value = type
      chartTime.value = [start.toDate(), end.toDate()]
      fetchDetail()
    }

    // 处理图表数据
    const processChartData = (data) => {
      // 根据时间范围确定日期格式和数据聚合方式
      const getDateFormat = () => {
        switch (activeTimeRange.value) {
          case 'lastWeek':
            return 'MM-DD HH:mm'
          case 'lastMonth':
          case 'lastThreeMonth':
            return 'MM-DD'
          default:
            return 'MM-DD'
        }
      }
      const xData = []
      const yData = []
      const dateMap = new Map() // 生成时间序列
      const generateTimePoints = () => {
        const points = []
        const format = 'YYYY-MM-DD'

        switch (activeTimeRange.value) {
          case 'lastWeek':
            const weekNow = dayjs()
            for (let i = 6; i >= 0; i--) {
              points.push(weekNow.subtract(i, 'day').format(format))
            }
            break
          case 'lastMonth':
            const monthNow = dayjs()
            for (let i = 29; i >= 0; i--) {
              points.push(monthNow.subtract(i, 'day').format(format))
            }
            break
          case 'lastThreeMonth':
            const threeMonthNow = dayjs()
            for (let i = 89; i >= 0; i--) {
              points.push(threeMonthNow.subtract(i, 'day').format(format))
            }
            break
          case 'custom':
            const startDate = dayjs(chartTime.value[0])
            const endDate = dayjs(chartTime.value[1])
            const diffDays = endDate.diff(startDate, 'day')
            for (let i = 0; i <= diffDays; i++) {
              points.push(startDate.add(i, 'day').format(format))
            }
            break
        }
        return points
      }

      if (data && data.content) {
        // 按创建时间排序并聚合数据
        const sortedData = data.content.sort((a, b) => dayjs(a.createDate).valueOf() - dayjs(b.createDate).valueOf())

        // 数据聚合到日期Map
        sortedData.forEach((item) => {
          const dateKey = dayjs(item.createDate).format('YYYY-MM-DD')
          if (!dateMap.has(dateKey)) {
            dateMap.set(dateKey, {
              date: item.createDate,
              num: item.num || 0
            })
          } else {
            const existing = dateMap.get(dateKey)
            existing.num = item.num || existing.num // 使用最新的数据
          }
        })

        // 生成完整的时间序列并填充数据
        const timePoints = generateTimePoints()
        timePoints.forEach((dateKey) => {
          const point = dateMap.get(dateKey)
          xData.push(dayjs(dateKey).format(getDateFormat()))
          yData.push(point ? point.num : 0)
        })
      }

      // 获取图表标题
      const getChartTitle = () => {
        switch (activeTimeRange.value) {
          case 'lastWeek':
            return t('tagStatistics.JYZBHQS')
          case 'lastMonth':
            return t('tagStatistics.JYYBHQS')
          case 'lastThreeMonth':
            return t('tagStatistics.JSYBHQS')
          case 'custom':
            const start = dayjs(chartTime.value[0]).format('YYYY-MM-DD')
            const end = dayjs(chartTime.value[1]).format('YYYY-MM-DD')
            return `${start} ${t('tagStatistics.zhi')} ${end} ${t('tagStatistics.bianHuaQuShi')}`
          default:
            return t('tagStatistics.bianHuaQuShi')
        }
      }

      // 配置x轴显示
      const axisLabelConfig = {
        lastWeek: {
          interval: 0,
          rotate: 0
        },
        lastMonth: {
          interval: Math.floor(xData.length / 7),
          rotate: 30
        },
        lastThreeMonth: {
          interval: Math.floor(xData.length / 10),
          rotate: 30
        }
      }

      chartOption.value = {
        type: 'Line',
        option: {
          title: {
            text: getChartTitle()
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            formatter: function (params) {
              const item = params[0]
              return `${item.name}<br/>${item.seriesName}: ${item.value}${t('tagStatistics.ren')}`
            }
          },
          xAxis: {
            type: 'category',
            data: xData,
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              ...axisLabelConfig[activeTimeRange.value],
              formatter: (value) => {
                if (activeTimeRange.value === 'lastWeek') {
                  return dayjs(value, 'MM-DD HH:mm').format('MM-DD HH:mm')
                }
                return dayjs(value, 'MM-DD').format('MM-DD')
              }
            }
          },
          yAxis: {
            type: 'value',
            name: t('tagStatistics.renShu'),
            axisLabel: {
              formatter: (value) => {
                if (value >= 10000) {
                  return (value / 10000).toFixed(1) + 'w'
                }
                return value
              }
            }
          },
          series: [
            {
              name: t('tagStatistics.biaoQianRenShu'),
              data: yData,
              type: 'line',
              smooth: true,
              areaStyle: {
                opacity: 0.3
              },
              lineStyle: {
                width: 2
              },
              itemStyle: {
                borderWidth: 2
              },
              symbol: activeTimeRange.value === 'lastWeek' ? 'circle' : 'none',
              symbolSize: 6
            }
          ]
        }
      }
    }

    const fetchDetail = async () => {
      if (!chartTime.value || chartTime.value.length !== 2) {
        setChartTime('lastMonth')
        return
      }

      loading.value = true
      try {
        let query = {
          tagId_eq: id.value,
          createDate_Q_ge_lt: [
            dayjs(chartTime.value[0]).format('YYYY-MM-DD HH:mm:ss'),
            dayjs(chartTime.value[1]).format('YYYY-MM-DD HH:mm:ss')
          ]
        }
        let res = await $api.userTag.snapshotStatistics({ page: 0, size: 1000, expression: $expression(query) })

        // 处理显示数据
        if (res && res.content) {
          // 获取最新一条数据的人数作为今日人数
          const latestNum = res.content[0]?.num || 0

          // 计算平均人数
          const totalNum = res.content.reduce((sum, item) => sum + (item.num || 0), 0)
          const averageNum = res.content.length > 0 ? Math.round(totalNum / res.content.length) : 0

          dataForm.value = {
            dayNum: latestNum,
            averageNum: averageNum
          }

          processChartData(res)
        }
      } catch (error) {
        console.error(t('tagStatistics.HQBQTJSJSB'), error)
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      setChartTime('lastMonth') // 默认显示最近一月的数据
    })

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/user/tag')
    }

    return {
      dataForm,
      dataFormRef,
      loading,
      chartTime,
      chartOption,
      showTime,
      activeTimeRange,
      tagName,
      fetchDetail,
      setChartTime,
      handleTimeChange,
      close
    }
  }
}
