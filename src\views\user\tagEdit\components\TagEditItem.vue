<template>
  <div class="tag-edit-item">
    <lwFormMini ref="dataFormRef" :config="config" :isView="isView" v-model="dataForm">
      <template #consumerList>
        <ConsumerList v-model="dataForm.conditions[0]" />
      </template>
      <template #behaviorList>
        <BehaviorList v-model="dataForm.conditions[1]" />
      </template>
      <template #expList>
        <el-input
          v-model="dataForm.conditions[0].exp"
          :rows="5"
          type="textarea"
          :placeholder="$t('userTagEdit.qingShuRu')" />
      </template>
    </lwFormMini>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, watch, inject } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import ConsumerList from './ConsumerList.vue'
import BehaviorList from './BehaviorList.vue'

const route = useRoute()
const {
  proxy: { $api, $tool, $bus, t }
} = getCurrentInstance()
const dataForm = defineModel({
  type: Object,
  default: () => ({})
})

const props = defineProps({
  type: {
    type: String,
    default: ''
  },
  editType: {
    type: String,
    default: 'normal'
  }
})

const dataFormRef = ref(null)
const isView = computed(() => !!route.query.isView)
const isEdit = computed(() => !dataForm?.value?.isAdd)

// 判断标签显示状态
const options = inject('optionsPersistent')
const tagTypeView = computed(() => {
  if (props.editType == 'normal') {
    if (options.value.typeGroup == 'enumeration') {
      return [{ component: 'consumerList' }, { component: 'behaviorList' }]
    }

    if (options.value.typeGroup == 'numerical') {
      return [{ component: 'behaviorList' }]
    }
  } else if (props.editType == 'exp') {
    return [{ label: t('userTagEdit.biaoDaShi'), component: 'expList' }]
  }

  return []
})

const config = computed(() => {
  return {
    labelWidth: '100px',
    labelPosition: isView.value ? 'right' : 'top',
    size: 'default',
    formItems: [
      {
        label: t('userTagEdit.biaoQianZhiMingCheng'),
        name: 'name',
        value: '',
        span: 12,
        component: 'input',
        options: {
          placeholder: t('userTagEdit.qingShuRu')
        },
        rules: [{ required: true, message: t('userTagEdit.qingShuRu'), trigger: 'blur' }]
      },
      {
        label: t('userTagEdit.biaoQianZhiBianMa'),
        name: 'code',
        value: '',
        span: 12,
        component: 'input',
        options: {
          disabled: isEdit.value,
          placeholder: t('userTagEdit.qingShuRu')
        },
        rules: [{ required: true, message: t('userTagEdit.qingShuRu'), trigger: 'blur' }]
      },
      {
        label: t('userTagEdit.biaoQianZhiQuDao'),
        name: 'channels',
        value: [],
        span: 12,
        component: 'select',
        options: {
          items: [
            { value: 'qw_write', label: 'qw_write' },
            { value: 'qw_read', label: 'qw_read' },
            { value: 'default', label: 'default' },
            { value: 'ec_ncc', label: 'ec_ncc' },
            { value: 'WeChat', label: 'WeChat' },
            { value: 'CounterPortal', label: 'CounterPortal' }
          ],
          multiple: true,
          placeholder: t('userTagEdit.qingXuanZe')
        }
      },
      {
        label: t('userTagEdit.xuYaoHuiChuan'),
        name: 'needPostback',
        value: false,
        span: 12,
        component: 'switch'
      },
      ...tagTypeView.value,
      {
        label: t('userTagEdit.biaoQianZhiMiaoShu'),
        name: 'description',
        value: '',
        span: 24,
        component: 'input',
        options: {
          maxlength: 200,
          type: 'textarea',
          placeholder: t('userTagEdit.qingShuRu')
        }
      },
    ]
  }
})
</script>
<style lang="scss" scoped>
.tag-edit-item {
  :deep(.lw-form-mini-card) {
    padding: 0 !important;
    border: none !important;
    .el-form-item {
      margin-bottom: 18px !important;
    }
  }
}
</style>
