.card {
  margin-bottom: 15px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  height: 135px;
  &.task {
    height: 180px;
    background-color: #fff;
  }
  &.tag {
    height: 460px;
    background-color: #fff;
  }
  &.cloud {
    height: 265px;
    background-color: #fff;
  }
  .title {
    font-size: 18px;
    color: #474747;
    line-height: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 20px;
    a {
      cursor: pointer;
      color: #b2b3b8;
      font-size: 14px;
    }
  }
}
.card-chart {
  margin-bottom: 15px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  padding: 20px;
  background-color: #fff;
}
.input-time {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 5px;
  gap: 10px;
}
.task-list {
  display: flex;
  align-items: center;
  .el-statistic {
    flex: 1;
    text-align: center;
    :deep(.el-statistic__head) {
      font-size: 14px;
    }
    :deep(.el-statistic__number) {
      font-size: 30px;
    }
  }
}
