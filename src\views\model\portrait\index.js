import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'modelPortrait',
  setup() {
    const router = useRouter()
    const store = useStore()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()
    const BAR_DATA = $tool.data.get('BAR_DATA') || {}

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        delete_ne: true,
        dataRoles_in: BAR_DATA.dataRoleIds.toString()
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    let persistentType = {
      consumer: t('modelPortrait.keHu'),
      behavior: t('modelPortrait.xingWei'),
      normal: t('modelPortrait.puTong')
    }

    onBeforeMount(async () => {
      const options = BAR_DATA.dataRoles || []

      state.searchOptions = [
        {
          label: t('modelPortrait.huaXiangMingCheng'),
          prop: 'name_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('modelPortrait.huaXiangMingCheng'), dataIndex: 'name', minWidth: '120', tooltip: true },
        { title: t('modelPortrait.huaXiangBianMa'), dataIndex: 'code', width: '220', tooltip: true },
        {
          title: t('modelPortrait.jueSeMingCheng'),
          dataIndex: 'dataRoleNames',
          width: '220'
        },
        { title: t('modelPortrait.chuangJianShiJian'), dataIndex: 'createTime', width: '180', date: true }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '180',
        fixed: 'right',
        ellipsis: true,
        operation: [
          {
            clickFun: (item) => edit(item, 'view'),
            label: t('btn.view'),
            isShow: () => store.state.user?.status,
            auth: ['cdp.model_portrait_edit']
          },
          {
            clickFun: edit,
            label: t('btn.edit'),
            isShow: () => !store.state.user?.status,
            auth: ['cdp.model_portrait_edit']
          },
          {
            clickFun: del,
            label: t('btn.delete'),
            auth: ['cdp.model_portrait.delete']
          }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = async (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        sort: 'createTime,DESC',
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      let res = await $api.modelPortrait.page(params)

      state.tableData = res.content
      state.currentPage = page
      state.totalCount = res.totalElements
      state.loading = false
    }

    const reset = () => {
      state.searchParams = { delete_ne: true, dataRoles_in: BAR_DATA.dataRoleIds.toString() }
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    const edit = (item, type) => {
      let query = { id: item.id }
      if (type == 'view') {
        query.isView = true
      }
      router.push({ path: '/model/portraitEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.modelPortrait.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    const add = () => {
      router.push({ path: '/model/portraitEdit' })
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      add
    }
  }
}
