<template>
  <div class="consumer-dataForm-select">
    <!-- 字段选择 -->
    <el-cascader
      v-model="cascaderValue"
      :options="consumerList"
      :props="{
        value: 'name',
        label: 'aliasName',
        children: 'fields'
      }"
      placeholder="选择字段"
      filterable
      class="field-select"
      @change="handleFieldChange" />

    <!-- 操作符 -->
    <el-select
      :disabled="!fieldConfig"
      v-model="dataForm.operator"
      placeholder="操作符"
      class="operator-select"
      @change="handleOperatorChange">
      <el-option v-for="op in operatorMap[dataForm.fieldType]" :key="op" :label="operatorLabels[op]" :value="op" />
    </el-select>

    <!-- 值输入 -->
    <div class="value" :class="[dataForm.operator]">
      <!-- 空值判断 -->
      <template v-if="dataForm.operator === 'empty'">
        <el-select
          v-model="dataForm.fieldValue"
          placeholder="请选择"
          class="value-input"
          :disabled="!dataForm.operator"
          clearable>
          <el-option label="是" value="true" />
          <el-option label="否" value="false" />
        </el-select>
      </template>

      <!-- 其他输入类型 -->
      <template v-else>
        <!-- 数字输入 -->
        <template v-if="fieldConfig?.type === 'NUMBER'">
          <template v-if="dataForm.operator === 'between'">
            <div class="between-input">
              <el-input-number
                v-model="dataForm.fieldValue[0]"
                controls-position="right"
                placeholder="最小值"
                class="value-input" />
              <span class="separator">-</span>
              <el-input-number
                v-model="dataForm.fieldValue[1]"
                controls-position="right"
                placeholder="最大值"
                class="value-input" />
            </div>
          </template>
          <template v-else-if="dataForm.operator === 'in'">
            <el-select
              v-model="dataForm.fieldValue"
              multiple
              filterable
              clearable
              collapse-tags
              class="value-input"
              placeholder="请选择多个值">
              <el-option v-for="(val, index) in dataForm.fieldValue || []" :key="index" :label="val" :value="val" />
            </el-select>
          </template>
          <el-input-number
            v-else
            v-model="dataForm.fieldValue"
            controls-position="right"
            placeholder="请输入"
            class="value-input" />
        </template>

        <!-- 日期选择 -->
        <template v-else-if="fieldConfig?.type === 'DATE'">
          <!-- 日期范围 -->
          <template v-if="dataForm.operator === 'between'">
            <el-date-picker
              v-model="dataForm.fieldValue"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="value-input date-range"
              value-format="YYYY-MM-DD" />
          </template>

          <!-- 相对时间(bf/af) -->
          <template v-else-if="['bf', 'af', 'dateRange'].includes(dataForm.operator)">
            <el-select
              :model-value="dateValue.direction"
              @update:model-value="(val) => (dateValue = { ...dateValue, direction: val })"
              class="direction-select">
              <el-option label="过去" value="-" />
              <el-option label="未来" value="+" />
            </el-select>
            <el-input-number
              :model-value="dateValue.amount"
              @update:model-value="(val) => (dateValue = { ...dateValue, amount: val })"
              :min="0"
              class="amount-input"
              placeholder="值"
              controls-position="right" />
            <el-select
              :model-value="dateValue.unit"
              @update:model-value="(val) => (dateValue = { ...dateValue, unit: val })"
              class="unit-select"
              placeholder="单位">
              <el-option label="日" value="d" />
              <el-option label="月" value="M" />
              <el-option v-if="dataForm.operator != 'dateRange'" label="年" value="y" />
              <el-option v-if="dataForm.operator != 'dateRange'" label="整日" value="!d" />
              <el-option v-if="dataForm.operator != 'dateRange'" label="整月" value="!M" />
              <el-option v-if="dataForm.operator != 'dateRange'" label="整年" value="!y" />
            </el-select>
            <!-- 时间选择器 -->
            <!-- <el-time-picker
              class="time-picker"
              v-if="dateValue.unit === 'd' && dataForm.operator != 'dateRange'"
              v-model="dataForm.timeStr"
              placeholder="时间"
              value-format="HH:mm:ss" /> -->
          </template>

          <!-- 月份选择 -->
          <template v-else-if="dataForm.operator === 'monthIn'">
            <el-select v-model="dataForm.fieldValue" placeholder="月份" class="value-input">
              <el-option v-for="m in 12" :key="m" :label="`${m}月`" :value="m.toString()" />
            </el-select>
          </template>

          <!-- 时间周期 -->
          <template v-else-if="dataForm.operator === 'monthPeriod'">
            <el-select
              :model-value="dateValue.direction"
              @update:model-value="(val) => (dateValue = { ...dateValue, direction: val })"
              class="direction-select">
              <el-option label="过去" value="-" />
              <el-option label="未来" value="+" />
            </el-select>
            <el-input-number
              :model-value="dateValue.amount"
              @update:model-value="(val) => (dateValue = { ...dateValue, amount: val })"
              :min="0"
              class="amount-input"
              placeholder="值"
              controls-position="right" />
            <el-select
              :model-value="dateValue.unit"
              @update:model-value="(val) => (dateValue = { ...dateValue, unit: val })"
              class="unit-select"
              placeholder="单位">
              <el-option value="F" label="月上旬" />
              <el-option value="L" label="月下旬" />
            </el-select>
          </template>

          <!-- 普通日期选择 -->
          <el-date-picker
            v-else
            v-model="dataForm.fieldValue"
            type="date"
            placeholder="请选择日期"
            class="value-input"
            value-format="YYYY-MM-DD" />
        </template>

        <!-- 字典选择 -->
        <template v-else-if="fieldConfig?.type === 'DICT'">
          <el-select
            v-model="dataForm.fieldValue"
            :multiple="dataForm.operator === 'in'"
            filterable
            clearable
            collapse-tags
            class="value-input"
            placeholder="请选择">
            <el-option
              v-for="opt in getDictOptions(fieldConfig.dictId)"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value" />
          </el-select>
        </template>

        <!-- 文本输入 -->
        <el-input
          v-else
          v-model="dataForm.fieldValue"
          placeholder="请输入"
          :disabled="!dataForm.operator"
          class="value-input"
          clearable />
      </template>
    </div>

    <!-- 按钮组 -->
    <div v-if="!isView" class="btn-group">
      <el-tooltip effect="dark" content="删除" placement="top">
        <el-button type="danger" text @click="removeCondition" icon="el-icon-delete"> </el-button>
      </el-tooltip>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, inject } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const isView = computed(() => !!route.query.isView)
const {
  proxy: { $api, $tool, $bus, t }
} = getCurrentInstance()

// 定义响应式数据模型
const dataForm = defineModel({
  type: Object,
  default: { conditions: [] }
})
const props = defineProps({
  isAdd: {
    type: Boolean,
    default: true
  }
})

const cascaderValue = computed({
  get() {
    if (!dataForm.value.fieldName) return []
    return dataForm.value.fieldName.split('.')
  },
  set(val) {
    dataForm.value.fieldName = val.join('.')
  }
})

const dateValue = computed({
  get() {
    // 默认值设置
    const defaultValue = { direction: '-', amount: '', unit: 'd' }

    // 基础验证
    if (!dataForm.value?.fieldValue) return defaultValue
    if (Array.isArray(dataForm.value.fieldValue)) return defaultValue

    let value = dataForm.value.fieldValue

    // 对象类型转换处理
    if (typeof value === 'object') {
      try {
        value = JSON.stringify(value)
      } catch {
        return defaultValue
      }
    }

    // 类型检查
    if (typeof value !== 'string') return defaultValue

    // monthPeriod 操作符处理
    if (dataForm.value.operator === 'monthPeriod') {
      const match = value.match(/^([+-]?)(\d+)([FL])$/)
      if (!match) return { direction: '-', amount: '', unit: 'F' }
      return {
        direction: match[1] === '-' ? '-' : '+',
        amount: parseInt(match[2]) || '',
        unit: match[3]
      }
    }

    // dateRange 操作符处理
    if (dataForm.value.operator === 'dateRange') {
      const parts = value.split(',')
      if (parts.length === 2) {
        const match = parts[0].match(/^(!?)([+-]?)(\d+)([dMy])$/)
        if (match) {
          return {
            direction: match[2] === '-' ? '-' : '+',
            amount: parseInt(match[3]) || '',
            unit: match[1] ? `!${match[4]}` : match[4]
          }
        }
      }
      return defaultValue
    }

    // bf/af 操作符处理
    const match = value.match(/^(!?)([+-]?)(\d+)([dMy])$/)
    if (!match) return defaultValue

    return {
      direction: match[2] === '-' ? '-' : '+',
      amount: parseInt(match[3]) || '',
      unit: match[1] ? `!${match[4]}` : match[4]
    }
  },
  set(val) {
    // 输入验证
    if (!val || typeof val !== 'object') {
      dataForm.value.fieldValue = null
      return
    }

    // 数值验证
    if (typeof val.amount !== 'number' && val.amount !== '') {
      dataForm.value.fieldValue = null
      return
    }

    // monthPeriod 处理
    if (dataForm.value.operator === 'monthPeriod') {
      if (!['F', 'L'].includes(val.unit)) {
        dataForm.value.fieldValue = null
        return
      }
      dataForm.value.fieldValue = `${val.direction === '-' ? '-' : ''}${val.amount}${val.unit}`
      return
    }

    // dateRange 处理
    if (dataForm.value.operator === 'dateRange') {
      if (!['d', 'M'].includes(val.unit)) {
        dataForm.value.fieldValue = null
        return
      }
      const value = `${val.unit.startsWith('!') ? '!' : ''}${val.direction === '-' ? '-' : ''}${
        val.amount
      }${val.unit.replace('!', '')}`
      dataForm.value.fieldValue = `${value},${value}`
      return
    }

    // bf/af 处理
    const validUnits = ['d', 'M', 'y', '!d', '!M', '!y']
    if (!validUnits.includes(val.unit)) {
      dataForm.value.fieldValue = null
      return
    }

    // 时间字符串处理
    if (['bf', 'af'].includes(dataForm.value.operator) && val.unit === 'd') {
      if (!dataForm.value.timeStr) {
        dataForm.value.timeStr = ''
      }
    }

    // 生成最终值
    dataForm.value.fieldValue = `${val.unit.startsWith('!') ? '!' : ''}${val.direction === '-' ? '-' : ''}${
      val.amount
    }${val.unit.replace('!', '')}`
  }
})

const options = inject('optionsRedirect')

// 客户字段数据
const consumerList = computed(() => {
  return options.value?.consumerMap?.fields || []
})

// 整合操作符
const operatorMap = {
  KEYWORD: ['empty', 'eq', 'ne', 'in', 'nin'],
  BOOLEAN: ['empty', 'eq', 'ne', 'in', 'nin'],
  TEXT: ['empty', 'like', 'not_like'],
  SHORT: ['empty', 'eq', 'ne', 'gt', 'lt', 'ge', 'le', 'in', 'nin'],
  INTEGER: ['empty', 'eq', 'ne', 'gt', 'lt', 'ge', 'le', 'in', 'nin'],
  LONG: ['empty', 'eq', 'ne', 'gt', 'lt', 'ge', 'le', 'in', 'nin'],
  DOUBLE: ['empty', 'eq', 'ne', 'gt', 'lt', 'ge', 'le', 'in', 'nin'],
  FLOAT: ['empty', 'eq', 'ne', 'gt', 'lt', 'ge', 'le', 'in', 'nin'],
  DATE: ['empty', 'eq', 'ne', 'gt', 'lt', 'ge', 'le', 'af', 'bf', 'monthIn', 'dateRange', 'monthPeriod']
}

// 操作符映射表
const operatorLabels = {
  ne: '不等于',
  eq: '等于',
  lt: '小于',
  le: '小于等于',
  ge: '大于等于',
  gt: '大于',
  empty: '是否为空',
  like: '包含',
  not_like: '不包含',
  in: '在...范围内',
  nin: '不在...范围内',
  bf: '相对时间小于',
  af: '相对时间大于等于',
  monthPeriod: '时间在',
  dateRange: '日期在',
  monthIn: '月份在'
}

// 修改 fieldConfig 计算属性
const fieldConfig = computed(() => {
  if (!dataForm.value.fieldName) return null
  const fields = dataForm.value.fieldName.split('.')
  let current = consumerList.value

  // 根据数组长度逐层查找
  for (let i = 0; i < fields.length; i++) {
    const field = current.find((x) => x.name === fields[i])
    if (!field) return null

    // 如果是最后一层，直接返回字段配置
    if (i === fields.length - 1) {
      return field
    }
    // 不是最后一层，继续往下找
    current = field.fields || []
  }
  return null
})

const handleFieldChange = (values) => {
  if (!values?.length) {
    dataForm.value.fieldType = ''
    return
  }

  let current = consumerList.value
  // 获取最后一层的字段配置
  for (let i = 0; i < values.length; i++) {
    const field = current.find((x) => x.name === values[i])
    if (!field) {
      dataForm.value.fieldType = ''
      return
    }
    // 如果是最后一层，设置字段类型
    if (i === values.length - 1) {
      dataForm.value.fieldType = field.type
      return
    }
    current = field.fields || []
  }
}

// 处理操作符变化
const handleOperatorChange = (value) => {
  // 重置值
  if (value === 'empty') {
    dataForm.value.fieldValue = null
  } else if (value === 'between') {
    // 范围值处理
    if (fieldConfig.value?.type === 'NUMBER' || fieldConfig.value?.type === 'DATE') {
      dataForm.value.fieldValue = ['', '']
    }
  } else if (['bf', 'af', 'dateRange'].includes(value)) {
    // 相对时间处理
    dataForm.value.fieldValue = '-0d' // 设置一个默认值，会通过 dateValue 的 get 方法转换
  } else if (value === 'monthPeriod') {
    // 月周期处理
    dataForm.value.fieldValue = '-0F' // 设置月上旬作为默认值
  } else if (value === 'monthIn') {
    // 月份处理
    dataForm.value.fieldValue = '1'
  } else if (value === 'in' && fieldConfig.value?.type === 'DICT') {
    // 字典多选处理
    dataForm.value.fieldValue = []
  } else {
    dataForm.value.fieldValue = null
  }
}

// 删除条件
const removeCondition = () => {
  emits('remove')
}
</script>
<style lang="scss" scoped>
.consumer-dataForm-select {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
  :deep(.field-select) {
    width: 240px;
    min-width: 240px;
  }
  .operator-select {
    width: 140px;
    min-width: 140px;
  }
  .value {
    display: flex;
    gap: 10px;
    width: 100%;
    &.dateRange,
    &.monthPeriod {
      min-width: 360px;
    }
    &.af,
    &.bf {
      min-width: 440px;
    }
  }
  .btn-group {
    display: flex;
    align-items: center;
    min-width: 45px;
  }
  .relative-date-input {
    display: flex;
    align-items: center;
    width: 500px;
    gap: 10px;
  }
  .direction-select {
    min-width: 80px;
  }
  .amount-input {
    min-width: 120px;
  }
  .unit-select {
    min-width: 80px;
  }
  :deep(.time-picker) {
    min-width: 120px;
  }
}
</style>
