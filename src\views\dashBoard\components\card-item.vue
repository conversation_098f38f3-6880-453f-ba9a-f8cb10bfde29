<template>
  <div class="statistic-card">
    <el-statistic :value="itemData.value">
      <template #title>
        <div class="top-title">
          {{ itemData.label }}
        </div>
      </template>
    </el-statistic>
    <div v-if="itemData?.percent >= 0" class="statistic-footer">
      <span>{{$t('dashboard.tongBiShangZhou')}}</span>
      <div :style="{ color: itemData.percent >= 0 ? '#BF1220' : '#00B78B' }">
        <el-icon v-if="itemData.percent >= 0" class="icon-up">
          <el-icon-top />
        </el-icon>
        <el-icon v-else class="icon-down">
          <el-icon-bottom />
        </el-icon>
        {{ itemData.percent }}%
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CardItem',
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      value: 0
    }
  },
  watch: {},
  mounted() {},
  methods: {}
}
</script>

<style scoped>
.el-statistic {
  --el-statistic-content-font-size: 28px;
}
.top-title {
  font-size: 14px;
}

.statistic-card {
  height: 100%;
  padding: 20px;
  border-radius: 4px;
  background-color: var(--el-bg-color-overlay);
}

.statistic-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 16px;
}

.statistic-footer .footer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistic-footer .footer-item span:last-child {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
}

.green {
  color: var(--el-color-success);
}
.red {
  color: var(--el-color-error);
}
</style>
