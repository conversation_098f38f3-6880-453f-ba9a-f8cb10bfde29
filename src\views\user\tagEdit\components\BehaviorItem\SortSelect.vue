<template>
  <div class="sort-dataForm-select">
    <el-cascader
      v-model="cascaderValue"
      :options="modelList"
      :disabled="modelList.length == 0"
      :props="{
        value: 'name',
        label: 'aliasName',
        children: 'fields'
      }"
      :placeholder="$t('userTagEdit.xuanZeZiDuan')"
      filterable
      class="field-select" />

    <el-select
      v-model="dataForm.direction"
      :placeholder="$t('userTagEdit.paiXuFangShi')"
      class="value-input"
      @change="rangeTypeChange"
      :disabled="modelList.length == 0">
      <el-option :label="$t('userTagEdit.zhengXu')" value="ASC"></el-option>
      <el-option :label="$t('userTagEdit.daoXu')" value="DESC"></el-option>
    </el-select>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, inject, watch } from 'vue'

const {
  proxy: { $api, $tool, $bus, t }
} = getCurrentInstance()

// 定义响应式数据模型
const dataForm = defineModel({
  type: Object,
  default: { conditions: [] }
})
const props = defineProps({
  dataPersistentModelId: {
    type: String,
    default: ''
  }
})

const cascaderValue = computed({
  get() {
    if (!dataForm.value.fieldName) return []
    // 将 "parent.child" 格式转换为数组
    return dataForm.value.fieldName.split('.')
  },
  set(val) {
    // 将数组转换为 "parent.child" 格式
    dataForm.value.fieldName = val.join('.')
  }
})

const options = inject('optionsPersistent')

// 行为字段数据
const behaviorList = computed(() => {
  if (!options.value?.behaviorList) return []
  return options.value.behaviorList
})
const modelList = computed(() => {
  let item = behaviorList.value.find((x) => x.id == props.dataPersistentModelId)
  return item?.fields || []
})
</script>
<style lang="scss" scoped>
.sort-dataForm-select {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
  padding-right: 185px;
}
</style>
