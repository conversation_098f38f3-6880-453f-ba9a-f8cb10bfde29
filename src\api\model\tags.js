import config from '@/config'
import request from '@/utils/request'

export default {
  // 分页
  page: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag_category/{collectionId}`
    return await request.get(url, params)
  },
  // 列表
  list: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag_category/{collectionId}/list`
    return await request.get(url, params)
  },
  // 详情
  info: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag_category/{collectionId}/${params.id}`
    return await request.get(url)
  },
  // 修改
  edit: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/tag_category/{collectionId}`
    return await request.put(url, data)
  },
  // 新增
  add: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/tag_category/{collectionId}`
    return await request.post(url, data)
  },
  // 删除
  delete: async (id) => {
    let url = `${config.API_URL}/cdp-portal/tag_category/{collectionId}/${id}`
    return await request.delete(url)
  }
}
