<template>
  <el-container>
    <el-main>
      <lwFormMini ref="dataFormRef" :config="config" :isView="isView" v-model="dataForm" :loading="loading">
        <template #cronSelect>
          <span v-if="dataForm?.executeType == 'manual'">
            <span>手动触发</span>
          </span>
          <lwCronSelect v-else v-model="dataForm.timingWheelPeriodModel.expression" :isView="isView" />
        </template>
        <template #taskTypeSelect>
          <el-radio-group v-model="dataForm.taskType" :disabled="id || isView">
            <el-radio value="cdp_data_replay" size="large" border>{{ $t('taskDeployEdit.shuJuZhongFang') }}</el-radio>
            <el-radio value="cdp_tag_calculate" size="large" border>{{ $t('taskDeployEdit.biaoQianJiSuan') }}</el-radio>
            <el-radio value="cdp_data_extractor" size="large" border>{{ $t('taskDeployEdit.shuJuChouQu') }}</el-radio>
            <el-radio value="cdp_tag_snapshot" size="large" border>{{
              $t('taskDeployEdit.biaoQianKuaiZhao')
            }}</el-radio>
            <el-radio value="cdp_audience_snapshot" size="large" border>{{
              $t('taskDeployEdit.renQunKuaiZhao')
            }}</el-radio>
          </el-radio-group>
        </template>
        <template #updateUserSelect>
          <el-radio-group v-model="dataForm.replayType" :disabled="id || isView">
            <el-radio value="routing" size="large" border>{{ $t('taskDeployEdit.luYou') }}</el-radio>
            <el-radio value="subscribe" size="large" border>{{ $t('taskDeployEdit.dingYue') }}</el-radio>
          </el-radio-group>
        </template>
      </lwFormMini>
    </el-main>
    <el-footer class="footer-body">
      <el-button @click="close">{{ $t('btn.close') }}</el-button>
      <el-button v-if="!isView" type="primary" @click="save">{{ $t('btn.save') }}</el-button>
    </el-footer>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
