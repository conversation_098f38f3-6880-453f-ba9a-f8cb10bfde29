import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'collectFile',
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        enableUploadFile_eq: false
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 修改状态
    const changeNeedPersistent = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.collectFile.edit({
        ...item,
        needPersistent: value
      })
    }
    const changeNeedLogging = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.collectFile.edit({
        ...item,
        needLogging: value
      })
    }

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('collectFile.bianMa'),
          prop: 'name_like',
          renderType: 'input'
        },
        {
          label: t('collectFile.mingCheng'),
          prop: 'aliasName_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('collectFile.bianMa'), dataIndex: 'name', minWidth: '120', copy: true, tooltip: true },
        { title: t('collectFile.mingCheng'), dataIndex: 'aliasName', minWidth: '160' },
        { title: t('collectFile.chuanShuMoXing'), dataIndex: 'transferModelName', width: '160' },
        { title: t('collectFile.shiFouCunChu'), dataIndex: 'needPersistent', width: '160', switch: true, clickFun: changeNeedPersistent },
        { title: t('collectFile.cunChuMoXing'), dataIndex: 'persistentModelName', minWidth: '160' },
        { title: t('collectFile.shiFouRiZhi'), dataIndex: 'needLogging', width: '160', switch: true, clickFun: changeNeedLogging }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '180',
        fixed: 'right',
        ellipsis: true,
        operation: [
          { clickFun: (item) => edit(item, 'view'), label: t('btn.view') },
          { clickFun: (item) => edit(item, 'edit'), label: t('btn.edit') },
          { clickFun: (item) => edit(item, 'down'), label: t('collectFile.xiaZai') },
          { clickFun: del, label: t('btn.delete') }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.collectFile
        .page(params)
        .then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = {}
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 确认导出
    const confirmExport = async (item) => {
      let res = await $api.collectFile.export(item.transferModelName)
      if (res) {
        const link = document.createElement('a')
        link.href = res
        link.download = `${item.aliasName}.csv`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        ElMessage({ type: 'error', message: t('collectFile.DCSBQSHZS') })
      }
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      if (type == 'down') {
        confirmExport(item)
        return false
      }

      let query = {}
      if (type === 'view') {
        query = { id: item.id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/collect/fileEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.collectFile.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      edit
    }
  }
}
