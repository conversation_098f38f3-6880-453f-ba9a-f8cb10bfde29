import lwFilterList from '@/components/lwFilterList/index.vue'
import lwMappingEdit from '@/components/lwMappingEdit/index.vue'
import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, provide, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'subscribeApiEdit',
  components: {
    lwMappingEdit,
    lwFilterList
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const dataForm = ref({
      name: '',
      aliasName: '',
      targetType: 'interface',
      mappingList: []
    })
    const dataFormRef = ref(null)
    const mappingEditRef = ref(null)
    const loading = ref(false)

    // 存储模型
    const persistentlList = ref([])
    const getPersistentList = async () => {
      let res = await $api.modelPersistent.list()
      persistentlList.value = res.map((item) => ({ label: item.aliasName, value: item.id, fields: item.fields }))
    }

    // 传输模型
    const transmissionList = ref([])
    const getTransmissionList = async () => {
      let res = await $api.modelTransmission.list()
      transmissionList.value = res.map((item) => ({ label: item.aliasName, value: item.id, fields: item.fields }))
    }

    const subscribeList = computed(() => {
      return dataForm.value.subscribeModelType == 'transfer' ? transmissionList.value : persistentlList.value
    })

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('subscribeApiEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('subscribeApiEdit.mingCheng'),
            name: 'aliasName',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('subscribeApiEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeApiEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('subscribeApiEdit.bianMa'),
            name: 'name',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('subscribeApiEdit.qingShuRu')
            },
            tips: t('subscribeApiEdit.ZNWXXZMSZHXH'),
            rules: [
              { required: true, message: t('subscribeApiEdit.qingShuRu'), trigger: 'blur' },
              { pattern: /^[a-z0-9_]+$/, message: t('subscribeApiEdit.ZNWXXZMSZHXH'), trigger: 'blur' },
              { max: 20, message: t('subscribeApiEdit.CDBNCGGZF'), trigger: 'blur' }
            ]
          },
          {
            label: t('subscribeApiEdit.wanZhengLuJing'),
            name: 'fullPath',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('subscribeApiEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeApiEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('subscribeApiEdit.chuanShuMoXing'),
            name: 'transferModelId',
            value: '',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('subscribeApiEdit.qingXuanZe'),
              items: transmissionList.value
            },
            tips: t('subscribeApiEdit.KXZCSMXZYJLD'),
            rules: [{ required: true, message: t('subscribeApiEdit.qingXuanZe'), trigger: 'blur' }]
          },

          {
            label: t('subscribeApiEdit.shuJuTuiSongMoXing'),
            name: 'subscribeModelType',
            value: 'persistent',
            span: 8,
            component: 'radio',
            options: {
              items: [
                { label: t('subscribeApiEdit.chuanShuMoXing'), value: 'transfer' },
                { label: t('subscribeApiEdit.cunChuMoXing'), value: 'persistent' }
              ]
            }
          },

          {
            label: t('subscribeApiEdit.dingYueMoXing'),
            name: 'subscribeModelId',
            value: '',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('subscribeApiEdit.qingXuanZe'),
              items: subscribeList.value
            },
            rules: [{ required: true, message: t('subscribeApiEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            name: 'dataFilter.dataFilterConditionList',
            value: [],
            span: 24,
            component: 'optionsTable'
          },
          {
            label: t('subscribeApiEdit.zhuangTaiMa'),
            name: 'successHttpCode',
            value: '',
            span: 4,
            component: 'input',
            options: {
              placeholder: t('subscribeApiEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeApiEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('subscribeApiEdit.shiFouJiLuRiZhi'),
            name: 'needLogging',
            value: true,
            span: 4,
            component: 'switch',
            options: {
              activeText: t('subscribeApiEdit.jiLu'),
              inactiveText: t('subscribeApiEdit.buJiLu')
            },
            tips: t('subscribeApiEdit.SFXYJLRZKQHJ')
          },
          {
            label: t('subscribeApiEdit.shiFouXuYaoChongShi'),
            name: 'needRetry',
            value: false,
            span: 4,
            component: 'switch',
            options: {
              activeText: t('subscribeApiEdit.xuYao'),
              inactiveText: t('subscribeApiEdit.buXuYao')
            }
          },
          {
            label: t('subscribeApiEdit.zuiDaChongShiCiShu'),
            name: 'maxRetryTimes',
            value: 0,
            span: 4,
            component: 'number',
            options: {
              min: 0
            },
            hideHandle: '!$.needRetry'
          },
          {
            label: t('subscribeApiEdit.chongShiShiJianJianGe'),
            name: 'retryDelaySeconds',
            value: 0,
            span: 4,
            component: 'number',
            options: {
              min: 0,
              suffix: t('subscribeApiEdit.miao')
            },
            hideHandle: '!$.needRetry'
          },

          {
            label: t('subscribeApiEdit.beiZhu'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              type: 'textarea',
              placeholder: t('subscribeApiEdit.qingShuRu')
            }
          },
          { label: t('subscribeApiEdit.yingShePeiZhi'), component: 'divider' },
          { component: 'mappingEdit' }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.subscribeApi.info({ id: id.value })
      dataForm.value = res
    }

    // 用户模型
    const optionsRedirect = ref({
      consumerMap: {},
      typeGroup: '',
      behaviorList: []
    })

    watch(
      () => dataForm.value.subscribeModelId,
      (val) => {
        optionsRedirect.value.consumerMap = subscribeList.value.find((x) => x.value == val) || []
      }
    )

    // 传参
    provide('optionsRedirect', optionsRedirect)

    onMounted(() => {
      loading.value = true
      Promise.all([getPersistentList(), getTransmissionList()])
        .then(() => {
          if (id.value) {
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          await $api.subscribeApi[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('subscribeApiEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/subscribe/api', 'subscribeApi')
    }

    return {
      dataForm,
      mappingEditRef,
      dataFormRef,
      config,
      loading,
      isView,
      save,
      close
    }
  }
}
