<template>
  <div class="behavior-item">
    <div class="relation-left" v-if="dataForm.conditions.length > 1">
      <div class="line">
        <el-button class="relation" type="primary" plain size="small" :disabled="isView" @click="toggleRelation">
          {{ dataForm.relation === 'or' ? $t('userTagEdit.huo') : $t('userTagEdit.qie') }}
        </el-button>
        <div class="line-horizontal-t"></div>
        <div class="line-horizontal-b"></div>
      </div>
    </div>
    <el-form class="behavior-body" :disabled="isView">
      <template v-for="(item, index) in dataForm.conditions" :key="item?.id">
        <template v-if="item.conditions.length > 0 && isParentAnd(item)">
          <BehaviorItem v-model="dataForm.conditions[index]" />
        </template>
        <template v-else>
          <FieldSelect
            v-model="dataForm.conditions[index]"
            :isAdd="index == 0 && !isTop"
            @add="handleAdd(index)"
            @remove="handleRemove(index)" />
          <!-- 首条条件 -->
          <div v-if="item.rangeType == 'head'" class="and-item-list m10">
            <div class="and-left">{{ $t('userTagEdit.paiXuZiDuan') }}</div>
            <div class="and-item">
              <SortSelect
                v-for="(and, andIndex) in item.sortOrderList"
                :key="and.id"
                :dataPersistentModelId="item.dataPersistentModelId"
                v-model="dataForm.conditions[index].sortOrderList[andIndex]" />
            </div>
          </div>

          <!-- 数据映射 -->
          <div v-if="item.rangeType == 'head'" class="and-item-list m10">
            <div class="and-left">
              <el-tooltip :content="$t('userTagEdit.xinZengYingShe')" placement="top">
                <el-button type="primary" link @click="addMappingItem(index)" icon="el-icon-plus" size="small">
                  {{ $t('userTagEdit.shuJuYingShe') }}
                </el-button>
              </el-tooltip>
            </div>
            <div class="and-item">
              <DataMapping
                v-for="(and, andIndex) in item.rangeDataList"
                :key="and.id"
                :dataPersistentModelId="item.dataPersistentModelId"
                v-model="dataForm.conditions[index].rangeDataList[andIndex]"
                @remove="removeMappingItem(index, andIndex)" />
              <el-alert
                v-if="item.rangeDataList.length == 0"
                title="{{$t('userTagEdit.ZWSJYSQDJZCT')}}"
                type="info"
                class="info-tips" />
            </div>
          </div>

          <!-- 并且满足 -->
          <div v-if="item.conditions.length > 0" class="and-item-list">
            <div class="and-left">{{ $t('userTagEdit.bingQieManZu') }}</div>
            <div class="and-item">
              <FieldSelect
                v-for="(and, andIndex) in item.conditions"
                :key="and.id"
                v-model="dataForm.conditions[index].conditions[andIndex]"
                :isAdd="index == 0 && !isTop"
                :isAnd="true"
                @remove="removeAddItem(index, andIndex)" />
            </div>
          </div>
        </template>
      </template>
    </el-form>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, inject, onBeforeMount, onBeforeUnmount } from 'vue'
import FieldSelect from './FieldSelect.vue'
import SortSelect from './SortSelect.vue'
import DataMapping from './DataMapping.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const isView = computed(() => !!route.query.isView)
defineOptions({
  name: 'BehaviorItem'
})
const {
  proxy: { $api, $tool, $bus, t }
} = getCurrentInstance()

// 定义响应式数据模型
const dataForm = defineModel({
  type: Object,
  default: { conditions: [] }
})
const props = defineProps({
  isTop: {
    type: Boolean,
    default: false
  }
})

const toggleRelation = (item) => {
  dataForm.value.relation = dataForm.value.relation === 'or' ? 'and' : 'or'
}

// 处理添加条件
const handleAdd = (index) => {
  if (index > -1) {
    dataForm.value.conditions.splice(index + 1, 0, {
      dataPersistentModelId: dataForm.value.conditions[index].dataPersistentModelId || '',
      fieldName: '',
      fieldType: '',
      aggregation: '',
      aggregationList: [],
      operator: '',
      value: '',
      valueSource: 'fixed_value',
      consumerFieldName: '',
      targetFieldName: '',
      rangeType: 'default',
      rangeFieldName: '',
      relation: 'and',
      timeStr: '',
      conditions: [],
      id: $tool.getUUID()
    })
  }
}

// 处理删除条件
const handleRemove = (index) => {
  if (index > -1) {
    dataForm.value.conditions.splice(index, 1)

    // 判断是不是剩下最后一个
    if (dataForm.value.conditions.length === 1 && !props.isTop) {
      // 获取要删除的条件
      const condition = dataForm.value.conditions[0]

      // 如果该条件有字段配置，就将其移动到上一级
      if (condition.fieldName || condition.operator || condition.value !== undefined) {
        // 将当前条件的值复制到父级
        Object.keys(condition).forEach((key) => {
          if (key !== 'conditions') {
            dataForm.value[key] = condition[key]
          }
        })
        // 清空conditions数组
        dataForm.value.conditions = []
      } else {
        // 如果没有字段配置，直接删除
        dataForm.value.conditions.splice(index, 1)
      }
    } else {
    }
  }
}

// 删除并且条件
const removeAddItem = (index, andIndex) => {
  dataForm.value.conditions[index].conditions.splice(andIndex, 1)
}
// 删除映射
const removeMappingItem = (index, andIndex) => {
  dataForm.value.conditions[index].rangeDataList.splice(andIndex, 1)
}

const addMappingItem = (index) => {
  dataForm.value.conditions[index].rangeDataList.push({ destFieldName: '', sourceFieldName: '' })
}

// 判断子级是不是父级的并且条件
const isParentAnd = (item) => {
  let list = item.conditions.filter((x) => x.dataPersistentModelId != item.dataPersistentModelId)
  return list.length > 0
}
</script>
<style lang="scss" scoped>
.behavior-item {
  display: flex;
  .relation-left {
    position: relative;
    width: 20px;
    min-width: 20px;
    margin: 0 10px 0 15px;

    .line {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 2px;
      background-color: var(--el-color-primary-light-5);
      display: flex;
      align-items: center;
      justify-content: center;

      .relation {
        position: absolute;
        z-index: 1;
      }

      .line-horizontal-t,
      .line-horizontal-b {
        position: absolute;
        width: 20px;
        height: 2px;
        background-color: var(--el-color-primary-light-5);
        left: 0;
      }

      .line-horizontal-t {
        top: 0;
      }

      .line-horizontal-b {
        bottom: 0;
      }
    }
  }
  .behavior-body {
    width: 100%;
  }
  .and-item-list {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    &.m10 {
      margin-bottom: 10px;
    }
    .and-item {
      width: 100%;
    }
    .and-left {
      min-width: calc((100% - 745px) / 2);
      width: calc((100% - 745px) / 2);
      padding-right: 10px;
      margin-right: 10px;
      text-align: right;
      font-size: 12px;
      font-weight: bold;
      color: var(--el-color-primary-light-2);
      border-right: 2px solid var(--el-color-primary-light-5);
      position: relative;
      &::after {
        content: '';
        height: 2px;
        width: 10px;
        position: absolute;
        bottom: 0;
        right: -10px;
        background-color: var(--el-color-primary-light-5);
      }
      &::before {
        content: '';
        height: 2px;
        width: 10px;
        position: absolute;
        top: 0;
        right: -10px;
        background-color: var(--el-color-primary-light-5);
      }
    }
    .info-tips {
      width: calc(100% - 185px);
      span {
        font-size: 12px;
      }
    }
  }
}
</style>
