import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export default {
  name: 'userPortrait',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        cdp_latest_version_Q_eq: true
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 获取显示字段
    const fieldList = ref([])
    const getFieldList = async () => {
      let list = await $api.modelPersistent.list()
      let res = list.find((item) => item.dataPersistentInfo.persistentType === 'consumer')?.fields

      // 搜索条件&表头
      state.searchOptions = []
      state.tableHeaders = []

      function getFields(list, name) {
        list?.forEach((item) => {
          let aliasName = item.aliasName.split('.')?.[1] || item.aliasName
          if (item.visualSetting.useForSearch) {
            let config = {
              label: aliasName,
              prop: `${item.fieldName}_Q_${item.visualSetting.operator}`,
              renderType: item.type.toLowerCase() === 'date' ? 'dateRange' : 'input'
            }
            if (item.type.toLowerCase() === 'date') {
              config.prop = `${item.fieldNamename}_Q_ge_lt`
              config.span = 12
              config.startPlaceholder = `${aliasName}${t('userPortrait.kaiShiShiJian')}`
              config.endPlaceholder = `${aliasName}${t('userPortrait.jieShuShiJian')}`
            }
            state.searchOptions.push(config)
          }
          if (item.fields && item.fields.length > 0) {
            getFields(item.fields, item.name)
          } else {
            state.tableHeaders.push({
              title: aliasName,
              dataIndex: name ? `${name}.${item.name}` : item.name,
              align: 'left',
              minWidth: item.type == 'DATE' ? '180' : 60 + aliasName.length * 12,
              tooltip: true,
              date: item.type == 'DATE'
            })
          }
        })
      }
      getFields(res)

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '80',
        fixed: 'right',
        ellipsis: true,
        operation: [{ clickFun: (item) => edit(item, 'view'), label: t('btn.view') }]
      })
    }

    onMounted(() => {
      state.loading = true
      Promise.all([getFieldList()])
        .then(() => {
          getTableData()
        })
        .finally(() => {
          state.loading = false
        })
    })

    const getTableData = (page = 0) => {
      let params = {
        page: page,
        size: state.searchParams.size,
        expression: $expression(state.searchParams)
      }
      $api.userPortrait.page(params).then((res) => {
        state.tableData = res.content
        state.currentPage = page
        state.totalCount = res.totalElements
      })
    }

    const reset = () => {
      state.searchParams = { cdp_latest_version_Q_eq: true }
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.cdp_id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/user/portraitEdit', query })
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      search,
      edit
    }
  }
}
