export default {
  collectApiEdit: {
    jiBenXinXi: '基本信息',
    jieKouMingCheng: '接口名称',
    qingShuRu: '请输入',
    jieKouBianMa: '接口编码',
    ZNWXXZMSZHXH: '只能为小写字母、数字或下划线',
    CDBNCGGZF: '长度不能超过20个字符',
    jieKouShiQu: '接口时区',
    qingXuanZe: '请选择',
    chuanShuMoXing: '传输模型',
    KXZCSMXZYJLD: '可选择传输模型中已建立的数据表',
    shiFouJiLuRiZhi: '是否记录日志',
    jiLu: '记录',
    buJiLu: '不记录',
    SFXYJLRZKQHJ: '是否需要记录日志，开启后记录日志',
    shuJuShiFouCunChu: '数据是否存储',
    cunChu: '存储',
    buCunChu: '不存储',
    KQHXXZCCMXHZ: '开启后需选择存储模型汇总已建立的数据表',
    cunChuMoXing: '存储模型',
    KXZCCMXZYJLD: '可选择存储模型中已建立的数据表',
    beiZhu: '备注',
    yingShePeiZhi: '映射配置',
    baoCunChengGong: '保存成功'
  }
}
