import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, getCurrentInstance, onMounted, provide, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import TagEditItem from './components/TagEditItem.vue'

export default {
  name: 'userTagEdit',
  components: {
    TagEditItem
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const tagId = computed(() => route.query.tagId)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    // 获取类型列表
    const typeList = ref([])
    function buildTree(data, parentId = '0') {
      return data
        .filter((item) => item.parent === parentId)
        .map((item) => ({
          ...item,
          children: buildTree(data, item.id)
        }))
    }
    const getTypeList = async () => {
      let res = await $api.tags.list({ expression: 'delete ne true' })
      typeList.value = buildTree(res)
    }

    // 数据角色
    const BAR_DATA = $tool.data.get('BAR_DATA') || {}
    const roleList = ref([{ label: t('userTagEdit.moRen'), value: '0' }, ...BAR_DATA?.dataRoles])

    const config = computed(() => {
      return {
        labelWidth: '100px',
        labelPosition: isView.value ? 'right' : 'top',
        size: 'default',
        formItems: [
          { label: t('userTagEdit.jiChuXinXi'), component: 'divider' },
          {
            label: t('userTagEdit.biaoQianMingCheng'),
            name: 'name',
            value: '',
            span: 6,
            component: 'input',
            options: {
              maxlength: 20,
              placeholder: t('userTagEdit.qingShuRu')
            },
            rules: [
              { required: true, message: t('userTagEdit.qingShuRu'), trigger: 'blur' },
              { pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/, message: t('userTagEdit.ZNSRZWZMSZHX'), trigger: 'blur' }
            ]
          },
          {
            label: t('userTagEdit.biaoQianFenLei'),
            name: 'tagCategory',
            value: '',
            span: 6,
            component: 'treeSelect',
            options: {
              placeholder: t('userTagEdit.qingXuanZe'),
              props: { value: 'id', label: 'name' },
              items: typeList.value
            },
            rules: [{ required: true, message: t('userTagEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('userTagEdit.biaoQianLeiXing'),
            name: 'type',
            value: '',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('userTagEdit.qingXuanZe'),
              disabled: !!id.value,
              items: [
                { label: t('userTagEdit.guiZeBiaoQian'), value: 'enumeration' },
                { label: t('userTagEdit.xingWeiBiaoQian'), value: 'numerical' },
                { label: t('userTagEdit.shouGongBiaoQian'), value: 'hand' }
              ]
            },
            rules: [{ required: true, message: t('userTagEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('userTagEdit.shouGongBiaoQianLeiXing'),
            name: 'handType',
            value: '',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('userTagEdit.qingXuanZe'),
              items: [
                { label: t('userTagEdit.guDingZhi'), value: 'fixed_value' },
                { label: t('userTagEdit.duoZhi'), value: 'multi_value' }
              ]
            },
            rules: [{ required: true, message: t('userTagEdit.qingXuanZe'), trigger: 'blur' }],
            hideHandle: '$.type !== "hand"'
          },
          {
            label: t('userTagEdit.shuJuJueSe'),
            name: 'dataRoles',
            value: [],
            span: 6,
            component: 'select',
            options: {
              placeholder: t('userTagEdit.qingXuanZe'),
              multiple: true,
              items: roleList.value
            }
          },
          {
            label: t('userTagEdit.biaoQianMiaoShu'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              maxlength: 200,
              type: 'textarea',
              placeholder: t('userTagEdit.qingShuRu')
            }
          },
          { label: t('userTagEdit.biaoQianDingYi'), component: 'divider', options: { component: 'tagBtns' } },
          { component: 'tagList' }
        ]
      }
    })

    // 查询详情
    const dataForm = ref({ dataRoles: ['0'] })
    const dataFormRef = ref(null)
    const loading = ref(false)
    const fetchDetail = async () => {
      let res = await $api.userTag.infoGroup({ id: id.value })

      // 复制 去掉部分字段
      if (copy.value) {
        delete res.id
        res.name = `${res.name} {{$t('userTagEdit.fuZhi')}}`
      }

      dataForm.value = res
    }

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          let res = await $api.userTag[id.value && !copy.value ? 'editGroup' : 'addGroup'](params)

          // 循环保存标签
          let conditionsFun = editableTags.value.map(async (x) => {
            let query = {
              ...x,
              fullName: `${dataForm.value.name}_${x.name}`,
              mode: editType.value[editableTagsValue.value]
            }

            if (x?.isAdd) {
              query.tagGroup = res.id
            }
            await $api.userTag[!x.isAdd ? 'edit' : 'add'](query)
          })
          await Promise.all(conditionsFun)

          ElMessage({ type: 'success', message: t('userTagEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/user/tag', 'userTag')
    }

    // 用户模型
    const optionsPersistent = ref({
      consumerMap: {},
      typeGroup: '',
      dictList: [],
      behaviorList: []
    })

    const getConsumer = async () => {
      optionsPersistent.value.consumerMap = await $api.modelPersistent.consumer()
    }
    const getBehavior = async () => {
      optionsPersistent.value.behaviorList = await $api.modelPersistent.behavior()
    }

    // 传参
    provide('optionsPersistent', optionsPersistent)

    // 标签
    const editType = ref({})
    const addTags = () => {
      let tenantId = $tool.data.get('tenantId')
      let BAR_DATA = $tool.data.get('BAR_DATA')
      let id = $tool.getUUID()
      let item = {
        id: id,
        name: '',
        code: '',
        mode: 'normal',
        description: '',
        isAdd: true,
        tagGroup: id.value,
        tagType: 'auto',
        targetType: dataForm.value.type,
        tenantId,
        collectionId: BAR_DATA.collectionId,
        conditionRelation: 'and',
        relation: 'and',
        conditions: []
      }

      if (dataForm.value.type == 'hand') {
        item.tagType = dataForm.value.type
        item.targetType = 'enumeration'
      }

      if (dataForm.value.type != 'hand') {
        item.conditions = [
          //用户数据
          {
            dataPersistentModelId: optionsPersistent.value.consumerMap.id,
            aggregation: '',
            aggregationList: [],
            valueSource: 'fixed_value',
            rangeType: 'default',
            relation: 'and',
            conditions: [],
            aggregationKey: '',
            timeStr: '',
            id: $tool.getUUID()
          },
          //行为数据
          {
            dataPersistentModelId: '',
            aggregation: '',
            aggregationList: [],
            valueSource: 'fixed_value',
            rangeType: 'default',
            relation: 'and',
            conditions: [],
            aggregationKey: '',
            timeStr: '',
            sortOrderList: [],
            id: $tool.getUUID()
          }
        ]
      }

      editType.value[id] = 'normal'
      editableTags.value.push(item)
      editableTagsValue.value = item.id
    }
    const editableTagsValue = ref('')
    const editableTags = ref([])
    const getTagList = async () => {
      if (isView.value) {
        let res = await $api.userTag.info({ id: tagId.value })
        editableTags.value = [res]
      } else {
        editableTags.value = await $api.userTag.tagList({ expression: `tagGroup eq ${id.value} AND delete ne true` })
      }

      editType.value = editableTags.value.reduce((pre, cur) => {
        pre[cur.id] = cur.mode
        return pre
      }, {})

      editableTagsValue.value = editableTags.value?.[0]?.id || ''

      // 如果没有数据 创建一条
      if (editableTags.value.length == 0) {
        addTags()
      }
    }
    const removeTag = async (id) => {
      ElMessageBox.confirm(t('userTagEdit.QDSCDQBQM'), t('userTagEdit.tiShi'), {
        confirmButtonText: t('userTagEdit.queDing'),
        cancelButtonText: t('userTagEdit.quXiao'),
        type: 'warning'
      }).then(async () => {
        let index = editableTags.value.findIndex((x) => x.id == id)
        if (index > -1) {
          editableTags.value.splice(index, 1)
        } else {
          await $api.userTag.delete(id)
        }
        ElMessage({ type: 'success', message: t('userTagEdit.shanChuChengGong') })
        getTagList()
      })
    }

    watch(
      () => dataForm.value.type,
      (val) => {
        optionsPersistent.value.typeGroup = val

        // 如果是新增就创建数据
        if (!id.value && val) {
          editableTags.value = []
          addTags()
        }
      },
      { immediate: true, deep: true }
    )

    // 字典
    const getDict = async () => {
      optionsPersistent.value.dictList = await $api.dict.list({
        expression: `tenantId eq ${$tool.data.get('tenantId')}`
      })
    }

    onMounted(() => {
      loading.value = true
      Promise.all([getDict(), getTypeList()])
        .then(() => {
          getBehavior()
          getConsumer()
          if (id.value) {
            getTagList()
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    // 是否显示专家模式
    const isTabAdd = computed(() => {
      let index = editableTags.value.findIndex((x) => x.id == editableTagsValue.value)
      return editableTags.value[index]?.isAdd
    })

    return {
      dataForm,
      dataFormRef,
      config,
      loading,
      isView,
      id,
      save,
      close,
      // 标签
      editType,
      isTabAdd,
      addTags,
      editableTagsValue,
      editableTags,
      getTagList,
      removeTag
    }
  }
}
