import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'collectApi',
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        enableUploadFile_eq: false
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 修改状态
    const changeNeedPersistent = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.collectApi.edit({
        ...item,
        needPersistent: value
      })
    }
    const changeNeedLogging = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.collectApi.edit({
        ...item,
        needLogging: value
      })
    }

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('collectApi.bianMa'),
          prop: 'name_like',
          renderType: 'input'
        },
        {
          label: t('collectApi.mingCheng'),
          prop: 'aliasName_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('collectApi.bianMa'), dataIndex: 'name', minWidth: '120', copy: true, tooltip: true },
        { title: t('collectApi.mingCheng'), dataIndex: 'aliasName', minWidth: '160' },
        { title: t('collectApi.chuanShuMoXing'), dataIndex: 'transferModelName', width: '160' },
        { title: t('collectApi.shiFouCunChu'), dataIndex: 'needPersistent', width: '160', switch: true, clickFun: changeNeedPersistent },
        { title: t('collectApi.cunChuMoXing'), dataIndex: 'persistentModelName', minWidth: '160' },
        { title: t('collectApi.shiFouRiZhi'), dataIndex: 'needLogging', width: '160', switch: true, clickFun: changeNeedLogging }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '180',
        fixed: 'right',
        ellipsis: true,
        operation: [
          { clickFun: (item) => edit(item, 'view'), label: t('btn.view') },
          { clickFun: (item) => edit(item, 'edit'), label: t('btn.edit') },
          { clickFun: del, label: t('btn.delete') }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.collectApi
        .page(params)
        .then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = {}
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/collect/apiEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.collectApi.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      edit
    }
  }
}
