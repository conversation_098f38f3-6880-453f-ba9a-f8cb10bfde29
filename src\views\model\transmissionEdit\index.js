import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import lwFieldsEdit from './components/lwFieldsEdit/index.vue'

export default {
  name: 'modelTransmissionEdit',
  components: {
    lwFieldsEdit
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const dataForm = ref({
      name: '',
      aliasName: '',
      fields: []
    })
    const dataFormRef = ref(null)
    const loading = ref(false)

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('modelTransmissionEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('modelTransmissionEdit.moXingMingCheng'),
            name: 'name',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelTransmissionEdit.qingShuRu'),
              disabled: !!id.value
            },
            rules: [{ required: true, message: t('modelTransmissionEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelTransmissionEdit.moXingZhongWen'),
            name: 'aliasName',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelTransmissionEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('modelTransmissionEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelTransmissionEdit.miaoShuXinXi'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              type: 'textarea',
              placeholder: t('modelTransmissionEdit.qingShuRu')
            }
          },
          { label: t('modelTransmissionEdit.gouJianMoXing'), component: 'divider' },
          { component: 'lwFieldsEdit' }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.modelTransmission.info({ id: id.value })
      dataForm.value = res
    }

    onMounted(() => {
      if (id.value) {
        fetchDetail()
      }
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          await $api.modelTransmission[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('modelTransmissionEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/model/transmission', 'modelTransmission')
    }

    return {
      dataForm,
      dataFormRef,
      config,
      loading,
      isView,
      save,
      close
    }
  }
}
