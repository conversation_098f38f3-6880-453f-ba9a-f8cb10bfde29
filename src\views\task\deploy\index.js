import parseCronExpression from '@/utils/cron.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'taskDeploy',
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('taskDeploy.mingCheng'),
          prop: 'name_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        {
          title: t('taskDeploy.mingCheng'),
          dataIndex: 'name',
          minWidth: '120',
          copy: true,
          tooltip: true,
          align: 'left'
        },
        { title: t('taskDeploy.leiXing'), dataIndex: 'taskType', minWidth: '160' },
        { title: t('taskDeploy.kaiShiShiJian'), dataIndex: 'startTime', width: '180', date: true },
        { title: t('taskDeploy.jieShuShiJian'), dataIndex: 'endTime', width: '180', date: true },
        {
          title: t('taskDeploy.pinLü'),
          dataIndex: 'expression',
          width: '220',
          tooltip: true,
          align: 'left',
          eleRender: (item) => {
            return parseCronExpression(item.expression)
          }
        }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '140',
        fixed: 'right',
        ellipsis: true,
        operation: [
          { clickFun: (item) => edit(item, 'edit'), label: t('btn.edit'), auth: ['cdp.task_deploy_edit'] },
          { clickFun: del, label: t('btn.delete'), auth: ['cdp.task_deploy.delete'] }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.taskDeploy
        .page(params)
        .then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = {}
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 修改状态
    const changeStatus = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.taskDeploy.edit({
        ...item,
        status: value
      })
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/task/deployEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.taskDeploy.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      edit
    }
  }
}
