import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import lwBehaviorEdit from './components/lwBehaviorEdit/index.vue'
import lwFieldsEdit from './components/lwFieldsEdit/index.vue'

export default {
  name: 'modelPortraitEdit',
  components: {
    lwFieldsEdit,
    lwBehaviorEdit
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    let portraitType = {
      consumer: t('modelPortraitEdit.keHu'),
      behavior: t('modelPortraitEdit.xingWei'),
      normal: t('modelPortraitEdit.puTong')
    }
    const dataForm = ref({
      name: '',
      behaviorSetting: [],
      consumerSetting: { fields: [] }
    })
    const dataFormRef = ref(null)
    const behaviorSettingList = ref([])
    const loading = ref(false)

    // 获取数据角色
    const BAR_DATA = $tool.data.get('BAR_DATA') || {}
    const roleList = ref(BAR_DATA.dataRoles || [])

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('modelPortraitEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('modelPortraitEdit.huaXiangMingCheng'),
            name: 'name',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('modelPortraitEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('modelPortraitEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPortraitEdit.huaXiangBianMa'),
            name: 'code',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('modelPortraitEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('modelPortraitEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPortraitEdit.shuJuJueSe'),
            name: 'dataRoles',
            value: [],
            span: 8,
            component: 'cascader',
            options: {
              placeholder: t('modelPortraitEdit.qingXuanZe'),
              props: {
                children: 'children',
                label: 'label',
                value: 'value',
                emitPath: false,
                checkStrictly: true,
                multiple: true,
                checkStrictly: true
              },
              items: roleList.value
            },
            rules: [{ required: true, message: t('modelPortraitEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPortraitEdit.xingWeiShuJu'),
            name: 'behaviors',
            value: [],
            span: 24,
            component: 'treeSelect',
            options: {
              placeholder: t('modelPortraitEdit.qingXuanZe'),
              props: {
                children: 'children',
                label: 'aliasName',
                value: 'id'
              },
              multiple: true,
              items: behaviorList.value
            },
            rules: [{ required: true, message: t('modelPortraitEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('modelPortraitEdit.yeMianLianJie'),
            span: 24,
            component: 'h5Url'
          },
          {
            label: t('modelPortraitEdit.miaoShuXinXi'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              type: 'textarea',
              placeholder: t('modelPortraitEdit.qingShuRu')
            }
          },
          { label: t('modelPortraitEdit.keHuMoXingPeiZhi'), component: 'divider' },
          { component: 'lwFieldsEdit' },
          ...behaviorSettingList.value
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.modelPortrait.info({ id: id.value })
      res.behaviors = res.behaviorSetting.map((item) => item.persistentModelId)
      dataForm.value = res
    }

    // 获取字段模型
    const behaviorList = ref([])
    const consumerList = ref([])
    const getFieldsModel = async () => {
      let res = await $api.modelPersistent.list({ expression: 'delete ne true' })
      behaviorList.value = res.filter((item) => item.dataPersistentInfo.persistentType == 'behavior')

      // 客户模型
      let item = res.find((item) => item.dataPersistentInfo.persistentType == 'consumer')
      dataForm.value.consumerSetting.persistentModelId = item.id
      consumerList.value = item.fields
    }

    onMounted(() => {
      loading.value = true
      Promise.all([getFieldsModel()])
        .then(() => {
          if (id.value) {
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          await $api.modelPortrait[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('modelPortraitEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/model/portrait', 'modelPortrait')
    }

    // h5
    const copyUrlText = `/mobile/#/horizontal?apikey={apigetway apikey}&profileCode={{${t(
      'modelPortraitEdit.huaXiangBianMa'
    )}}}&keyword={{${t('modelPortraitEdit.yongHuBiaoShiZhi')}}}&keywordType={{${t(
      'modelPortraitEdit.yongHuBiaoShiLeiXing'
    )}}}`
    const locationOrigin = window.location.origin

    watch(
      () => dataForm.value.behaviors,
      (val) => {
        // 处理显示
        let list = []
        val.forEach((id) => {
          let behavior = behaviorList.value.find((i) => i.id == id)
          if (behavior) {
            list.push({ label: behavior.aliasName + t('modelPortraitEdit.peiZhi'), component: 'divider' })
            list.push({ component: behavior.id })
          }
        })
        behaviorSettingList.value = list
      }
    )

    return {
      dataForm,
      dataFormRef,
      config,
      loading,
      isView,
      behaviorList,
      consumerList,
      locationOrigin,
      copyUrlText,
      save,
      close
    }
  }
}
