import { computed, getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export default {
  name: 'userPortrait',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const code = computed(() => route?.query?.id || '')
    const type = computed(() => route?.query?.type || '')

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        cdp_latest_version_Q_eq: true
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      tableList: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 获取显示字段

    const fieldList = ref([])
    const getFieldList = async () => {
      let list = await $api.modelPersistent.list()

      let isArr = []
      function getArr(arr) {
        arr.forEach((item) => {
          if (item.arrayType && ['NESTED'].includes(item.type)) {
            isArr.push(item.name)
          }
          if (item.fields.length > 0) {
            getArr(item.fields)
          }
        })
      }
      getArr(list)

      let id = list.find((item) => item.dataPersistentInfo.persistentType === 'consumer')?.id
      let res = await $api.userPortrait.fields({ id })

      // 过滤字段
      res = res.filter((item) => {
        return item.dataModelVisualSetting.detailDisplay
      })

      // 搜索条件&表头
      state.searchOptions = []
      state.tableHeaders = []
      state.tableList = []
      res.forEach((item) => {
        let aliasName = item.aliasName.split('.')?.[1] || item.aliasName
        if (item.dataModelVisualSetting.useForSearch) {
          let config = {
            label: aliasName,
            prop: `${item.fieldName}_Q_${item.dataModelVisualSetting.operator}`,
            renderType: item.fieldType.toLowerCase() === 'date' ? 'dateRange' : 'input'
          }
          if (item.fieldType.toLowerCase() === 'date') {
            config.startPlaceholder = `${aliasName}${t('userPortrait.kaiShiShiJian')}`
            config.endPlaceholder = `${aliasName}${t('userPortrait.jieShuShiJian')}`
          }
          state.searchOptions.push(config)
        }

        if (isArr.includes(item.fieldName.split('.')?.[0])) {
          let index = state.tableHeaders.findIndex((i) => i.dataIndex === item.fieldName.split('.')?.[0])
          if (index > -1) {
            state.tableHeaders[index].list.push({
              title: aliasName,
              dataIndex: item.fieldName.split('.')?.[1],
              width: item.fieldType == 'DATE' ? '180' : 60 + aliasName.length * 6,
              tooltip: true,
              date: item.fieldType == 'DATE'
            })
          } else {
            let component = `${item.fieldName.split('.')?.[0]}-expand`
            state.tableList.push(component)
            state.tableHeaders.push({
              title: item.aliasName.split('.')?.[0],
              dataIndex: item.fieldName.split('.')?.[0],
              width: item.fieldType == 'DATE' ? '180' : 60 + aliasName.length * 12,
              tooltip: true,
              component,
              list: []
            })
          }
        } else {
          state.tableHeaders.push({
            title: aliasName,
            dataIndex: item.fieldName,
            width: item.fieldType == 'DATE' ? '180' : 60 + aliasName.length * 12,
            tooltip: true,
            date: item.fieldType == 'DATE'
          })
        }
      })

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '90',
        fixed: 'right',
        ellipsis: true,
        operation: [
          {
            clickFun: (item) => edit(item, 'view'),
            label: t('btn.view'),
            auth: ['cdp.user_portrait_edit'],
            isShow: (item) => item.id || item.cdp_id
          }
        ]
      })
    }

    onMounted(() => {
      state.loading = true
      Promise.all([getFieldList()])
        .then(() => {
          getTableData()
        })
        .finally(() => {
          state.loading = false
        })
    })

    const getTableData = (page = 0) => {
      if (code.value && type.value != 'cdp_audience') {
        state.searchParams.cdp_tag_Q_eq = code.value
      }

      state.searchParams.page = page + 1
      if (type.value == 'cdp_audience') {
        delete state.searchParams.cdp_latest_version_Q_eq
        delete state.searchParams.cdp_tag
        let tenantId = $tool.data.get('tenantId')
        let params = {
          page: page,
          tenantId,
          size: state.searchParams.size,
          sort: 'updateTime,desc',
          expression: $expression(state.searchParams)
        }
        $api.userPortrait.personPage(params, code.value).then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
        })
      } else {
        let params = {
          page: page,
          size: state.searchParams.size,
          sort: 'updateTime,desc',
          expression: $expression(state.searchParams)
        }
        $api.userPortrait.page(params).then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
        })
      }
    }

    const reset = () => {
      state.searchParams = { page: 1, size: 10, cdp_latest_version_Q_eq: true }
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.id || item.cdp_id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id || item.cdp_id }
      }
      if (code.value) {
        query = { id: item.id || item.cdp_id, code: code.value, isView: true }
      }
      router.push({ path: '/user/portraitEdit', query })
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      search,
      edit
    }
  }
}
