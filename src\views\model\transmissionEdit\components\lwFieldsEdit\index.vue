<template>
  <div class="lw-fields-edit">
    <el-card class="card-left" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>{{$t('modelTransmissionEdit.moXingLieBiao')}}</span>
          <div class="card-header-right">
            <el-input :placeholder="$t('modelTransmissionEdit.SRGJZJXGL')" v-model="menuFilterText" clearable size="small"></el-input>
            <el-button v-if="!isView" type="primary" size="small" @click="editJson">JSON</el-button>
          </div>
        </div>
      </template>
      <div class="tree-list" :class="{ 'is-view': isView }">
        <el-tree
          ref="fieldMenuRef"
          class="menu"
          node-key="id"
          :data="treeFields"
          :props="menuProps"
          highlight-current
          :expand-on-click-node="false"
          check-strictly
          show-checkbox
          :filter-node-method="fieldFilterNode"
          @node-click="fieldClick"></el-tree>
      </div>

      <template v-if="!isView" #footer>
        <div class="footer-btn-list">
          <el-button type="primary" icon="el-icon-plus" @click="addField()"></el-button>
          <el-button type="danger" plain icon="el-icon-delete" @click="delFields"></el-button>
        </div>
      </template>
    </el-card>
    <el-card class="card-right" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>{{$t('modelTransmissionEdit.ziDuanPeiZhi')}}</span>
          <div v-if="fieldItem?.id && !isView" class="card-header-right">
            <el-button type="primary" size="small" @click="editJson('field')">JSON</el-button>
          </div>
        </div>
      </template>

      <!-- 字段编辑 -->
      <FieldEdit ref="fieldEditRef" v-model="fieldItem" />
    </el-card>
  </div>

  <!-- json -->
  <el-dialog v-model="dialogVisible" :title="$t('modelTransmissionEdit.bianJi')" width="50%">
    <div style="width: 100%; height: 500px">
      <lwCodeEdit v-model="textareaJson" placeholder="{{$t('modelTransmissionEdit.QJSXSBNTZCCZ')}}" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('btn.cancel') }}</el-button>
        <el-button @click="confirmJson" type="primary">{{ $t('btn.confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
import lwCodeEdit from '@/components/lwCodeEdit.vue'
import FieldEdit from './edit.vue'
export default {
  props: {
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  components: {
    lwCodeEdit,
    FieldEdit
  },
  data() {
    return {
      dialogVisible: false,
      treeFields: [],
      fieldItem: {},
      jsonType: '',
      menuFilterText: '',
      textareaJson: '',
      isView: !!this.$route.query.isView,
      menuProps: {
        label: (data) => {
          return data.aliasName
        },
        children: 'fields'
      }
    }
  },
  watch: {
    menuFilterText(val) {
      this.$refs.fieldMenuRef.filter(val)
    },
    modelValue: {
      handler(val) {
        this.treeFields = this.initKey(JSON.parse(JSON.stringify(val)))
      },
      immediate: true
    },
    fieldItem: {
      handler(val) {
        this.changeFieldItem(val)
      },
      deep: true
    }
  },
  methods: {
    initKey(fields, identifyName) {
      return fields.map((item) => {
        if (!item.id) {
          item.id = this.$tool.getUUID()
          item.identifyName = identifyName ? `${identifyName}.${item.name}` : item.name
        }
        if (item.fields && item.fields.length > 0) {
          item.fields = this.initKey(item.fields, item.identifyName)
        }
        return item
      })
    },
    editJson(type = '') {
      if (type == 'field') {
        this.textareaJson = JSON.stringify(this.fieldItem, null, 2)
      } else {
        this.textareaJson = JSON.stringify(this.treeFields, null, 2)
      }
      this.jsonType = type
      this.dialogVisible = true
    },
    confirmJson() {
      if (this.jsonType == 'field') {
        let fieldItem = JSON.parse(this.textareaJson)
        this.fieldClick(fieldItem)
        this.changeFieldItem(fieldItem)
      } else {
        this.$emit('update:modelValue', JSON.parse(this.textareaJson))
      }

      this.dialogVisible = false
      this.jsonType = ''
    },
    fieldFilterNode(value, data) {
      if (!value) return true
      let targetText = data.name
      return targetText.indexOf(value) !== -1
    },
    // 新增
    addField() {
      let node = this.$refs.fieldMenuRef.getCurrentNode()
      let newMenuData = {
        id: this.$tool.getUUID(),
        name: '',
        aliasName: '',
        tenantId: this.$tool.data.get('tenantId'),
        collectionId: this.$tool.data.get('collectionId'),
        description: '',
        fields: []
      }
      this.$refs.fieldMenuRef.append(newMenuData, node)
      if (node) {
        this.$nextTick(() => {
          this.$refs.fieldMenuRef.setCurrentKey(newMenuData.id, true)
        })
      }
    },
    //树点击
    async fieldClick(data, node) {
      this.fieldItem = data
      this.$nextTick(() => {
        this.$refs.fieldEditRef.init()
      })
    },

    //删除字段
    async delFields() {
      let CheckedNodes = this.$refs.fieldMenuRef.getCheckedNodes()
      if (CheckedNodes.length === 0) {
        this.$message.warning(this.$t('modelTransmissionEdit.QXZXYSCDX'))
        return false
      }

      await this.$confirm(this.$t('modelTransmissionEdit.QRSCYXZDCSM'), this.$t('modelTransmissionEdit.tiShi'), {
        type: 'warning',
        confirmButtonText: this.$t('modelTransmissionEdit.shanChu'),
        confirmButtonClass: 'el-button--danger'
      })

      CheckedNodes.forEach((item) => {
        this.$refs.fieldMenuRef.remove(item)
      })
      this.$emit('update:modelValue', this.treeFields)
      this.$message.success(this.$t('modelTransmissionEdit.shanChuChengGong'))
    },
    // 编辑字段
    replaceNodeById(tree, newItem) {
      return tree.map((item) => {
        if (item.id === newItem.id) {
          return newItem // 完全替换
        }

        if (Array.isArray(item.fields)) {
          return {
            ...item,
            fields: this.replaceNodeById(item.fields, newItem)
          }
        }

        return item
      })
    },
    changeFieldItem(item) {
      this.treeFields = this.replaceNodeById(this.treeFields, item)
      this.$emit('update:modelValue', this.treeFields)
    }
  }
}
</script>
<style lang="scss" scoped>
.lw-fields-edit {
  display: flex;
  gap: 10px;
  justify-content: space-between;
  width: 100%;
  :deep(.el-card__body) {
    padding: 0;
  }
  .card-left {
    width: 300px;
  }
  .card-right {
    width: calc(100% - 310px);
  }
  .tree-list {
    height: calc(100vh - 520px);
    overflow: auto;
    &.is-view {
      height: calc(100vh - 470px);
    }
  }
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    .card-header-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 200px;
    }
  }
  .footer-btn-list {
    display: flex;
    align-items: center;
    button {
      flex: 1;
    }
  }
  .custom-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    .tree-icon-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }
  }
}
</style>
