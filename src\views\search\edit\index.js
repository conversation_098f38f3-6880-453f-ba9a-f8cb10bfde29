import lwMappingEdit from '@/components/lwMappingEdit/index.vue'
import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'searchEdit',
  components: {
    lwMappingEdit
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const dataForm = ref({
      name: '',
      aliasName: '',
      mappingList: []
    })
    const dataFormRef = ref(null)
    const mappingEditRef = ref(null)
    const loading = ref(false)

    // 存储模型
    const persistentlList = ref([])
    const getPersistentList = async () => {
      let res = await $api.modelPersistent.list()
      persistentlList.value = res.map((item) => ({ label: item.aliasName, value: item.id }))
    }

    // 传输模型
    const transmissionList = ref([])
    const getTransmissionList = async () => {
      let res = await $api.modelTransmission.list()
      transmissionList.value = res?.map((item) => ({ label: item.aliasName, value: item.id }))
    }

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('searchEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('searchEdit.jieKouMingCheng'),
            name: 'aliasName',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('searchEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('searchEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('searchEdit.jieKouBianMa'),
            name: 'name',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('searchEdit.qingShuRu')
            },
            tips: t('searchEdit.ZNWXXZMSZHXH'),
            rules: [
              { required: true, message: t('searchEdit.qingShuRu'), trigger: 'blur' },
              { pattern: /^[a-z0-9_]+$/, message: t('searchEdit.ZNWXXZMSZHXH'), trigger: 'blur' },
              { max: 20, message: t('searchEdit.CDBNCGGZF'), trigger: 'blur' }
            ]
          },
          {
            label: t('searchEdit.chuanShuMoXing'),
            name: 'transferModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('searchEdit.qingXuanZe'),
              items: transmissionList.value
            },
            tips: t('searchEdit.KXZCSMXZYJLD'),
            rules: [{ required: true, message: t('searchEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('searchEdit.cunChuMoXing'),
            name: 'persistentModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('searchEdit.qingXuanZe'),
              items: persistentlList.value
            },
            tips: t('searchEdit.KXZCCMXZYJLD'),
            rules: [{ required: true, message: t('searchEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('searchEdit.shiFouYongYuDaoChu'),
            name: 'enableExport',
            value: false,
            span: 6,
            component: 'switch',
            options: {
              activeText: t('searchEdit.shi'),
              inactiveText: t('searchEdit.fou')
            }
          },
          {
            label: t('searchEdit.beiZhu'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              type: 'textarea',
              placeholder: t('searchEdit.qingShuRu')
            }
          },
          { label: t('searchEdit.yingShePeiZhi'), component: 'divider' },
          { component: 'mappingEdit' }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.search.info({ id: id.value })
      dataForm.value = res
    }

    // 监听切换
    watch(
      () => dataForm.value.transferModelId,
      (val) => {
        if (val) {
          let item = transmissionList.value.find((item) => item.value === val)
          dataForm.value.transferModelName = item?.label || ''
        } else {
          dataForm.value.transferModelName = ''
        }
      }
    )
    watch(
      () => dataForm.value.persistentModelId,
      (val) => {
        if (val) {
          let item = persistentlList.value.find((item) => item.value === val)
          dataForm.value.persistentModelName = item?.label || ''
        } else {
          dataForm.value.persistentModelName = ''
        }
      }
    )

    onMounted(() => {
      loading.value = true
      Promise.all([getPersistentList(), getTransmissionList()])
        .then(() => {
          if (id.value) {
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          await $api.search[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('searchEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/search/list', 'searchList')
    }

    return {
      dataForm,
      mappingEditRef,
      dataFormRef,
      config,
      loading,
      isView,
      save,
      close
    }
  }
}
