export default {
  btn: {
    all: 'Select All',
    operation: 'Operation',
    submit: 'Submit',
    cancel: 'Cancel',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Confirm deletion?',
    deleteSuccess: 'Deleted successfully',
    confirm: 'Confirm',
    tips: 'Notification',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    search: 'Search',
    refresh: 'Refresh',
    upload: 'Upload',
    download: 'Download',
    close: 'Close',
    apply: 'Apply',
    reset: 'Reset',
    add: 'Add',
    view: 'View',
    more: 'More',
    copy: 'Copy',
    filter: 'Filter',
    sort: 'Sort',
    export: 'Export',
    import: 'Import',
    query: 'Query'
  },
  layout: {
    topbar: 'Current Location',
    userData: 'Profile',
    outLogin: 'Logout',
    online: 'Online',
    configuring: 'Configuring',
    clearCache: 'Clear Cache'
  },
  user: {
    settings: 'Settings',
    nightmode: 'Dark Mode',
    color: 'Theme Color',
    layout: 'Layout',
    menu: 'Collapse Menu',
    tag: 'Tag Bar',
    lang: 'Internationalization',
    nightmode_msg: 'Suitable for low-light environments (Dark mode is in beta)',
    language: 'Language',
    language_msg: 'Translation in progress, currently only this view is translated'
  },
  menu: {
    dashboard: 'Dashboard',
    dataWarehouse: 'DW',
    dataRouter: {
      list: 'Data Router',
      edit: 'Edit Router'
    },
    task: {
      list: 'Tasks',
      edit: 'Edit Task'
    },
    model: {
      name: 'Models',
      persistent: 'Storage Model',
      persistentEdit: 'Edit Storage',
      transmission: 'Transfer Model',
      transmissionEdit: 'Edit Transfer',
      export: 'Export Model',
      exportEdit: 'Edit Export',
      portrait: 'Profile Model',
      portraitEdit: 'Edit Profile'
    },
    user: {
      name: 'User Insights',
      portrait: 'User Profile',
      portraitEdit: 'Profile Details',
      tag: 'Tag Mgmt',
      tagEdit: 'Edit Tag',
      tagStatistics: 'Tag Stats',
      tagHistory: 'Tag Snapshot',
      marking: 'Manual Tagging',
      markingEdit: 'Edit Tagging',
      circle: 'Crowd Select',
      circleStatistics: 'Crowd Stats',
      circleHistory: 'Crowd Snapshot',
      circleEdit: 'Edit Crowd',
      userOther: 'Insights'
    },
    dataInsight: {
      name: 'Data Insights'
    },
    collect: {
      name: 'Collection',
      api: 'API',
      apiEdit: 'Edit API',
      database: 'DB',
      databaseEdit: 'Edit DB',
      file: 'File',
      fileEdit: 'Edit File'
    },
    arrange: {
      name: 'Orchestration',
      redirect: 'Data Redirect',
      redirectEdit: 'Edit Redirect'
    },
    subscribe: {
      name: 'Subscription',
      api: 'API',
      apiEdit: 'Edit API',
      database: 'DB',
      databaseEdit: 'Edit DB'
    },
    task: {
      name: 'Tasks',
      deploy: 'Config',
      deployEdit: 'Edit Config',
      record: 'Records',
      recordEdit: 'Edit Record'
    },
    search: {
      name: 'Query',
      edit: 'Edit Query'
    },
    tags: 'Tag Categories'
  }
}
