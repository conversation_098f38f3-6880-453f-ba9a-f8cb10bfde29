import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

export function exportExcel(data, headers, filename = "数据表.xlsx") {
  if (!data || data.length === 0) {
    console.warn("数据为空，无法导出");
    return;
  }

  // 1. 生成表头（第一行）
  const headerNames = headers.map(header => header.name); // 中文表头
  const headerKeys = headers.map(header => header.value); // 数据 key

  // 2. 生成数据行
  const sheetData = [
    headerNames, // 第一行是表头
    ...data.map(row => headerKeys.map(key => row[key] || "")), // 数据行
  ];

  // 3. 创建工作表
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 4. **自动列宽**（根据内容长度调整）
  const columnWidths = headers.map(header => ({ wch: Math.max(10, header.name.length + 2) })); // 最小宽度 10
  worksheet["!cols"] = columnWidths;

  // 5. **设置样式**
  const defaultFont = { name: "宋体", sz: 12 }; // 默认字体
  const borderStyle = { style: "thin", color: { auto: 1 } }; // 边框样式

  Object.keys(worksheet).forEach(cell => {
    if (cell.startsWith("!")) return;

    const cellRef = XLSX.utils.decode_cell(cell);
    const isHeader = cellRef.r === 0; // 判断是否为表头

    worksheet[cell].s = {
      font: isHeader ? { ...defaultFont, bold: true } : defaultFont, // 表头加粗
      fill: isHeader
        ? { fgColor: { rgb: "FFFF99" } } // 表头背景色：淡黄色
        : undefined,
      alignment: { horizontal: "center", vertical: "center" }, // 文字居中
      border: {
        top: borderStyle,
        bottom: borderStyle,
        left: borderStyle,
        right: borderStyle,
      }, // 添加边框
    };
  });

  // 6. 创建工作簿并添加工作表
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

  // 7. 生成 Excel 并导出
  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  const excelBlob = new Blob([excelBuffer], { type: "application/octet-stream" });
  saveAs(excelBlob, filename);
}

/**
 * excel 内容解析
 * @param {*} file 
 * @param {*} headers 
 * @returns 
 */
export function parseExcel(file, headers) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: "array" });

      // 获取第一个工作表
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // 解析为 JSON（原始数据）
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // **检查数据是否为空**
      if (jsonData.length === 0) {
        reject("Excel 文件内容为空");
        return;
      }

      // **解析表头，映射成 headers 里的 key**
      const excelHeader = jsonData[0]; // 取第一行作为表头
      const keyMap = headers.reduce((map, item) => {
        const colIndex = excelHeader.indexOf(item.name); // 找到表头对应的列索引
        if (colIndex !== -1) {
          map[colIndex] = item.value; // 存储列索引与 key 的映射关系
        }
        return map;
      }, {});

      // **解析数据**
      const parsedData = jsonData.slice(1).map(row => {
        return Object.keys(keyMap).reduce((obj, colIndex) => {
          obj[keyMap[colIndex]] = row[colIndex] || ""; // 根据 keyMap 转换数据
          return obj;
        }, {});
      });

      resolve(parsedData);
    };
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
}
