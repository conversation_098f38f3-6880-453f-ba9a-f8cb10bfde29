import config from '@/config'
import request from '@/utils/request'

export default {
  // 获取分页列表
  page: async (params) => {
    let url = `${config.API_URL}/cdp-portal/data_persistent_model/{collectionId}`
    return await request.get(url, params)
  },
  // 数据览顶部数据
  getTopData: async () => {
    let url = `${config.API_URL}/cdp-portal/dashboard/{collectionId}/data_statistics`
    return await request.get(url)
  },
  // 数据资产
  assetData: async (params) => {
    let url = `${config.API_URL}/cdp-portal/dashboard/{collectionId}/asset_data`
    return await request.get(url, params)
  },
  // 任务概览
  task: async (params) => {
    let url = `${config.API_URL}/cdp-portal/dashboard/{collectionId}/task_data`
    return await request.get(url, params)
  },
  // 标签
  tag: async (params) => {
    let url = `${config.API_URL}/cdp-portal/dashboard/{collectionId}/tag_data`
    return await request.get(url, params)
  },
  // 人群
  audience: async (params) => {
    let url = `${config.API_URL}/cdp-portal/dashboard/{collectionId}/audience_data`
    return await request.get(url, params)
  }
}
