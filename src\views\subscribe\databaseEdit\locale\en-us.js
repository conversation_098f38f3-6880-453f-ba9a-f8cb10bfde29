export default {
  subscribeDatabaseEdit: {
    jiBenXinXi: 'Basic Information',
    mingCheng: 'Name',
    qingShuRu: 'Please Enter',
    bianMa: 'Code',
    ZNWXXZMSZHXH: 'Only lowercase letters, numbers or underscores allowed',
    CDBNCGGZF: 'Maximum length is 20 characters',
    chuanShuMoXing: 'Transfer Model',
    qingXuanZe: 'Please Select',
    KXZCSMXZYJLD: 'Available data tables from the transfer model',
    shuJuTuiSongMoXing: 'Data Push Model',
    cunChuMoXing: 'Storage Model',
    dingYueMoXing: 'Subscription Model',
    shiFouJiLuRiZhi: 'Enable Logging',
    jiLu: 'Enable',
    buJiLu: 'Disable',
    SFXYJLRZKQHJ: 'Enable logging to record operation logs',
    beiZhu: 'Remarks',
    shuJuKu: 'Database',
    shuJuKuMingCheng: 'Database Name',
    shuJuKuLeiXing: 'Database Type',
    biaoMing: 'Table Name',
    zhu<PERSON><PERSON>: 'Primary Key',
    yongHuMing: 'Username',
    miMa: 'Password',
    guo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Filter Conditions',
    paiXu: 'Sort Order',
    zhuJi: 'Host',
    duanKou: 'Port',
    yingShePeiZhi: 'Mapping Configuration',
    baoCunChengGong: 'Successfully Saved'
  }
}
