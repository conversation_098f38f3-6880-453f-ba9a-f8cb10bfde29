import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'collectDatabase',
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 修改状态
    const changeNeedPersistent = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.collectDatabase.edit({
        ...item,
        needPersistent: value
      })
    }
    const changeNeedLogging = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.collectDatabase.edit({
        ...item,
        needLogging: value
      })
    }

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('collectDatabase.bianMa'),
          prop: 'name_like',
          renderType: 'input'
        },
        {
          label: t('collectDatabase.mingCheng'),
          prop: 'aliasName_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('collectDatabase.bianMa'), dataIndex: 'name', minWidth: '120', copy: true, tooltip: true },
        { title: t('collectDatabase.mingCheng'), dataIndex: 'aliasName', minWidth: '160' },
        { title: t('collectDatabase.shiFouCunChu'), dataIndex: 'needPersistent', width: '160', switch: true, clickFun: changeNeedPersistent },
        { title: t('collectDatabase.shiFouRiZhi'), dataIndex: 'needLogging', width: '160', switch: true, clickFun: changeNeedLogging }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '180',
        fixed: 'right',
        ellipsis: true,
        operation: [
          { clickFun: (item) => edit(item, 'view'), label: t('btn.view') },
          { clickFun: (item) => edit(item, 'edit'), label: t('btn.edit') },
          { clickFun: (item) => edit(item, 'run'), label: t('collectDatabase.yunXing') },
          { clickFun: del, label: t('btn.delete') }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.collectDatabase
        .page(params)
        .then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = {}
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    const runDb = async (item) => {
      await ElMessageBox.confirm(t('collectDatabase.queDingYunXingMa'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      let collectionId = $tool.data.get('collectionId')
      let tenantId = $tool.data.get('tenantId')
      await $api.collectDatabase.run({
        collectionId,
        dataExtractorId: item.id,
        executeType: 'manual',
        name: 'dataExtractorTask',
        taskType: 'cdp_data_extractor',
        tenantId
      })
      ElMessage({ type: 'success', message: t('collectDatabase.shuJuChouQuWanCheng') })
    }

    // 查看 新增 编辑 运行
    const edit = async (item, type = 'add') => {
      if (type == 'run') {
        runDb(item)
        return false
      }

      let query = {}
      if (type === 'view') {
        query = { id: item.id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/collect/databaseEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.collectDatabase.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      edit
    }
  }
}
