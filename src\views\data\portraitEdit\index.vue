<template>
  <el-container>
    <el-main v-loading="loading">
      <lwFormMini ref="dataFormRef" :config="config" :isView="isView" v-model="dataForm" blackType="tabs">
        <template #tagsView>
          <TagsView :tagList="tagList" />
        </template>
        <template v-for="item in fieldList" v-slot:[item.name] :key="item.name">
          <UserHistory v-if="item.name == 'user_history'" :id="item.name" :fields="item.fields" :identity="identity" />
          <TabTable v-else :id="item.name" :fields="item.fields" :identity="identity" />
        </template>
      </lwFormMini>
    </el-main>
    <el-footer class="footer-body">
      <el-button @click="close">{{ $t('btn.close') }}</el-button>
      <el-button v-if="!isView" type="primary" @click="save">{{ $t('btn.save') }}</el-button>
    </el-footer>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
