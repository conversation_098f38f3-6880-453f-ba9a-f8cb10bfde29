<template>
  <div class="field-edit">
    <lwFormMini
      v-if="dataForm?.id"
      ref="dataFormRef"
      :config="config"
      :isView="isView"
      v-model="dataForm"
      :loading="loading">
      <template #selectDicts>
        <lwDictEdit v-model="dataForm.mappingDictId" @getDicts="getDicts" />
      </template>
    </lwFormMini>

    <el-empty v-else :description="$t('modelPersistentEdit.QXZYBJDZD')" />
  </div>
</template>

<script>
import { ref, computed, onMounted, getCurrentInstance, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import lwDictEdit from '@/components/lwDictEdit/index.vue'

export default {
  components: {
    lwDictEdit
  },
  props: {
    modelValue: {
      type: Object,
      default: () => {}
    }
  },
  setup(props, { emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, t }
    } = getCurrentInstance()

    // 表单数据
    const dataForm = ref({})
    const dataFormRef = ref(null)
    const isView = computed(() => !!route.query.isView)
    const loading = ref(false)

    const primaryList = ref([])
    const init = () => {
      dataForm.value = props.modelValue
      primaryList.value = props.modelValue?.fields.map((item) => ({ label: item.aliasName, value: item.name })) || []
    }

    // 获取类型
    const options = ref([])
    const getTypes = async () => {
      const res = await $api.modelPersistent.fieldTypes()
      options.value = res.map((item) => ({ label: item, value: item }))
    }

    watch(
      () => dataForm.value,
      (val) => {
        emit('update:modelValue', val)
      },
      { deep: true }
    )

    const dicts = ref([])
    const getDicts = async () => {
      const res = await $api.dict.list({
        expression: `tenantId eq ${$tool.data.get('tenantId')}`
      })
      dicts.value = res.map((item) => ({
        label: item.name,
        value: item.id,
        ...item
      }))
    }

    const config = computed(() => {
      return {
        labelWidth: '60px',
        labelPosition: 'top',
        size: 'small',
        formItems: [
          {
            label: t('modelPersistentEdit.mingCheng'),
            name: 'name',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelPersistentEdit.ziDuanMingCheng')
            },
            rules: [{ required: true, message: t('modelPersistentEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPersistentEdit.zhongWenMing'),
            name: 'aliasName',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelPersistentEdit.zhongWenMing')
            },
            rules: [{ required: true, message: t('modelPersistentEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPersistentEdit.leiXing'),
            name: 'type',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              items: options.value
            },
            rules: [{ required: true, message: t('modelPersistentEdit.qingXuanZe'), trigger: 'change' }]
          },
          {
            label: t('modelPersistentEdit.zhuJian'),
            name: 'primaryKey',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZeZhuJian'),
              items: primaryList.value
            },
            hideHandle: '$.type != "NESTED"'
          },
          {
            label: t('modelPersistentEdit.ziDuanJiaoYanFangShi'),
            name: 'dataFieldValidType',
            value: 'NONE',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.ziDuanJiaoYanFangShi'),
              items: [
                { label: t('modelPersistentEdit.wuXuJiaoYan'), value: 'NONE' },
                { label: t('modelPersistentEdit.shouJiJiaoYan'), value: 'PHONE' },
                { label: t('modelPersistentEdit.zhengZeJiaoYan'), value: 'REGEX' }
              ]
            }
          },
          {
            label: t('modelPersistentEdit.zhengZeBiaoDaShi'),
            name: 'regExp',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelPersistentEdit.zhengZeBiaoDaShi')
            },
            hideHandle: '$.dataFieldValidType != "REGEX"'
          },
          {
            label: t('modelPersistentEdit.yunXuWeiKong'),
            name: 'allowNull',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('modelPersistentEdit.shiFouShuZu'),
            name: 'arrayType',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('modelPersistentEdit.SZYSSFHB'),
            name: 'arrayDataMergeMode',
            value: false,
            span: 6,
            component: 'switch',
            hideHandle: '!$.arrayType'
          },
          {
            label: t('modelPersistentEdit.yongYuBiaoQian'),
            name: 'useForTag',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('modelPersistentEdit.jinCunChu'),
            name: 'onlyStorage',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: 'piiData',
            name: 'piiData',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('modelPersistentEdit.ziDianYingShe'),
            name: 'enableMappingDict',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('modelPersistentEdit.yongYuShouGongBiaoQian'),
            name: 'useForHandTag',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('modelPersistentEdit.yanMaLeiXing'),
            name: 'piiDataSetting.dataMaskSetting.dataMaskType',
            value: '',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              items: [
                { label: t('modelPersistentEdit.shouJiYanMa'), value: 'MOBILE' },
                { label: t('modelPersistentEdit.zuoJiYanMa'), value: 'PHONE' },
                { label: t('modelPersistentEdit.diZhiYanMa'), value: 'ADDRESS' },
                { label: t('modelPersistentEdit.ziDingYiYanMa'), value: 'USER_DEFINED' }
              ]
            },
            hideHandle: '!($.piiData && $.piiDataSetting.piiDataDisplayType === "MASK")'
          },
          {
            label: t('modelPersistentEdit.yanMaQiShiWeiZhi'),
            name: 'piiDataSetting.dataMaskSetting.startIndex',
            value: '',
            span: 6,
            component: 'number',
            options: {
              placeholder: t('modelPersistentEdit.yanMaQiShiWeiZhi')
            },
            hideHandle:
              '!($.piiData && $.piiDataSetting.piiDataDisplayType === "MASK" && $.piiDataSetting.dataMaskSetting.dataMaskType === "USER_DEFINED")'
          },
          {
            label: t('modelPersistentEdit.yanMaWeiShu'),
            name: 'piiDataSetting.dataMaskSetting.maskLength',
            value: '',
            span: 6,
            component: 'number',
            options: {
              placeholder: t('modelPersistentEdit.yanMaWeiShu')
            },
            hideHandle:
              '!($.piiData && $.piiDataSetting.piiDataDisplayType === "MASK" && $.piiDataSetting.dataMaskSetting.dataMaskType === "USER_DEFINED")'
          },
          // 可视化设置
          {
            label: t('modelPersistentEdit.xiangQingXianShi'),
            name: 'visualSetting.detailDisplay',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('modelPersistentEdit.lieBiaoXianShi'),
            name: 'visualSetting.listDisplay',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('modelPersistentEdit.zhanShi'),
            name: 'piiDataSetting.piiDataDisplayType',
            value: '',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              items: [
                { label: t('modelPersistentEdit.zhiJieZhanShi'), value: 'ORIGINAL' },
                { label: t('modelPersistentEdit.buZhanShi'), value: 'NONE' },
                { label: t('modelPersistentEdit.yanMaZhanShi'), value: 'MASK' },
                { label: t('modelPersistentEdit.zhanShi'), value: 'MD5' },
                { label: t('modelPersistentEdit.zhanShi'), value: 'SHA' }
              ]
            },
            hideHandle: '!$.piiData'
          },
          // 字典映射相关字段
          {
            label: t('modelPersistentEdit.ziDianJi'),
            name: 'mappingDictId',
            value: '',
            span: 6,
            component: 'selectDicts',
            hideHandle: '!$.enableMappingDict'
          },
          // 标签选择相关字段
          {
            label: t('modelPersistentEdit.biaoQianXuanZeFangShi'),
            name: 'tagConditionFieldSetting.displayType',
            value: '',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.biaoQianXuanZeFangShi'),
              items: [
                { label: t('modelPersistentEdit.shouDongTianXie'), value: 'input_box' },
                { label: t('modelPersistentEdit.danXuanShuRu'), value: 'single_list' },
                { label: t('modelPersistentEdit.duoXuanShuRu'), value: 'multi_list' }
              ]
            }
          },
          {
            label: t('modelPersistentEdit.xuanZeShuJu'),
            name: 'tagConditionFieldSetting.dictId',
            value: '',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.ziDianJi'),
              clearable: true,
              items: dicts.value
            },
            hideHandle: '!["single_list", "multi_list"].includes($.tagConditionFieldSetting?.displayType)'
          },
          {
            label: t('modelPersistentEdit.yongYuSouSuo'),
            name: 'visualSetting.useForSearch',
            value: false,
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingShuRu'),
              items: [
                { label: t('modelPersistentEdit.yunXuSouSuo'), value: true },
                { label: t('modelPersistentEdit.jinZhiSouSuo'), value: false }
              ]
            }
          },
          {
            label: t('modelPersistentEdit.souSuoFangShi'),
            name: 'visualSetting.operator',
            value: 'eq',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              items: [{ label: t('modelPersistentEdit.wanQuanPiPei'), value: 'eq' }]
            },
            hideHandle:
              '!$.visualSetting?.useForSearch || ["TEXT", "LONG", "DATE", "INTEGET", "SHORT", "DOUBLE", "INTEGER"].includes($.type)'
          },
          {
            label: t('modelPersistentEdit.souSuoFangShi'),
            name: 'visualSetting.operator',
            value: 'eq',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              items: [
                { label: t('modelPersistentEdit.wanQuanPiPei'), value: 'eq' },
                { label: t('modelPersistentEdit.moHuSouSuo'), value: 'like' }
              ]
            },
            hideHandle: '!($.visualSetting?.useForSearch && $.type == "TEXT")'
          },
          {
            label: t('modelPersistentEdit.souSuoFangShi'),
            name: 'visualSetting.operator',
            value: 'eq',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              items: [
                { label: t('modelPersistentEdit.wanQuanPiPei'), value: 'eq' },
                { label: t('modelPersistentEdit.xiaoYuShuRuZhi'), value: 'lt' },
                { label: t('modelPersistentEdit.daYuShuRuZhi'), value: 'gt' },
                { label: t('modelPersistentEdit.quJian'), value: 'bt' }
              ]
            },
            hideHandle:
              '!($.visualSetting?.useForSearch && ["LONG", "DATE", "INTEGET", "SHORT", "DOUBLE", "INTEGER"].includes($.type))'
          },
          {
            label: t('modelPersistentEdit.xianShiLeiXing'),
            name: 'visualSetting.displayType',
            value: '',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              items: options.value
            },
            hideHandle: '!($.visualSetting.detailDisplay || $.visualSetting.listDisplay)'
          },
          {
            label: t('modelPersistentEdit.shiFouHuanHang'),
            name: 'visualSetting.newLine',
            value: false,
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              items: [
                { label: t('modelPersistentEdit.yunXuHuanHang'), value: true },
                { label: t('modelPersistentEdit.jinZhiHuanHang'), value: false }
              ]
            },
            hideHandle: '!($.visualSetting.detailDisplay || $.visualSetting.listDisplay)'
          },
          {
            label: t('modelPersistentEdit.riQiGeShi'),
            name: 'visualSetting.dateFormat',
            value: '',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              items: [
                { label: 'YYYY-MM-DD hh:mm:ss', value: 'YYYY-MM-DD hh:mm:ss' },
                { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' }
              ]
            },
            hideHandle:
              '!(($.visualSetting.detailDisplay || $.visualSetting.listDisplay) && $.visualSetting.displayType == "DATE")'
          }
        ]
      }
    })

    onMounted(() => {
      getDicts()
      getTypes()
    })

    return {
      dataForm,
      dataFormRef,
      isView,
      loading,
      config,
      options,
      getDicts,
      init
    }
  }
}
</script>
<style lang="scss" scoped>
.field-edit {
  :deep(.lw-form-mini-card) {
    border: 0 !important;
  }
}
</style>
