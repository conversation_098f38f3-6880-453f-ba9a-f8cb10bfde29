export default {
  name: 'searchList',
  path: '/search/list',
  sort: 9,
  meta: {
    icon: 'el-icon-search',
    type: 'menu',
    title: 'menu.search.name',
    roles: ['cdp.search_list']
  },
  component: 'search/list',
  children: [
    {
      name: 'searchEdit',
      path: '/search/edit',
      component: 'search/edit',
      sort: 0,
      meta: {
        icon: 'el-icon-edit',
        type: 'menu',
        title: 'menu.search.edit',
        active: '/search/list',
        hidden: true
      }
    }
  ]
}
