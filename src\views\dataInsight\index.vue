<template>
  <el-container>
    <el-main>
      <lw-search
        :options="searchOptions"
        v-model="searchParams"
        :columnNumber="layout.grid"
        :expandNumber="4"
        :labelWidth="layout.labelWidth + 'px'"
        :hideLabel="layout.hideLabel"
        :labelAlign="layout.labelAlign"
        @search="search"
        @reset="reset" />
      <div class="table-block">
        <div class="btn-container">
          <el-button
            v-auth="['cdp.data_insight.export']"
            type="primary"
            icon="el-icon-download"
            @click="openExportDialog">
            {{ $t('dataInsight.daoChu') }}
          </el-button>
        </div>
        <lw-table
          :hideTool="false"
          :loading="loading"
          :table-data="tableData"
          :tableColumns="tableHeaders"
          :search-params="searchParams"
          :isShowPagination="true"
          :total-count="totalCount"
          @getTableData="getTableData" />
      </div>
    </el-main>

    <!-- 导出弹窗 -->
    <el-dialog
      v-model="exportDialogVisible"
      :title="$t('dataInsight.daoChu')"
      width="450px"
      :close-on-click-modal="false"
      custom-class="export-dialog">
      <div style="margin-bottom: 5px">{{ $t('dataInsight.QXZDCMX') }}</div>
      <el-select v-model="exportData.name" :placeholder="$t('dataInsight.QXZDCMX')" style="width: 100%" clearable>
        <el-option v-for="option in exportModel" :key="option.name" :label="option.aliasName" :value="option.name" />
      </el-select>
      <div style="margin-bottom: 5px; margin-top: 10px">{{ $t('dataInsight.guoLüTiaoJian') }}</div>
      <el-input v-model="exportData.expression" :placeholder="$t('dataInsight.QSRGLTJ')" />
      <template #footer>
        <el-button @click="cancelExport">{{ $t('dataInsight.quXiao') }}</el-button>
        <el-button type="primary" @click="confirmExport">{{ $t('dataInsight.queDing') }}</el-button>
      </template>
    </el-dialog>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
