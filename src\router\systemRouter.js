import config from "@/config"

//系统路由
const routes = [
	{
		name: 'layout',
		path: '/',
		component: () => import('@/layout'),
		redirect: config.DASHBOARD_URL || '/dashboard',
		children: []
	},
	{
		name: 'initialization',
		path: '/initialization',
		component: () => import('@/views/initialization/index.vue'),
		meta: {
			icon: 'el-icon-user',
			type: 'menu',
			title: 'menu.initialization',
			hidden: true
		}
	},
	{
		path: '/passport-login',
		component: () => import('@/views/login/index.vue'),
		meta: {
			title: '登录'
		}
	}
]

export default routes;
