import config from '@/config'
import request from '@/utils/request'

export default {
  // 获取加密设置
  key: async () => {
    let url = `${config.API_URL}/platform-tenant/authentication/transport_encrypt_setting`
    return await request.get(url)
  },
  // 用户登录
  token: async (data = {}) => {
    let url = `${config.API_URL}/platform-tenant/authentication/user`
    return await request.post(url, data)
  },
  // 获取所有BU（Business Units）
  bu: async () => {
    let url = `${config.API_URL}/platform-tenant/tenant_business_unit/current_user_business_unit`
    return await request.get(url)
  },
  // 获取用户信息
  user: async () => {
    let url = `${config.API_URL}/platform-tenant/authentication/status`
    return await request.get(url)
  },

  // 初始化校验
  initialized: async () => {
    let url = `${config.API_URL}/loyalty-manage/{tenantId}/{buCode}/customer_center/zone/initialized`
    return await request.get(url)
  },
  // 获取权限菜单
  menu: async () => {
    let url = `${config.API_URL}/platform-tenant/tenant_menu/current_user_menu`
    return await request.get(url)
  },
  // 设置初始化状态
  setInitialized: async (data = {}) => {
    let url = `${config.API_URL}/loyalty-manage/{tenantId}/{buCode}/customer_center/zone`
    return await request.put(url, data)
  },
  // 修改密码
  password: async (data = {}) => {
    let url = `${config.API_URL}/platform-tenant/authentication/password`
    return await request.put(url, data)
  },
  // 上传文件
  upload: async (data = {}, c = {}) => {
    let url = `${config.API_URL}/cms-content/{tenantId}/{buCode}/asset_file/upload`
    return await request.post(url, data, c)
  },
  // 申请使用
  apply: async (data = {}) => {
    let url = `${config.API_URL}/platform-portal/platform_potential`
    return await request.post(url, data)
  },
  // 切换状态
  editStatus: async (data) => {
    let url = `${config.API_URL}/cdp-portal/meta_data/status/{tenantId}?serviceStatus=${data}`
    return await request.put(url, data)
  },
  // 获取状态
  getStatus: async () => {
    let url = `${config.API_URL}/cdp-portal/meta_data/status/{tenantId}`
    return await request.get(url)
  },
  // 获取BU数据库
  getDb: async (params) => {
    let url = `${config.API_URL}/cdp-portal/data_collection/list`
    return await request.get(url, params)
  },
  // 获取数据角色
  getRoleData: async (params) => {
    let url = `${config.API_URL}/platform-tenant/tenant_data_role/current_user_data_role`
    return await request.get(url, params)
  },
  // 获取洞察数据
  getInsightList: async (params) => {
    let url = `${config.API_URL}/cdp-portal/customer_view_model/{collectionId}/list/current_user?expression=delete ne true`
    return await request.get(url, params)
  }
}
