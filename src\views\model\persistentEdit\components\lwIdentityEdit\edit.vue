<template>
  <div class="field-edit">
    <lwFormMini
      v-if="dataForm?.id"
      ref="dataFormRef"
      :config="config"
      :isView="isView"
      v-model="dataForm"
      :loading="loading">
    </lwFormMini>

    <el-empty v-else :description="$t('modelPersistentEdit.QXZYBJDBS')" />
  </div>
</template>

<script>
import { ref, computed, onMounted, getCurrentInstance, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { ElMessageBox } from 'element-plus'

export default {
  props: {
    modelValue: {
      type: Object,
      default: () => {}
    },
    fields: {
      type: Array,
      default: () => []
    }
  },
  setup(props, { emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, t }
    } = getCurrentInstance()

    // 表单数据
    const dataForm = ref({})
    const dataFormRef = ref(null)
    const isView = computed(() => !!route.query.isView)
    const loading = ref(false)

    // 表单配置
    const config = computed(() => {
      return {
        labelWidth: '60px',
        labelPosition: 'top',
        size: 'small',
        formItems: [
          {
            label: t('modelPersistentEdit.ziDuan'),
            name: 'identifyValueFieldName',
            value: [],
            span: 12,
            component: 'cascader',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              props: {
                value: 'name',
                label: 'aliasName',
                children: 'fields'
              },
              items: props.fields
            },
            rules: [{ required: true, message: t('modelPersistentEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPersistentEdit.qianZhuiLeiXing'),
            name: 'identifyTypeAssignType',
            value: 'fixed',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              items: [
                { label: t('modelPersistentEdit.guDingZhi'), value: 'fixed' },
                { label: t('modelPersistentEdit.yiYouZiDuan'), value: 'fromField' }
              ]
            },
            rules: [{ required: true, message: t('modelPersistentEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('modelPersistentEdit.qianZhui'),
            name: 'identifyTypeFixedValue',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelPersistentEdit.qingShuRu')
            },
            hideHandle: '$.identifyTypeAssignType != "fixed"'
          },
          {
            label: t('modelPersistentEdit.qianZhui'),
            name: 'identifyTypeFieldName',
            value: [],
            span: 12,
            component: 'cascader',
            options: {
              placeholder: t('modelPersistentEdit.qingXuanZe'),
              props: {
                value: 'name',
                label: 'aliasName',
                children: 'fields'
              },
              items: props.fields
            },
            hideHandle: '$.identifyTypeAssignType != "fromField"'
          },
          {
            label: t('modelPersistentEdit.zhuYaoBiaoJi'),
            name: 'disableIdMappingWhenDifferent',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('modelPersistentEdit.yunXuXiuGai'),
            name: 'enableUpdateIdentifyInfo',
            value: false,
            span: 6,
            component: 'switch'
          }
        ]
      }
    })

    const init = () => {
      dataForm.value = {
        ...props.modelValue,
        identifyTypeFieldName: props.modelValue?.identifyTypeFieldName?.split('.') || [],
        identifyValueFieldName: props.modelValue?.identifyValueFieldName?.split('.') || [],
        identifyValueFieldAliasName: props.modelValue?.identifyValueFieldAliasName?.split('.') || []
      }
    }

    // 获取选中的name值
    const getLabelsFromValue = (options, valueArray, labels = []) => {
      if (!valueArray || valueArray.length === 0) return labels

      const currentValue = valueArray[0]
      const currentOption = options.find((opt) => opt.name === currentValue)

      if (currentOption) {
        labels.push(currentOption.aliasName)
        if (currentOption.fields && valueArray.length > 1) {
          return getLabelsFromValue(currentOption.fields, valueArray.slice(1), labels)
        }
      }

      return labels
    }

    watch(
      () => dataForm.value,
      (valForm) => {
        let val = JSON.parse(JSON.stringify(valForm))
        val.identifyValueFieldAliasName = getLabelsFromValue(props.fields, val.identifyValueFieldName)
        emit('update:modelValue', {
          ...val,
          identifyTypeFieldName: val.identifyTypeFieldName?.join('.') || '',
          identifyValueFieldName: val.identifyValueFieldName?.join('.') || '',
          identifyValueFieldAliasName: val.identifyValueFieldAliasName?.join('.') || ''
        })
      },
      { deep: true }
    )

    return {
      dataForm,
      dataFormRef,
      isView,
      loading,
      config,
      init
    }
  }
}
</script>
<style lang="scss" scoped>
.field-edit {
  :deep(.lw-form-mini-card) {
    border: 0 !important;
  }
  .dict-list {
    display: flex;
    align-items: center;
    width: 100%;
    .dict-select {
      width: 100%;
    }
  }
  .el-button-group {
    display: flex;
    align-items: center;
  }
}
</style>
