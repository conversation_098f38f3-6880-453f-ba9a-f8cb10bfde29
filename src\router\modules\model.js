export default {
  name: 'model',
  path: '/model',
  sort: 7,
  meta: {
    icon: 'el-icon-files',
    type: 'menu',
    title: 'menu.model.name',
    roles: ['cdp.model']
  },
  redirect: '/model/persistent',
  children: [
    {
      name: 'modelPersistent',
      path: '/model/persistent',
      component: 'model/persistent',
      sort: 0,
      meta: {
        icon: 'el-icon-coin',
        type: 'menu',
        title: 'menu.model.persistent',
        roles: ['cdp.model_persistent']
      },
      children: [
        {
          name: 'modelPersistentEdit',
          path: '/model/persistentEdit',
          component: 'model/persistentEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.model.persistentEdit',
            active: '/model/persistent',
            hidden: true
          }
        }
      ]
    },
    {
      name: 'modelTransmission',
      path: '/model/transmission',
      component: 'model/transmission',
      sort: 1,
      meta: {
        icon: 'el-icon-switch',
        type: 'menu',
        title: 'menu.model.transmission',
        roles: ['cdp.model_transmission']
      },
      children: [
        {
          name: 'modelTransmissionEdit',
          path: '/model/transmissionEdit',
          component: 'model/transmissionEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.model.transmissionEdit',
            active: '/model/transmission',
            hidden: true
          }
        }
      ]
    },
    {
      name: 'modelExport',
      path: '/model/export',
      component: 'model/export',
      sort: 2,
      meta: {
        icon: 'el-icon-download',
        type: 'menu',
        title: 'menu.model.export',
        roles: ['cdp.model_export']
      },
      children: [
        {
          name: 'modelExportEdit',
          path: '/model/exportEdit',
          component: 'model/exportEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.model.exportEdit',
            active: '/model/export',
            hidden: true
          }
        }
      ]
    },
    {
      name: 'modelPortrait',
      path: '/model/portrait',
      component: 'model/portrait',
      sort: 3,
      meta: {
        icon: 'el-icon-picture',
        type: 'menu',
        title: 'menu.model.portrait',
        roles: ['cdp.model_portrait']
      },
      children: [
        {
          name: 'modelPortraitEdit',
          path: '/model/portraitEdit',
          component: 'model/portraitEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.model.portraitEdit',
            active: '/model/portrait',
            hidden: true
          }
        }
      ]
    }
  ]
}
