<template>
  <div class="other-filter-ist">
    <div class="other-filter-item">
      <div class="relation-left">
        <div class="line">
          <el-button class="relation" type="primary" plain size="small">且</el-button>
          <div class="line-horizontal-t"></div>
          <div class="line-horizontal-b"></div>
        </div>
      </div>
      <div class="other-filter-body">
        <div class="other-filter-tag">
          <div class="top-title">
            <strong>
              必要条件
              <div class="span-tips">
                <el-icon><el-icon-warning /></el-icon>
                以下条件全部命中则满足筛选
              </div>
            </strong>
            <el-button v-if="!isView" type="primary" plain @click="add('mustContainTags')" size="small"
              >新增/编辑</el-button
            >
          </div>

          <el-card class="tag-list" shadow="always">
            <div class="tag-item" v-for="(item, index) in dataForm.mustContainTags" :key="item">
              <el-tag type="primary">{{ tagMap[item] }}</el-tag>
              <span v-if="index !== dataForm.mustContainTags.length - 1">且</span>
            </div>
          </el-card>
        </div>
        <div class="other-filter-tag">
          <div class="top-title">
            <strong>
              可选条件
              <div class="span-tips">
                <el-icon><el-icon-warning /></el-icon>
                以下条件命中其一则满足筛选
              </div>
            </strong>
            <el-button v-if="!isView" type="primary" plain @click="add('shouldContainTags')" size="small"
              >新增/编辑</el-button
            >
          </div>
          <el-card class="tag-list" shadow="always">
            <div class="tag-item" v-for="(item, index) in dataForm.shouldContainTags" :key="item">
              <el-tag type="primary">{{ tagMap[item] }}</el-tag>
              <span v-if="index !== dataForm.shouldContainTags.length - 1">或</span>
            </div>
          </el-card>
        </div>
        <div class="other-filter-tag">
          <div class="top-title">
            <strong>
              排除条件
              <div class="span-tips">
                <el-icon><el-icon-warning /></el-icon>
                以下条件命中其一则排除筛选
              </div>
            </strong>
            <el-button v-if="!isView" type="primary" plain @click="add('notContainTags')" size="small"
              >新增/编辑</el-button
            >
          </div>
          <el-card class="tag-list" shadow="always">
            <div class="tag-item" v-for="(item, index) in dataForm.notContainTags" :key="item">
              <el-tag type="info">{{ tagMap[item] }}</el-tag>
              <span v-if="index !== dataForm.notContainTags.length - 1">或</span>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 选中弹窗 -->
    <el-dialog v-model="dialogVisible" title="编辑条件" width="650">
      <el-transfer
        v-model="tagValue"
        filterable
        :filter-method="filterMethod"
        filter-placeholder="输入筛选"
        :data="tagList"
        :titles="['标签列表', '已选标签']"
        :props="{
          key: 'tagId',
          label: 'fullName'
        }" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="submit"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, inject } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const {
  proxy: { $api, $tool, $bus, t }
} = getCurrentInstance()

const isView = computed(() => !!route.query.isView)

// 定义响应式数据模型
const dataForm = defineModel({
  type: Object,
  default: () => ({
    conditions: []
  })
})

// 添加条件
const options = inject('optionsPersistent')
const tagList = computed(() =>
  options.value.tagList.map((x) => ({ ...x, fullName: `${x?.tagGroupName ? x.tagGroupName + '-' : ''}${x.tagName}` }))
)
const tagMap = computed(() => {
  return options.value.tagList.reduce((acc, obj) => {
    acc[obj.tagId] = `${obj?.tagGroupName ? obj.tagGroupName + '-' : ''}${obj.tagName}`
    return acc
  }, {})
})

// 新增编辑
const dialogVisible = ref(false)
const tagValue = ref([])
const curName = ref('')
const add = (name) => {
  tagValue.value = []
  if (name === 'mustContainTags') {
    tagValue.value = dataForm.value.mustContainTags
  } else if (name === 'shouldContainTags') {
    tagValue.value = dataForm.value.shouldContainTags
  } else if (name === 'notContainTags') {
    tagValue.value = dataForm.value.notContainTags
  }
  curName.value = name
  dialogVisible.value = true
}
const submit = () => {
  let list = tagValue.value
    .filter((x) => tagList.value.find((y) => y.tagId === x))
    .map((x) => ({ id: x.tagId, name: x.fullName }))

  if (curName.value === 'mustContainTags') {
    dataForm.value.mustContainTagInfos = list
  } else if (curName.value === 'shouldContainTags') {
    dataForm.value.shouldContainTagInfos = list
  } else if (curName.value === 'notContainTags') {
    dataForm.value.notContainTagInfos = list
  }
  dataForm.value[curName.value] = tagValue.value
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.other-filter-ist {
  width: 100%;

  .condition-list {
    :deep(.logic-tree) {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .filter-item {
        padding: 16px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
      }
    }
  }

  .other-filter-item {
    display: flex;
    width: 100%;
    .relation-left {
      position: relative;
      width: 20px;
      min-width: 20px;
      margin: 0 10px 0 15px;

      .line {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 2px;
        background-color: var(--el-color-primary-light-5);
        display: flex;
        align-items: center;
        justify-content: center;

        .relation {
          position: absolute;
          z-index: 1;
        }

        .line-horizontal-t,
        .line-horizontal-b {
          position: absolute;
          width: 20px;
          height: 2px;
          background-color: var(--el-color-primary-light-5);
          left: 0;
        }

        .line-horizontal-t {
          top: 0;
        }

        .line-horizontal-b {
          bottom: 0;
        }
      }
    }

    .top-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      strong {
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 5px;
        .span-tips {
          display: flex;
          align-items: center;
          gap: 5px;
          color: #999;
          font-size: 12px;
          font-weight: normal;
          line-height: 24px;
        }
      }
    }
    .other-filter-body {
      width: 100%;

      .tag-list {
        margin-top: 5px;
        margin-bottom: 10px;
      }
      :deep(.el-card__body) {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .tag-item {
          span {
            margin-left: 5px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
:deep(.el-transfer-panel) {
  width: 228px;
}
</style>
