export default {
  modelPortraitEdit: {
    keHu: '客户',
    xingWei: '行为',
    puTong: '普通',
    jiBenXinXi: '基本信息',
    huaXiangMingCheng: '画像名称',
    qingShuRu: '请输入',
    huaXiangBianMa: '画像编码',
    shuJuJueSe: '数据角色',
    qingXuanZe: '请选择',
    xingWeiShuJu: '行为数据',
    yeMianLianJie: 'H5页面链接',
    miaoShuXinXi: '描述信息',
    keHuMoXingPeiZhi: '客户模型配置',
    baoCunChengGong: '保存成功',
    yongHuBiaoShiZhi: '用户标识值',
    yongHuBiaoShiLeiXing: '用户标识类型',
    peiZhi: '配置',
    fuZhi: '复制',
    ziDuan: '字段key',
    xianShiZhongWen: '显示中文',
    zhongWenMing: '中文名',
    xianShiYingWen: '显示英文',
    yingWenMing: '英文名',
    QXZYBJDZD: '请选择要编辑的字段',
    xiangQingXianShi: '详情显示',
    lieBiaoXianShi: '列表显示',
    zhanShi: 'SHA展示',
    zhiJieZhanShi: '直接展示',
    buZhanShi: '不展示',
    yanMaZhanShi: '掩码展示',
    yanMaLeiXing: '掩码类型',
    shouJiYanMa: '手机掩码',
    zuoJiYanMa: '座机掩码',
    diZhiYanMa: '地址掩码',
    ziDingYiYanMa: '自定义掩码',
    yanMaQiShiWeiZhi: '掩码起始位置',
    yanMaWeiShu: '掩码位数',
    yongYuSouSuo: '用于搜索',
    yunXuSouSuo: '允许搜索',
    jinZhiSouSuo: '禁止搜索',
    souSuoFangShi: '搜索方式',
    wanQuanPiPei: '完全匹配',
    moHuSouSuo: '模糊搜索',
    xiaoYuShuRuZhi: '小于输入值',
    daYuShuRuZhi: '大于输入值',
    quJian: '区间',
    xianShiLeiXing: '显示类型',
    shiFouHuanHang: '是否换行',
    yunXuHuanHang: '允许换行',
    jinZhiHuanHang: '禁止换行',
    riQiGeShi: '日期格式',
    SRGJZJXGL: '输入关键字进行过滤',
    bianJi: 'JSON编辑',
    moXingLieBiao: '模型列表',
    quanXuan: '全选',
    quXiaoQuanXuan: '取消全选',
    ziDuanPeiZhi: '字段配置',
    QJSXSBNTZCCZ: '请将所需识别json粘贴至此处。注意文末不可带标点符号，如，等'
  }
}
