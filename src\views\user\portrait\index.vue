<template>
  <el-container>
    <el-main>
      <lw-search
        :options="searchOptions"
        v-model="searchParams"
        :columnNumber="layout.grid"
        :expandNumber="4"
        :labelWidth="layout.labelWidth + 'px'"
        :hideLabel="layout.hideLabel"
        :labelAlign="layout.labelAlign"
        @search="search"
        @reset="reset" />
      <div class="table-block">
        <lw-table
          :hideTool="false"
          :loading="loading"
          :table-data="tableData"
          :tableColumns="tableHeaders"
          :search-params="searchParams"
          :isShowPagination="true"
          :total-count="totalCount"
          @getTableData="getTableData">
          <template v-for="item in tableList" :key="item" #[item]="{ scope, col }">
            <el-popover placement="top" width="400px" trigger="click">
              <template #reference>
                <el-button size="small" link type="primary"> {{ scope[col.dataIndex]?.length || 0 }} 条数据 </el-button>
              </template>
              <el-table :data="scope[col.dataIndex]">
                <el-table-column
                  :prop="c.dataIndex"
                  :label="c.title"
                  :width="c.width"
                  :align="c.align ?? 'center'"
                  :fixed="c.fixed"
                  :sortable="c.sortable"
                  :show-overflow-tooltip="c.tooltip"
                  v-for="c in col.list"
                  :key="c.dataIndex" />
              </el-table>
            </el-popover>
          </template>
        </lw-table>
      </div>
    </el-main>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
