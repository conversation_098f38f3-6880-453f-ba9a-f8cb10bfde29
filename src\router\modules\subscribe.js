export default {
  name: 'subscribe',
  path: '/subscribe',
  sort: 6,
  meta: {
    icon: 'el-icon-collection',
    type: 'menu',
    title: 'menu.subscribe.name',
    roles: ['cdp.subscribe']
  },
  redirect: '/subscribe/api',
  children: [
    {
      name: 'subscribeApi',
      path: '/subscribe/api',
      component: 'subscribe/api',
      sort: 0,
      meta: {
        icon: 'el-icon-sort',
        type: 'menu',
        title: 'menu.subscribe.api',
        roles: ['cdp.subscribe_api']
      },
      children: [
        {
          name: 'subscribeApiEdit',
          path: '/subscribe/apiEdit',
          component: 'subscribe/apiEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.subscribe.apiEdit',
            active: '/subscribe/api',
            hidden: true
          }
        }
      ]
    },
    {
      name: 'subscribeDatabase',
      path: '/subscribe/database',
      component: 'subscribe/database',
      sort: 1,
      meta: {
        icon: 'el-icon-coin',
        type: 'menu',
        title: 'menu.subscribe.database',
        roles: ['cdp.subscribe_database']
      },
      children: [
        {
          name: 'subscribeDatabaseEdit',
          path: '/subscribe/databaseEdit',
          component: 'subscribe/databaseEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.subscribe.databaseEdit',
            active: '/subscribe/database',
            hidden: true
          }
        }
      ]
    }
  ]
}
