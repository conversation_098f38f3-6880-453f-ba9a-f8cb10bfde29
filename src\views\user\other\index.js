import i18n from '@/locales'
import { computed, getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'userOther',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()

    const code = computed(() => route?.params?.id || '')
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        cdp_latest_version_Q_eq: true
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 获取显示字段
    const fieldList = ref([])
    const getFieldList = async () => {
      const BAR_DATA = $tool.data.get('BAR_DATA') || {}
      let item = BAR_DATA.insightList.find((item) => item.code === code.value)
      let res = item?.fields

      // 标题
      store.commit('updateViewTagsTitle', item?.label)

      // 搜索条件&表头
      state.searchOptions = []
      state.tableHeaders = []

      // 过滤字段
      res = res.filter((item) => {
        return item?.visualSetting?.detailDisplay
      })

      res?.forEach((item) => {
        let key = i18n.locale == 'en-us' ? 'viewFieldNameEn' : 'viewFieldNameCn'
        let aliasName = item?.[key] || item.fieldAliasName
        if (item.visualSetting.useForSearch) {
          let config = {
            label: aliasName,
            prop: `${item.fieldName}_Q_${item.visualSetting.operator}`,
            renderType: item.fieldType.toLowerCase() === 'date' ? 'dateRange' : 'input'
          }
          if (item.fieldType.toLowerCase() === 'date') {
            config.startPlaceholder = `${aliasName}${t('userOther.kaiShiShiJian')}`
            config.endPlaceholder = `${aliasName}${t('userOther.jieShuShiJian')}`
          }
          state.searchOptions.push(config)
        }
        state.tableHeaders.push({
          title: aliasName,
          dataIndex: item.fieldName,
          align: 'left',
          minWidth: item.fieldType == 'DATE' ? '180' : 60 + aliasName.length * 12,
          tooltip: true,
          date: item.fieldType == 'DATE'
        })
      })
      if (state.tableHeaders.length > 0) {
        state.tableHeaders.push({
          title: t('btn.operation'),
          width: '80',
          fixed: 'right',
          ellipsis: true,
          operation: [{ clickFun: (item) => edit(item, 'view'), label: t('btn.view'), auth: ['cdp.user_other.view'] }]
        })
      }
    }

    onMounted(() => {
      state.loading = true
      Promise.all([getFieldList()])
        .then(() => {
          getTableData()
        })
        .finally(() => {
          state.loading = false
        })
    })

    const getTableData = (page = 0) => {
      let params = {
        pageIndex: page,
        pageSize: state.searchParams.size,
        expression: $expression(state.searchParams)
      }
      $api.userOther.page(params, code.value).then((res) => {
        state.tableData = res.content
        state.currentPage = page
        state.totalCount = res.totalElements
      })
    }

    const reset = () => {
      state.searchParams = { cdp_latest_version_Q_eq: true }
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.cdp_id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/user/portraitEdit', query })
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      search,
      edit
    }
  }
}
