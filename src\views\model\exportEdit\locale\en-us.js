export default {
  modelExportEdit: {
    jiBenXinXi: 'Basic Information',
    jieKouMingCheng: 'API Name',
    qingShuRu: 'Please enter',
    ZNWXXZMSZHXH: 'Only lowercase letters, numbers and underscores allowed',
    CDBNCGGZF: 'Maximum 20 characters',
    jie<PERSON><PERSON><PERSON>hongWen: 'Display Name (Chinese)',
    ZCZYWJSZBZCT:
      'Supports Chinese/English characters and numbers. Special characters not allowed. Max 20 chars. Must be unique. Field validation occurs on save.',
    cunChuMoXing: 'Storage Model',
    qingXuanZe: 'Please select',
    KXZCCMXZYJLD: 'Select from existing tables in storage models',
    chuanShuMoXing: 'Transfer Model',
    KXZCSMXZYJLD: 'Select from existing tables in transfer models',
    shiFouYongYuDaoChu: 'Enable for Export',
    shi: 'Yes',
    fou: 'No',
    GXHGC<PERSON>MXKYYZ:
      'When enabled, this query model will be available for export in Data Insights and User Portrait modules. Disabled by default.',
    ying<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Field Mapping',
    baoCunChengGong: 'Saved successfully'
  }
}
