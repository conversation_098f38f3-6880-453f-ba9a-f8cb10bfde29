<template>
  <div class="dict-edit">
    <div class="dict-list">
      <el-select class="dict-select" v-model="selectedDictId" placeholder="请选择">
        <el-option v-for="item in dicts" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button-group>
        <el-button
          v-if="selectedDictId"
          type="primary"
          icon="el-icon-edit"
          text
          bg
          @click="editOrAddDict(selectedDictId)" />
        <el-button type="primary" icon="el-icon-plus" text bg @click="editOrAddDict()" />
      </el-button-group>
    </div>

    <!-- 新增编辑字典 -->
    <el-dialog v-model="dialogVisible" append-to-body :title="dictForm.id ? '编辑字典' : '新增字典'" width="500px">
      <lwFormMini v-model="dictForm" :config="configDict">
        <template #dictList>
          <lwTableForm :config="configDictTags" v-model="dictForm.dict" />
        </template>
      </lwFormMini>
      <template #footer>
        <el-popover v-if="!selectedDictId" placement="top" trigger="hover" width="160px">
          <template #reference>
            <el-button class="import-btn" :loading="importLoading">
              批量导入字典
              <input type="file" @change="handleImport" accept=".xlsx, .xls" />
            </el-button>
          </template>
          <div class="upload-list">
            <span>Excel导入模版</span>
            <a type="primary" link href="excel/dictImport.xlsx" target="_blank" download="字典导入模版.xlsx">下载</a>
          </div>
        </el-popover>
        <el-button @click="deleteDict">删字典集</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveDict">{{ $t('btn.save') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted, getCurrentInstance, watch } from 'vue'
import { ElMessageBox } from 'element-plus'
import { parseExcel } from '@/utils/exportExcel'

export default {
  name: 'lwDictEdit',
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    }
  },
  emits: ['update:modelValue', 'getDicts'],
  setup(props, { emit }) {
    const {
      proxy: { $api, $tool, t }
    } = getCurrentInstance()

    const selectedDictId = ref(props.modelValue)
    const dicts = ref([])
    const dialogVisible = ref(false)
    const dictForm = ref({})

    const getDicts = async () => {
      const res = await $api.dict.list({
        expression: `tenantId eq ${$tool.data.get('tenantId')}`
      })
      dicts.value = res.map((item) => ({
        label: item.name,
        value: item.id,
        ...item
      }))

      emit('getDicts')
    }

    const editOrAddDict = (id) => {
      let item = dicts.value.find((item) => item.value == id) || {
        dict: {},
        name: '',
        tenantId: $tool.data.get('tenantId')
      }

      if (!Array.isArray(item?.dict)) {
        item.dict = Object.keys(item.dict).map((key) => ({
          value: key,
          label: item.dict[key]
        }))
      }

      dictForm.value = JSON.parse(JSON.stringify(item))
      dialogVisible.value = true
    }

    const saveDict = async () => {
      let form = JSON.parse(JSON.stringify(dictForm.value))
      form.dict = form.dict.reduce((acc, { label, value }) => {
        acc[value] = label
        return acc
      }, {})
      await $api.dict[dictForm.value?.id ? 'edit' : 'add'](form)
      await getDicts()
      dialogVisible.value = false
    }

    const deleteDict = async () => {
      await ElMessageBox.confirm('确认删除整个字典集吗？', '提示', {
        type: 'warning',
        confirmButtonText: '删除',
        confirmButtonClass: 'el-button--danger'
      })
      await $api.dict.delete(dictForm.value?.id)
      selectedDictId.value = ''
      await getDicts()
      dialogVisible.value = false
    }

    watch(
      () => selectedDictId.value,
      (val) => {
        emit('update:modelValue', val)
      }
    )

    watch(
      () => props.modelValue,
      (val) => {
        selectedDictId.value = val
      }
    )

    const configDict = computed(() => ({
      labelWidth: '60px',
      labelPosition: 'top',
      size: 'small',
      formItems: [
        {
          label: '字典集名称',
          name: 'name',
          value: '',
          span: 24,
          component: 'input',
          options: {
            placeholder: '请输入'
          },
          rules: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        {
          label: '字典集列表',
          component: 'dictList'
        }
      ]
    }))

    const configDictTags = {
      treeProps: {
        children: 'fields'
      },
      rowKey: 'id',
      formItems: [
        {
          label: '名称',
          name: 'label',
          value: '',
          component: 'input',
          options: {
            placeholder: '请输入'
          },
          rules: [{ required: true, message: '不能为空', trigger: 'blur' }]
        },
        {
          label: '值',
          name: 'value',
          value: '',
          component: 'input',
          options: {
            placeholder: '请输入'
          },
          rules: [{ required: true, message: '不能为空', trigger: 'blur' }]
        },
        {
          label: '操作',
          component: 'operation',
          width: '60',
          fixed: 'right',
          options: {
            addDelete: [
              {
                icon: '',
                type: 'delete',
                label: '删除'
              }
            ]
          }
        }
      ]
    }

    onMounted(() => {
      getDicts()
    })

    // 批量导入
    const headers = [
      { name: '字典集名称(group)', value: 'group' },
      { name: '字典名称(title)', value: 'title' },
      { name: '字典值(value)', value: 'value' }
    ]
    const importLoading = ref(false)
    function transformData(data) {
      const groups = {}

      data.forEach((item) => {
        if (!groups[item.group]) {
          groups[item.group] = {}
        }
        groups[item.group][item.value] = item.title
      })

      return Object.keys(groups).map((group) => ({
        tenantId: $tool.data.get('tenantId'),
        name: group,
        dict: groups[group]
      }))
    }
    // 批量导入
    const handleImport = async (event) => {
      const file = event.target.files[0]
      if (!file) return
      importLoading.value = true
      let exl = await parseExcel(file, headers)
      let list = transformData(exl)
      await Promise.all(list.map((dict) => $api.dict.add(dict)))
      event.target.value = null
      await getDicts()
      importLoading.value = false
      dialogVisible.value = false
    }

    return {
      selectedDictId,
      dicts,
      dialogVisible,
      dictForm,
      editOrAddDict,
      saveDict,
      deleteDict,
      configDict,
      configDictTags,
      importLoading,
      handleImport
    }
  }
}
</script>

<style lang="scss" scoped>
.dict-edit {
  width: 100%;
  .dict-list {
    display: flex;
    align-items: center;
    width: 100%;
    .dict-select {
      width: 100%;
    }
  }
  .el-button-group {
    display: flex;
    align-items: center;
  }
}
.import-btn {
  position: relative;
  input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    opacity: 0;
    cursor: pointer;
  }
}
</style>
