<template>
  <el-container>
    <el-main>
      <el-row gutter="15">
        <el-col v-if="showTime" :span="24">
          <div class="top-body">
            <div class="title">{{ tagName }}</div>
            <div class="input-time">
              <el-button-group>
                <el-button @click="setChartTime('lastWeek')" :type="activeTimeRange === 'lastWeek' ? 'primary' : ''"
                  >{{$t('tagStatistics.jinYiZhou')}}</el-button
                >
                <el-button @click="setChartTime('lastMonth')" :type="activeTimeRange === 'lastMonth' ? 'primary' : ''"
                  >{{$t('tagStatistics.jinYiYue')}}</el-button
                >
                <el-button
                  @click="setChartTime('lastThreeMonth')"
                  :type="activeTimeRange === 'lastThreeMonth' ? 'primary' : ''"
                  >{{$t('tagStatistics.jinSanYue')}}</el-button
                >
              </el-button-group>
              <el-date-picker
                v-model="chartTime"
                type="daterange"
                style="max-width: 300px"
                :clearable="false"
                :range-separator="$t('tagStatistics.dao')"
                :start-placeholder="$t('tagStatistics.kaiShiShiJian')"
                :end-placeholder="$t('tagStatistics.jieShuShiJian')"
                value-format="YYYY-MM-DD"
                @change="handleTimeChange" />
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="card">
            <el-statistic :title="$t('tagStatistics.jinRiRenShu')" :value="dataForm.dayNum" />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="card">
            <el-statistic :title="$t('tagStatistics.pingJunRenShu')" :value="dataForm.averageNum" />
          </div>
        </el-col>
        <el-col :span="24">
          <div class="card last">
            <!-- eChart 折线图 -->
            <lwBiChartItem height="calc(100vh - 405px)" :rawData="chartOption" :isBi="false" />
          </div>
        </el-col>
      </el-row>
    </el-main>
    <el-footer class="footer-body">
      <el-button @click="close">{{$t('tagStatistics.fanHui')}}</el-button>
    </el-footer>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
