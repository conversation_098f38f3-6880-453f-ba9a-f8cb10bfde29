// 导入 tool 工具类
import tool from '@/utils/tool'

export default {
  state: {
    status: false
  },
  mutations: {
    // 修改 SET_USER mutation
    SET_USER(state, row) {
      // 将 row 中的属性逐个赋值给 state
      Object.keys(row).forEach((key) => {
        state[key] = row[key]
      })
    },

    // 新增一个用于从本地缓存加载数据的 mutation
    LOAD_USER_FROM_LOCAL_STORAGE(state) {
      // 使用 tool.data.get("USER_INFO") 获取本地缓存数据
      const localUserData = tool.data.get('USER_INFO')

      // 如果本地缓存数据存在，将其属性逐个赋值给 state
      if (localUserData) {
        Object.keys(localUserData).forEach((key) => {
          state[key] = localUserData[key]
        })
      }
    },

    // 设置状态
    setStatus(state, row) {
      state.status = row
    }
  }
}
