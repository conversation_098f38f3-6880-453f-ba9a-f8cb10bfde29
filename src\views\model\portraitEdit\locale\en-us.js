export default {
  modelPortraitEdit: {
    keHu: 'Customer',
    xing<PERSON><PERSON>: 'Behavior',
    puTong: 'General',
    jiBenXinXi: 'Basic Information',
    huaXiangMingCheng: 'Portrait Name',
    qingShuRu: 'Please Enter',
    hua<PERSON>iang<PERSON>ian<PERSON><PERSON>: 'Portrait Code',
    shu<PERSON>uJueSe: 'Data Role',
    qingXuanZe: 'Please Select',
    xingWeiShuJu: 'Behavior Data',
    yeMianLianJie: 'H5 Page Link',
    miaoShuXinXi: 'Description',
    keHuMoXingPeiZhi: 'Customer Model Configuration',
    baoCunChengGong: 'Save Successful',
    yongHuBiaoShiZhi: 'User Identifier Value',
    yongHuBiaoShiLeiXing: 'User Identifier Type',
    pei<PERSON>hi: 'Configure',
    fuZhi: 'Copy',
    ziDuan: 'Field Key',
    xianShiZhongWen: 'Display Chinese',
    zhongWenMing: 'Chinese Name',
    xianShiYingWen: 'Display English',
    yingWenMing: 'English Name',
    QXZYBJDZD: 'Please select the field to edit',
    xiang<PERSON>ing<PERSON>ian<PERSON>hi: 'Detail Display',
    lieBiaoXianShi: 'List Display',
    zhanShi: 'SHA Display',
    zhiJieZhanShi: 'Direct Display',
    buZhanShi: 'No Display',
    yanMaZhanShi: 'Mask Display',
    yanMaLeiXing: 'Mask Type',
    shouJiYanMa: 'Phone Mask',
    zuoJiYanMa: 'Landline Mask',
    diZhiYanMa: 'Address Mask',
    ziDingYiYanMa: 'Custom Mask',
    yanMaQiShiWeiZhi: 'Mask Start Position',
    yanMaWeiShu: 'Mask Digits',
    yongYuSouSuo: 'For Search',
    yunXuSouSuo: 'Allow Search',
    jinZhiSouSuo: 'Prohibit Search',
    souSuoFangShi: 'Search Method',
    wanQuanPiPei: 'Exact Match',
    moHuSouSuo: 'Fuzzy Search',
    xiaoYuShuRuZhi: 'Less Than Input',
    daYuShuRuZhi: 'Greater Than Input',
    quJian: 'Range',
    xianShiLeiXing: 'Display Type',
    shiFouHuanHang: 'Line Break',
    yunXuHuanHang: 'Allow Line Break',
    jinZhiHuanHang: 'Prohibit Line Break',
    riQiGeShi: 'Date Format',
    SRGJZJXGL: 'Enter keywords to filter',
    bianJi: 'JSON Edit',
    moXingLieBiao: 'Model List',
    quanXuan: 'Select All',
    quXiaoQuanXuan: 'Deselect All',
    ziDuanPeiZhi: 'Field Configuration',
    QJSXSBNTZCCZ: 'Please paste the JSON to be identified here. Note: No punctuation at the end, such as commas'
  }
}
