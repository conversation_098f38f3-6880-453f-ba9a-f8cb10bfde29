<template>
  <VAceEditor v-model:value="dataForm"
              lang="json"
              theme="monokai"
              style="height: 100%; width: 100%;"
              @input="onClick" />
</template>

<script>
import {VAceEditor} from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-json'
import 'ace-builds/src-noconflict/theme-monokai'

export default {
  components: {VAceEditor},
  data() {
    return {
      dataDrawer: true,
      dataForm: '请输入'
    }
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    }
  },
  watch: {
    modelValue: {
      handler(val) {
        this.dataForm = val
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 提交
    onClick(val) {
      this.$emit('update:modelValue', this.dataForm)
    }
  }
}
</script>

<style lang="scss">
.jsoneditor {
  border-color: var(--el-color-primary) !important;
}
.jsoneditor-menu {
  background-color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
}
.jsoneditor-poweredBy {
  display: none !important;
}
:deep(.ace-jsoneditor.ace_editor) {
  height: 600px;
}
</style>