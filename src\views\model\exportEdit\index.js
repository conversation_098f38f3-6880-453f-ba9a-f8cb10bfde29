import lwMappingEdit from '@/components/lwMappingEdit/index.vue'
import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'modelExportEdit',
  components: {
    lwMappingEdit
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const dataForm = ref({
      name: '',
      aliasName: '',
      mappingList: []
    })
    const dataFormRef = ref(null)
    const mappingEditRef = ref(null)
    const loading = ref(false)

    // 存储模型
    const persistentlList = ref([])
    const getPersistentList = async () => {
      let res = await $api.modelPersistent.list()
      persistentlList.value = res.map((item) => ({ label: item.aliasName, value: item.id }))
    }

    // 传输模型
    const transmissionList = ref([])
    const getTransmissionList = async () => {
      let res = await $api.modelTransmission.list()
      transmissionList.value = res.map((item) => ({ label: item.aliasName, value: item.id }))
    }

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('modelExportEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('modelExportEdit.jieKouMingCheng'),
            name: 'name',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelExportEdit.qingShuRu')
            },
            tips: t('modelExportEdit.ZNWXXZMSZHXH'),
            rules: [
              { required: true, message: t('modelExportEdit.qingShuRu'), trigger: 'blur' },
              { pattern: /^[a-z0-9_]+$/, message: t('modelExportEdit.ZNWXXZMSZHXH'), trigger: 'blur' },
              { max: 20, message: t('modelExportEdit.CDBNCGGZF'), trigger: 'blur' }
            ]
          },
          {
            label: t('modelExportEdit.jieKouZhongWen'),
            name: 'aliasName',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('modelExportEdit.qingShuRu')
            },
            tips: t('modelExportEdit.ZCZYWJSZBZCT'),
            rules: [{ required: true, message: t('modelExportEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelExportEdit.cunChuMoXing'),
            name: 'persistentModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('modelExportEdit.qingXuanZe'),
              items: persistentlList.value
            },
            tips: t('modelExportEdit.KXZCCMXZYJLD'),
            rules: [{ required: true, message: t('modelExportEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('modelExportEdit.chuanShuMoXing'),
            name: 'transferModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('modelExportEdit.qingXuanZe'),
              items: transmissionList.value
            },
            tips: t('modelExportEdit.KXZCSMXZYJLD'),
            rules: [{ required: true, message: t('modelExportEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('modelExportEdit.shiFouYongYuDaoChu'),
            name: 'enableExport',
            value: true,
            span: 3,
            component: 'switch',
            options: {
              activeText: t('modelExportEdit.shi'),
              inactiveText: t('modelExportEdit.fou')
            },
            tips: t('modelExportEdit.GXHGCXMXKYYZ')
          },
          { label: t('modelExportEdit.yingShePeiZhi'), component: 'divider' },
          { component: 'mappingEdit' }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.modelExport.info({ id: id.value })
      dataForm.value = res
    }

    onMounted(() => {
      getPersistentList()
      getTransmissionList()
      if (id.value) {
        fetchDetail()
      }
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          await $api.modelExport[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('modelExportEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/model/export', 'modelExport')
    }

    return {
      dataForm,
      mappingEditRef,
      dataFormRef,
      config,
      loading,
      isView,
      save,
      close
    }
  }
}
