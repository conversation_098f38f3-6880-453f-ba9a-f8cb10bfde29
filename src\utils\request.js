import sysConfig from '@/config'
import tool from '@/utils/tool'
import axios from 'axios'
import { ElNotification } from 'element-plus'

axios.defaults.baseURL = ''
let isBox = true

axios.defaults.timeout = sysConfig.TIMEOUT
// HTTP request 拦截器
axios.interceptors.request.use(
  (config) => {
    let token = tool.data.get('TOKEN')
    if (token) {
      config.headers[sysConfig.TOKEN_NAME] = token
    }

    if (!sysConfig.REQUEST_CACHE && config.method === 'get') {
      config.params = config.params || {}
      config.params['_'] = new Date().getTime()
    }
    Object.assign(config.headers, sysConfig.HEADERS)

    // 替换请求路径中的占位符
    const tenantId = tool.data.get('tenantId')
    const buCode = tool.data.get('buCode')
    const BAR_DATA = tool.data.get('BAR_DATA')
    if (tenantId) {
      config.headers['tenantid'] = tenantId
      config.url = config.url.replace(/{tenantId}/g, tenantId)
    }
    if (buCode) {
      config.url = config.url.replace(/{buCode}/g, buCode)
    }
    if (BAR_DATA?.collectionId) {
      config.url = config.url.replace(/{collectionId}/g, BAR_DATA?.collectionId)
    }
    if (BAR_DATA?.collectionName) {
      config.url = config.url.replace(/{collectionName}/g, BAR_DATA?.collectionName)
    }
    if (BAR_DATA?.collectionName) {
      config.url = config.url.replace(/{collectionName}/g, BAR_DATA?.collectionName)
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// HTTP response 拦截器
axios.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (axios.isCancel(error)) {
      // 为了终结promise链 就是实际请求 不会走到.catch(rej=>{});这样就不会触发错误提示之类了。
      return new Promise(() => {})
    } else {
      if (error?.response?.data?.message == 'Need authorization.') {
        ElNotification.error(error.response.data.message)
        ;(window.location.href = location.origin + '/#/passport-login'), '_blank'
      } else if (error?.response?.data) {
        ElNotification.error(error.response.data.message)
        return Promise.reject(error.response.data)
      }
    }
  }
)

let http = {
  /** get 请求
   * @param  {接口地址} url
   * @param  {请求参数} params
   * @param  {参数} config
   */
  get: function (url, params = {}, config = {}) {
    return new Promise((resolve, reject) => {
      axios({
        method: 'get',
        url: url,
        params: params,
        ...config
      })
        .then((response) => {
          resolve(response?.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  /** post 请求
   * @param  {接口地址} url
   * @param  {请求参数} data
   * @param  {参数} config
   */
  post: function (url, data = {}, config = {}) {
    return new Promise((resolve, reject) => {
      axios({
        method: 'post',
        url: url,
        data: data,
        ...config
      })
        .then((response) => {
          resolve(response?.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  /** put 请求
   * @param  {接口地址} url
   * @param  {请求参数} data
   * @param  {参数} config
   */
  put: function (url, data = {}, config = {}) {
    return new Promise((resolve, reject) => {
      axios({
        method: 'put',
        url: url,
        data: data,
        ...config
      })
        .then((response) => {
          resolve(response?.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  /** patch 请求
   * @param  {接口地址} url
   * @param  {请求参数} data
   * @param  {参数} config
   */
  patch: function (url, data = {}, config = {}) {
    return new Promise((resolve, reject) => {
      axios({
        method: 'patch',
        url: url,
        data: data,
        ...config
      })
        .then((response) => {
          resolve(response?.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  /** delete 请求
   * @param  {接口地址} url
   * @param  {请求参数} data
   * @param  {参数} config
   */
  delete: function (url, data = {}, config = {}) {
    return new Promise((resolve, reject) => {
      axios({
        method: 'delete',
        url: url,
        data: data,
        ...config
      })
        .then((response) => {
          resolve(response.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  /** jsonp 请求
   * @param  {接口地址} url
   * @param  {JSONP回调函数名称} name
   */
  jsonp: function (url, name = 'jsonp') {
    return new Promise((resolve) => {
      let script = document.createElement('script')
      let _id = `jsonp${Math.ceil(Math.random() * 1000000)}`
      script.id = _id
      script.type = 'text/javascript'
      script.src = url
      window[name] = (response) => {
        resolve(response)
        document.getElementsByTagName('head')[0].removeChild(script)
        try {
          delete window[name]
        } catch (e) {
          window[name] = undefined
        }
      }
      document.getElementsByTagName('head')[0].appendChild(script)
    })
  }
}

export default {
  ...axios,
  ...http
}
