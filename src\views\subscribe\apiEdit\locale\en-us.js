export default {
  subscribeApiEdit: {
    jiBenXinXi: 'Basic Information',
    mingCheng: 'Name',
    qingShuRu: 'Please Enter',
    bianMa: 'Code',
    ZNWXXZMSZHXH: 'Only lowercase letters, numbers or underscores allowed',
    CDBNCGGZF: 'Length cannot exceed 20 characters',
    wanZhengLuJing: 'Complete Path',
    chuanShuMoXing: 'Transfer Model',
    qingXuanZe: 'Please Select',
    KXZCSMXZYJLD: 'Selectable data tables already established in the transfer model',
    shuJuTuiSongMoXing: 'Data Push Model',
    cunChuMoXing: 'Storage Model',
    dingYueMoXing: 'Subscription Model',
    zhuangTaiMa: 'HTTP Status Code',
    shiFouJiLuRiZhi: 'Enable Logging',
    jiLu: 'Enable',
    buJiLu: 'Disable',
    SFXYJLRZKQHJ: 'Whether to enable logging, logs will be recorded when enabled',
    shiFouXuYaoChongShi: 'Retry Required',
    xu<PERSON><PERSON>: 'Yes',
    bu<PERSON><PERSON><PERSON><PERSON>: 'No',
    zuiDa<PERSON>hongShiCiShu: 'Maximum Retry Count',
    chongShiShiJianJianGe: 'Retry Interval',
    miao: 'Seconds',
    beiZhu: 'Remarks',
    yingShePeiZhi: 'Mapping Configuration',
    baoCunChengGong: 'Successfully Saved'
  }
}
