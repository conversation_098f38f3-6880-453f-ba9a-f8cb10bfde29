import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'tags',
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        delete_ne: true
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      if (type === 'add') {
        dataForm.value.parent = item.id
        dataForm.value.name = ''
      }
      if (type === 'edit') {
        dataForm.value = item
      }
      dialogVisible.value = true
    }

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('tags.fenLeiMingCheng'),
          prop: 'name_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('tags.fenLeiMingCheng'), dataIndex: 'name', minWidth: '120', tooltip: true, align: 'left' },
        { title: t('tags.beiZhu'), dataIndex: 'description', minWidth: '220', tooltip: true },
        { title: t('tags.chuangJianShiJian'), dataIndex: 'createTime', width: '180', date: true }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '180',
        fixed: 'right',
        ellipsis: true,
        operation: [
          { clickFun: (item) => edit(item, 'add'), label: t('btn.add'), auth: ['cdp.tags.create'] },
          { clickFun: (item) => edit(item, 'edit'), label: t('btn.edit'), auth: ['cdp.tags.modify'] },
          { clickFun: del, label: t('btn.delete'), auth: ['cdp.tags.delete'] }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    function buildTree(data, parentId = '0') {
      return data
        .filter((item) => item.parent === parentId)
        .map((item) => ({
          ...item,
          children: buildTree(data, item.id)
        }))
    }

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        expression: $expression(state.searchParams)
      }

      $api.tags
        .list(params)
        .then((res) => {
          state.tableData = buildTree(res)
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = {}
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.tags.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    // 新增编辑
    let tenantId = $tool.data.get('tenantId')
    const dialogVisible = ref(false)
    const dataForm = ref({
      name: '',
      parent: '0',
      tenantId,
      description: ''
    })
    const dataFormRef = ref(null)
    const formConfig = computed(() => {
      return {
        labelWidth: '85px',
        labelPosition: 'right',
        formItems: [
          {
            label: t('tags.shangJiFenLei'),
            name: 'parent',
            value: '',
            span: 12,
            component: 'treeSelect',
            options: {
              disabled: true,
              placeholder: t('tags.qingXuanZe'),
              props: { value: 'id', label: 'name' },
              items: [
                {
                  id: '0',
                  name: t('tags.yiJiFenLei'),
                  children: state.tableData
                }
              ]
            },
            rules: [{ required: true, message: t('tags.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('tags.mingCheng'),
            name: 'name',
            value: '',
            component: 'input',
            span: 24,
            options: {
              maxlength: '20',
              placeholder: t('tags.qingShuRu')
            },
            rules: [{ required: true, message: t('tags.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('tags.miaoShu'),
            name: 'description',
            value: '',
            component: 'input',
            span: 24,
            options: {
              type: 'textarea',
              maxlength: '200',
              placeholder: t('tags.qingShuRu')
            }
          }
        ]
      }
    })
    const handleClose = () => {
      dataForm.value = {
        name: '',
        parent: '0',
        tenantId,
        description: ''
      }

      dialogVisible.value = false
    }
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          await $api.tags[dataForm.value.id ? 'edit' : 'add'](dataForm.value).then(() => {
            getTableData(state.searchParams.page - 1)
            ElMessage({ type: 'success', message: t('tags.baoCunChengGong') })
            handleClose()
          })
          dialogVisible.value = false
        }
      })
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      dialogVisible,
      dataForm,
      dataFormRef,
      formConfig,
      handleClose,
      save
    }
  }
}
