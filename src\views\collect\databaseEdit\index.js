import lwMappingEdit from '@/components/lwMappingEdit/index.vue'
import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'collectDatabaseEdit',
  components: {
    lwMappingEdit
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const dataForm = ref({
      name: '',
      aliasName: '',
      mappingList: []
    })
    const dataFormRef = ref(null)
    const mappingEditRef = ref(null)
    const loading = ref(false)

    // 存储模型
    const persistentlList = ref([])
    const getPersistentList = async () => {
      let res = await $api.modelPersistent.list()
      persistentlList.value = res.map((item) => ({ label: item.aliasName, value: item.id }))
    }

    // 传输模型
    const transmissionList = ref([])
    const getTransmissionList = async () => {
      let res = await $api.modelTransmission.list()
      transmissionList.value = res.map((item) => ({ label: item.aliasName, value: item.id }))
    }

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('collectDatabaseEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('collectDatabaseEdit.jieKouMingCheng'),
            name: 'aliasName',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('collectDatabaseEdit.jieKouBianMa'),
            name: 'name',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            tips: t('collectDatabaseEdit.ZNWXXZMSZHXH'),
            rules: [
              { required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' },
              { pattern: /^[a-z0-9_]+$/, message: t('collectDatabaseEdit.ZNWXXZMSZHXH'), trigger: 'blur' },
              { max: 20, message: t('collectDatabaseEdit.CDBNCGGZF'), trigger: 'blur' }
            ]
          },
          {
            label: t('collectDatabaseEdit.jieKouShiQu'),
            name: 'defaultTimezone',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('collectDatabaseEdit.qingXuanZe'),
              items: timeList.value
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('collectDatabaseEdit.chuanShuMoXing'),
            name: 'transferModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('collectDatabaseEdit.qingXuanZe'),
              items: transmissionList.value
            },
            tips: t('collectDatabaseEdit.KXZCSMXZYJLD'),
            rules: [{ required: true, message: t('collectDatabaseEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('collectDatabaseEdit.shiFouJiLuRiZhi'),
            name: 'needLogging',
            value: true,
            span: 6,
            component: 'switch',
            options: {
              activeText: t('collectDatabaseEdit.jiLu'),
              inactiveText: t('collectDatabaseEdit.buJiLu')
            },
            tips: t('collectDatabaseEdit.SFXYJLRZKQHJ')
          },
          {
            label: t('collectDatabaseEdit.shuJuShiFouCunChu'),
            name: 'needPersistent',
            value: false,
            span: 6,
            component: 'switch',
            options: {
              activeText: t('collectDatabaseEdit.cunChu'),
              inactiveText: t('collectDatabaseEdit.buCunChu')
            },
            tips: t('collectDatabaseEdit.KQHXXZCCMXHZ')
          },
          {
            label: t('collectDatabaseEdit.cunChuMoXing'),
            name: 'persistentModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('collectDatabaseEdit.qingXuanZe'),
              items: persistentlList.value
            },
            tips: t('collectDatabaseEdit.KXZCCMXZYJLD'),
            rules: [{ required: true, message: t('collectDatabaseEdit.qingXuanZe'), trigger: 'blur' }],
            hideHandle: '!$.needPersistent'
          },
          {
            label: t('collectDatabaseEdit.beiZhu'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              type: 'textarea',
              placeholder: t('collectDatabaseEdit.qingShuRu')
            }
          },
          { label: t('collectDatabaseEdit.shuJuKu'), component: 'divider' },
          {
            label: t('collectDatabaseEdit.shuJuKuMingCheng'),
            name: 'databaseConfigModel.database',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('collectDatabaseEdit.shuJuKuLeiXing'),
            name: 'databaseConfigModel.databaseType',
            value: '',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('collectDatabaseEdit.qingXuanZe'),
              items: [
                { label: 'mysql', value: 'mysql' },
                { label: 'sqlserver', value: 'sqlserver' },
                { label: 'oracle', value: 'oracle' }
              ]
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('collectDatabaseEdit.biaoMing'),
            name: 'databaseConfigModel.tableName',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('collectDatabaseEdit.zhuJian'),
            name: 'databaseConfigModel.primaryKey',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('collectDatabaseEdit.yongHuMing'),
            name: 'databaseConfigModel.userName',
            value: '',
            span: 6,
            component: 'input',
            options: {
              autocomplete: 'new-passwords',
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('collectDatabaseEdit.miMa'),
            name: 'databaseConfigModel.password',
            value: '',
            span: 6,
            component: 'input',
            options: {
              autocomplete: 'new-passwords',
              type: 'password',
              showPassword: true,
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('collectDatabaseEdit.guoLüTiaoJian'),
            name: 'databaseConfigModel.expression',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('collectDatabaseEdit.qingShuRu')
            }
          },
          {
            label: t('collectDatabaseEdit.paiXu'),
            name: 'databaseConfigModel.orderby',
            value: 0,
            span: 6,
            component: 'number',
            options: {
              min: 0
            }
          },
          {
            label: t('collectDatabaseEdit.zhuJi'),
            name: 'databaseConfigModel.host',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('collectDatabaseEdit.qingShuRu')
            }
          },
          {
            label: t('collectDatabaseEdit.duanKou'),
            name: 'databaseConfigModel.port',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('collectDatabaseEdit.qingShuRu')
            }
          },
          {
            label: t('collectDatabaseEdit.shiFouKaiQiTongDao'),
            name: 'databaseConfigModel.enableSSH',
            value: false,
            span: 6,
            component: 'switch'
          },
          {
            label: t('collectDatabaseEdit.diZhi'),
            name: 'databaseConfigModel.databaseSSHInfo.sshHost',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' }],
            hideHandle: '!$.databaseConfigModel.enableSSH'
          },
          {
            label: t('collectDatabaseEdit.duanKou'),
            name: 'databaseConfigModel.databaseSSHInfo.sshPort',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' }],
            hideHandle: '!$.databaseConfigModel.enableSSH'
          },
          {
            label: t('collectDatabaseEdit.zhangHao'),
            name: 'databaseConfigModel.databaseSSHInfo.sshUserName',
            value: '',
            span: 6,
            component: 'input',
            options: {
              autocomplete: 'new-passwords',
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' }],
            hideHandle: '!$.databaseConfigModel.enableSSH'
          },
          {
            label: t('collectDatabaseEdit.miMa'),
            name: 'databaseConfigModel.databaseSSHInfo.sshPassword',
            value: '',
            span: 6,
            component: 'input',
            options: {
              autocomplete: 'new-passwords',
              type: 'password',
              showPassword: true,
              placeholder: t('collectDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectDatabaseEdit.qingShuRu'), trigger: 'blur' }],
            hideHandle: '!$.databaseConfigModel.enableSSH'
          },
          { label: t('collectDatabaseEdit.yingShePeiZhi'), component: 'divider' },
          { component: 'mappingEdit' }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.collectDatabase.info({ id: id.value })
      dataForm.value = res
    }

    // 获取时区
    const timeList = ref([])
    const getTime = async () => {
      let res = await $api.collectApi.time({ id: id.value })
      timeList.value = res.map((item) => ({ label: item, value: item }))
    }

    onMounted(() => {
      loading.value = true
      Promise.all([getPersistentList(), getTransmissionList(), getTime()])
        .then(() => {
          if (id.value) {
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          await $api.collectDatabase[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('collectDatabaseEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/collect/database', 'collectDatabase')
    }

    return {
      dataForm,
      mappingEditRef,
      dataFormRef,
      config,
      loading,
      isView,
      save,
      close
    }
  }
}
