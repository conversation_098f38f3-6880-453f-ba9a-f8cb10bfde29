export default {
  name: 'user',
  path: '/user',
  sort: 3,
  meta: {
    icon: 'el-icon-user',
    type: 'menu',
    title: 'menu.user.name',
    roles: ['cdp.user']
  },
  redirect: '/user/portrait',
  children: [
    {
      name: 'userPortrait',
      path: '/user/portrait',
      component: 'user/portrait',
      sort: 1,
      meta: {
        icon: 'el-icon-postcard',
        type: 'menu',
        title: 'menu.user.portrait',
        roles: ['cdp.user_portrait']
      },
      children: [
        {
          name: 'userPortraitEdit',
          path: '/user/portraitEdit',
          component: 'user/portraitEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            hidden: true,
            active: '/user/portrait',
            title: 'menu.user.portraitEdit'
          }
        }
      ]
    },
    {
      name: 'userTag',
      path: '/user/tag',
      component: 'user/tag',
      sort: 1,
      meta: {
        icon: 'el-icon-collection-tag',
        type: 'menu',
        title: 'menu.user.tag',
        roles: ['cdp.user_tag']
      },
      children: [
        {
          name: 'userTagEdit',
          path: '/user/tagEdit',
          component: 'user/tagEdit',
          sort: 1,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            hidden: true,
            active: '/user/tag',
            title: 'menu.user.tagEdit'
          }
        },
        {
          name: 'userTagStatistics',
          path: '/user/tagStatistics',
          component: 'user/tagStatistics',
          sort: 1,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            hidden: true,
            active: '/user/tag',
            title: 'menu.user.tagStatistics'
          }
        },
        {
          name: 'userTagHistory',
          path: '/user/tagHistory',
          component: 'user/tagHistory',
          sort: 1,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            hidden: true,
            active: '/user/tag',
            title: 'menu.user.tagHistory'
          }
        }
      ]
    },
    {
      name: 'userMarking',
      path: '/user/marking',
      component: 'user/marking',
      sort: 1,
      meta: {
        icon: 'el-icon-pointer',
        type: 'menu',
        title: 'menu.user.marking',
        roles: ['cdp.user_marking']
      },
      children: [
        {
          name: 'userMarkingEdit',
          path: '/user/markingEdit',
          component: 'user/markingEdit',
          sort: 1,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            hidden: true,
            active: '/user/marking',
            title: 'menu.user.markingEdit'
          }
        }
      ]
    },
    {
      name: 'userCircle',
      path: '/user/circle',
      component: 'user/circle',
      sort: 1,
      meta: {
        icon: 'el-icon-notification',
        type: 'menu',
        title: 'menu.user.circle',
        roles: ['cdp.user_circle']
      },
      children: [
        {
          name: 'userCircleEdit',
          path: '/user/circleEdit',
          component: 'user/circleEdit',
          sort: 1,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            hidden: true,
            active: '/user/circle',
            title: 'menu.user.circleEdit'
          }
        },
        {
          name: 'userCircleHistory',
          path: '/user/circleHistory',
          component: 'user/circleHistory',
          sort: 1,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            hidden: true,
            active: '/user/circle',
            title: 'menu.user.circleHistory'
          }
        },
        {
          name: 'userCircleStatistics',
          path: '/user/circleStatistics',
          component: 'user/circleStatistics',
          sort: 1,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            hidden: true,
            active: '/user/circle',
            title: 'menu.user.circleStatistics'
          }
        }
      ]
    },
    {
      name: 'userOther',
      path: '/user/other/:id',
      component: 'user/other',
      sort: 1,
      meta: {
        icon: 'el-icon-price-tag',
        type: 'menu',
        hidden: true,
        title: 'menu.user.userOther'
      }
    }
  ]
}
