import config from '@/config'
import request from '@/utils/request'

export default {
  // 分页
  page: async (params) => {
    let url = `${config.API_URL}/cdp-portal/data_routing/view`
    return await request.get(url, params)
  },
  // 列表
  list: async (params) => {
    let url = `${config.API_URL}/cdp-portal/data_routing/list`
    return await request.get(url, params)
  },
  // 详情
  info: async (params) => {
    let url = `${config.API_URL}/cdp-portal/data_routing/${params.id}`
    return await request.get(url)
  },
  // 修改
  edit: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/data_routing`
    return await request.put(url, data)
  },
  // 新增
  add: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/data_routing`
    return await request.post(url, data)
  },
  // 删除
  delete: async (id) => {
    let url = `${config.API_URL}/cdp-portal/data_routing/${id}`
    return await request.delete(url)
  },
  // 时区
  time: async () => {
    let url = `${config.API_URL}/cdp-portal/constant/data-time-zones`
    return await request.get(url)
  }
}
