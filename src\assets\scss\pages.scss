/* USERCENTER */
.user-info {
  padding: 20px 40px;

  &-top {
    text-align: center;

    h2 {
      margin-top: 10px;
      font-size: 24px;
    }

    p {
      color: #999;
      margin-top: 5px;
    }

    button {
      margin-top: 10px;
    }
  }

  &-main {
    padding: 20px 0;

    li {
      list-style-type: none;
      line-height: 2;
      font-size: 14px;

      i {
        margin-right: 10px;
      }
    }
  }

  &-bottom {
    border-top: 1px solid #e6e6e6;

    h2 {
      font-size: 14px;
      margin: 15px 0;
    }
  }
}

/* static-table */
.static-table {
  border-collapse: collapse;
  width: 100%;
  font-size: 14px;
  margin-bottom: 45px;
  line-height: 1.5em;

  th {
    text-align: left;
    white-space: nowrap;
    color: #909399;
    font-weight: 400;
    border-bottom: 1px solid #dcdfe6;
    padding: 15px;
    max-width: 250px;
  }

  td {
    border-bottom: 1px solid #dcdfe6;
    padding: 15px;
    max-width: 250px;
    color: #606266;
  }
}

/* header-tabs */
.header-tabs {
  padding: 10px 0 0 0;
  display: block;
  border: 0 !important;
  height: 50px;
  background: none;

  .el-tabs__header {
    padding-left: 10px;
    margin: 0;
  }

  .el-tabs__content {
    display: none;
  }

  .el-tabs__nav {
    border-radius: 0 !important;
  }

  .el-tabs__item {
    font-size: 13px;

    &.is-active {
      background-color: var(--el-bg-color-overlay);
    }
  }
}

/* common-page */
.common-page {
  // 没有定义具体样式
}

.common-header-left {
  display: flex;
  align-items: center;
}

.common-header-logo {
  display: flex;
  align-items: center;

  img {
    height: 30px;
    margin-right: 10px;
    vertical-align: bottom;
  }

  label {
    font-size: 20px;
  }
}

.common-header-title {
  font-size: 16px;
  border-left: 1px solid var(--el-border-color-light);
  margin-left: 15px;
  padding-left: 15px;
}

.common-header-right {
  display: flex;
  align-items: center;

  a {
    font-size: 14px;
    color: var(--el-color-primary);
    cursor: pointer;

    &:hover {
      color: var(--el-color-primary-light-3);
    }
  }
}

.common-container {
  max-width: 1240px;
  margin: 30px auto;
}

.common-main {
  padding: 20px;

  .el-form {
    width: 500px;
    margin: 30px auto;
  }

  .el-steps {
    .el-step__title {
      font-size: 14px;
    }

    .el-step__icon {
      border: 1px solid;
    }
  }

  .yzm {
    display: flex;
    width: 100%;

    .el-button {
      margin-left: 10px;
    }
  }

  .link {
    color: var(--el-color-primary);
    cursor: pointer;

    &:hover {
      color: var(--el-color-primary-light-3);
    }
  }
}

// 空值
.no-data {
  display: flex;
  padding: 100px;
  justify-content: center;
  width: 100%;
  color: #999;
}

// 抽屉底部
.el-drawer {
  background-color: var(--el-color-white);

  &__header {
    background-color: var(--el-color-white);
    padding: 15px 20px;
    margin-bottom: 0;
  }

  &__footer {
    background-color: var(--el-color-white);
    padding: 8px 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
}
