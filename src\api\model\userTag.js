import config from '@/config'
import request from '@/utils/request'

export default {
  // 分页
  page: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag_group/{collectionId}`
    return await request.get(url, params)
  },
  // 列表
  list: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag/{collectionId}/list`
    return await request.get(url, params)
  },
  // 过滤
  view: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag/{collectionId}/list/view`
    return await request.get(url, params)
  },
  // 组列表
  listGroup: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag_group/{collectionId}/list`
    return await request.get(url, params)
  },
  // 详情
  info: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag/{collectionId}/${params.id}`
    return await request.get(url)
  },
  // 详情
  infoGroup: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag_group/{collectionId}/${params.id}`
    return await request.get(url)
  },
  // 修改
  edit: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/tag/{collectionId}`
    return await request.put(url, data)
  },
  // 修改
  editGroup: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/tag_group/{collectionId}`
    return await request.put(url, data)
  },
  // 新增
  add: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/tag/{collectionId}`
    return await request.post(url, data)
  },
  // 新增组
  addGroup: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/tag_group/{collectionId}`
    return await request.post(url, data)
  },
  // 删除
  delete: async (id) => {
    let url = `${config.API_URL}/cdp-portal/tag/{collectionId}/${id}`
    return await request.delete(url)
  },
  // 删除组
  deleteGroup: async (id) => {
    let url = `${config.API_URL}/cdp-portal/tag_group/{collectionId}/${id}/all_tag`
    return await request.delete(url)
  },
  // 标签分类
  tagType: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag/{collectionId}/list/match_count`
    return await request.get(url, params)
  },
  // 导出
  export: async (id, name) => {
    let url = `${config.API_URL}/cdp-portal/export/{collectionId}/${name}/${id}/tag`
    return await request.get(url)
  },
  // 批量快照
  batchSnapshot: async (data = {}) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task/{collectionId}/tag_snapshot`
    return await request.post(url, data)
  },
  // 批量计算
  batchCalculate: async (data = {}) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task/{collectionId}/tag_calculate`
    return await request.post(url, data)
  },
  // 快照统计
  snapshotStatistics: async (params) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionId}/tag_snapshot`
    return await request.get(url, params)
  },
  // 导出快照
  exportSnapshot: async (params) => {
    let url = `${config.API_URL}/cdp-portal/export/es/{collectionId}/tag_customer_snapshot`
    return await request.get(url, params)
  },
  // 标签列表
  tagList: async (params) => {
    let url = `${config.API_URL}/cdp-portal/tag/{collectionId}/list`
    return await request.get(url, params)
  }
}
