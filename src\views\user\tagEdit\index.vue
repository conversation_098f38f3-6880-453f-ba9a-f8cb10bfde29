<template>
  <el-container>
    <el-main>
      <lwFormMini ref="dataFormRef" :config="config" :isView="isView" v-model="dataForm" :loading="loading">
        <template #tagBtns>
          <div class="tag-btns">
            <el-radio-group
              v-if="(dataForm.type && dataForm.type != 'hand' && !id && !isView) || isTabAdd"
              v-model="editType[editableTagsValue]"
              size="small">
              <el-radio-button :label="$t('userTagEdit.puTongMoShi')" value="normal" />
              <el-radio-button :label="$t('userTagEdit.zhuanJiaMoShi')" value="exp" />
            </el-radio-group>
            <el-button
              type="primary"
              v-if="dataForm.type != 'numerical' && !isView"
              plain
              @click="addTags"
              size="small"
              >{{ $t('userTagEdit.xinZengBiaoQian') }}</el-button
            >
          </div>
        </template>
        <template #tagList>
          <el-tabs
            v-if="editableTagsValue.length > 0"
            v-model="editableTagsValue"
            type="border-card"
            :closable="dataForm.type != 'numerical' && !isView"
            @tab-remove="removeTag"
            style="width: 100%">
            <el-tab-pane
              v-for="(item, index) in editableTags"
              :key="item.id"
              :label="item.name || this.$t('userTagEdit.biaoQianZhiMingCheng')"
              :name="item.id">
              <TagEditItem v-model="editableTags[index]" :editType="editType[editableTagsValue]" />
            </el-tab-pane>
          </el-tabs>

          <el-empty v-else :description="$t('userTagEdit.zanWuBiaoQianDingYi')" style="margin: 0 auto" />
        </template>
      </lwFormMini>
    </el-main>
    <el-footer class="footer-body">
      <el-button @click="close">{{ $t('btn.close') }}</el-button>
      <el-button v-if="!isView" type="primary" @click="save">{{ $t('btn.save') }}</el-button>
    </el-footer>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
