import config from "@/config"
import request from '@/utils/request'

export default {
	// 获取分页列表
	page: async (params) => {
		let url = `${config.API_MOCK}/page`
		return await request.get(url, params)
	},
	// SKU获取分页列表
	sku: async (params) => {
		let url = `${config.API_MOCK}/sku`
		return await request.get(url, params)
	},

	// 获取模拟远程菜单
	menu: async (params) => {
		let url = `${config.API_MOCK}/38777590`
		return await request.get(url, params)
	},
	// 查看页面模拟数据
	view: async (params) => {
		let url = `${config.API_MOCK}/view`
		return await request.get(url, params)
  },
  // 修改
  edit: async (data = {}) => {
    let url = `${config.API_MOCK}/view`
    return await request.get(url, data)
	}
}
