import lwFilterList from '@/components/lwFilterList/index.vue'
import lwMappingEdit from '@/components/lwMappingEdit/index.vue'
import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, provide, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'arrangeRedirectEdit',
  components: {
    lwMappingEdit,
    lwFilterList
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()
    const BAR_DATA = $tool.data.get('BAR_DATA')
    const tenantId = $tool.data.get('tenantId')

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const dataForm = ref({
      destCollectionId: BAR_DATA.collectionId,
      sourceCollectionId: BAR_DATA.collectionId,
      tenantId,
      dataFilter: {},
      mappingList: []
    })
    const dataFormRef = ref(null)
    const mappingEditRef = ref(null)
    const loading = ref(false)

    // 存储模型
    const persistentlList = ref([])
    const getPersistentList = async () => {
      let res = await $api.modelPersistent.list()
      persistentlList.value = res.map((item) => ({ label: item.aliasName, value: item.id, fields: item.fields }))
    }

    // 传输模型
    const transmissionList = ref([])
    const getTransmissionList = async () => {
      let res = await $api.modelTransmission.list()
      transmissionList.value = res.map((item) => ({ label: item.aliasName, value: item.id, fields: item.fields }))
    }

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('arrangeRedirectEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('arrangeRedirectEdit.mingCheng'),
            name: 'name',
            value: '',
            span: 24,
            component: 'input',
            options: {
              placeholder: t('arrangeRedirectEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('arrangeRedirectEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('arrangeRedirectEdit.yuanMoXing'),
            name: 'destCollectionId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              disabled: true,
              placeholder: t('arrangeRedirectEdit.qingXuanZe'),
              items: BAR_DATA.collectionList
            },
            rules: [{ required: true, message: t('arrangeRedirectEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('arrangeRedirectEdit.yuanCunChu'),
            name: 'sourcePersistentModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('arrangeRedirectEdit.qingXuanZe'),
              items: persistentlList.value
            },
            rules: [{ required: true, message: t('arrangeRedirectEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('arrangeRedirectEdit.muBiaoMoXing'),
            name: 'sourceCollectionId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              disabled: true,
              placeholder: t('arrangeRedirectEdit.qingXuanZe'),
              items: BAR_DATA.collectionList
            },
            tips: t('arrangeRedirectEdit.yongYuZhuanChuDe'),
            rules: [{ required: true, message: t('arrangeRedirectEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('arrangeRedirectEdit.muBiaoCunChu'),
            name: 'destPersistentModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('arrangeRedirectEdit.qingXuanZe'),
              items: persistentlList.value
            },
            rules: [{ required: true, message: t('arrangeRedirectEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            name: 'dataFilter.dataFilterConditionList',
            value: [],
            span: 24,
            component: 'optionsTable'
          },

          {
            label: t('arrangeRedirectEdit.beiZhu'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              type: 'textarea',
              placeholder: t('arrangeRedirectEdit.qingShuRu')
            }
          },
          { label: t('arrangeRedirectEdit.yingShePeiZhi'), component: 'divider' },
          { component: 'mappingEdit' }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.arrangeRedirect.info({ id: id.value })
      dataForm.value = res
    }

    // 用户模型
    const optionsRedirect = ref({
      consumerMap: {},
      typeGroup: '',
      behaviorList: []
    })

    watch(
      () => dataForm.value.sourcePersistentModelId,
      (val) => {
        console.log(val, persistentlList.value)
        optionsRedirect.value.consumerMap = persistentlList.value.find((x) => x.value == val) || []
      }
    )
    const getBehavior = async () => {
      optionsRedirect.value.behaviorList = await $api.modelPersistent.behavior()
    }

    // 传参
    provide('optionsRedirect', optionsRedirect)

    onMounted(() => {
      loading.value = true
      Promise.all([getPersistentList(), getTransmissionList()])
        .then(() => {
          if (id.value) {
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          await $api.arrangeRedirect[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('arrangeRedirectEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/arrange/redirect', 'arrangeRedirect')
    }

    return {
      dataForm,
      mappingEditRef,
      dataFormRef,
      config,
      loading,
      isView,
      save,
      close
    }
  }
}
