<template>
  <el-container>
    <el-main v-loading="loading">
      <el-row gutter="15">
        <el-col :span="6" v-for="item in statistics" :key="item.label">
          <div class="card">
            <CardItem :itemData="item" />
          </div>
        </el-col>
        <el-col :span="24">
          <div class="card-chart">
            <lwBiChartItem :rawData="optionEchart" :isBi="false" height="400px" />
          </div>
        </el-col>
        <el-col :span="12">
          <el-row gutter="15">
            <el-col :span="24">
              <div class="card task">
                <div class="title">{{$t('dashboard.biaoQianTongJi')}}</div>
                <div class="task-list">
                  <el-statistic :title="$t('dashboard.xingWeiBiaoQian')" :value="tagMap.enumTagNum" />
                  <el-statistic :title="$t('dashboard.shouGongBiaoQian')" :value="tagMap.handTagNum" />
                  <el-statistic :title="$t('dashboard.guiZeBiaoQian')" :value="tagMap.numericalTagNum" />
                </div>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="card cloud">
                <div class="title">{{$t('dashboard.biaoQianCiYun')}}</div>
                <TagCloud v-if="tagList.length > 0" :tags="tagList" />
              </div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <div class="card tag">
            <div class="title">
              {{ tagAudience == 'tag' ? this.$t('dashboard.biaoQianGuanLi') : this.$t('dashboard.renQunGuanLi') }}

              <el-radio-group v-model="tagAudience" size="small" @change="changeTagAudience">
                <el-radio-button :label="$t('dashboard.biaoQianGuanLi')" value="tag" />
                <el-radio-button :label="$t('dashboard.renQunGuanLi')" value="audience" />
              </el-radio-group>
            </div>
            <lwBiChartItem v-if="tagAudience == 'tag'" :rawData="tagMap.chart" :isBi="false" height="360px" />
            <lwBiChartItem v-if="tagAudience == 'audience'" :rawData="audienceMap" :isBi="false" height="360px" />
          </div>
        </el-col>
        <el-col :span="24">
          <div class="card task">
            <div class="title">{{$t('dashboard.renWuGaiLan')}}</div>
            <div class="task-list">
              <el-statistic :title="$t('dashboard.zongShu')" :value="taskMap.totalTaskNum" />
              <el-statistic :title="$t('dashboard.jinXingZhong')" :value="taskMap.runningTaskNum" />
              <el-statistic :title="$t('dashboard.chengGong')" :value="taskMap.successTaskNum" />
              <el-statistic :title="$t('dashboard.yiQuXiao')" :value="taskMap.cancelTaskNum" />
              <el-statistic :title="$t('dashboard.shiBai')" :value="taskMap.errorTaskNum" />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-main>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
