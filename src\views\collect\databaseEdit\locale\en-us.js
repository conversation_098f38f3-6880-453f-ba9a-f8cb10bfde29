export default {
  collectDatabaseEdit: {
    jiBenXinXi: 'Basic Information',
    jieKouMingCheng: 'Interface Name',
    qingShuRu: 'Please enter',
    jie<PERSON>ou<PERSON>ianMa: 'Interface Code',
    ZNWXXZMSZHXH: 'Only lowercase letters, numbers and underscores allowed',
    CDBNCGGZF: 'Maximum 20 characters',
    jieKouShiQu: 'Interface Timezone',
    qingXuanZe: 'Please select',
    chuanShuMoXing: 'Transfer Model',
    KXZCSMXZYJLD: 'Select from existing tables in transfer models',
    shiFouJiLuRiZhi: 'Enable Logging',
    jiLu: 'Enable',
    buJiLu: 'Disable',
    SFXYJLRZKQHJ: 'Enable to record operation logs',
    shuJuShiFouCunChu: 'Data Storage',
    cunChu: 'Store',
    buCunChu: "Don't Store",
    KQHXXZCCMXHZ: 'Storage model selection required when enabled',
    cunChuMoXing: 'Storage Model',
    KXZCCMXZYJLD: 'Select from existing tables in storage models',
    beiZhu: 'Remarks',
    shuJuKu: 'Database',
    shuJuKuMingCheng: 'Database Name',
    shuJuKuLeiXing: 'Database Type',
    biaoMing: 'Table Name',
    zhuJian: 'Primary Key',
    yongHuMing: 'Username',
    miMa: 'SSH Password',
    guoLüTiaoJian: 'Filter Conditions',
    paiXu: 'Sort Order',
    zhuJi: 'Host',
    duanKou: 'SSH Port',
    shiFouKaiQiTongDao: 'Enable SSH Tunnel',
    diZhi: 'SSH Address',
    zhangHao: 'SSH Account',
    yingShePeiZhi: 'Field Mapping',
    baoCunChengGong: 'Saved successfully'
  }
}
