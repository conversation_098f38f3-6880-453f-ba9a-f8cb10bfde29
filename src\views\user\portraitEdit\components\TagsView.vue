<template>
  <div class="tags-view">
    <el-row :gutter="20">
      <el-col :span="6" v-for="category in tagList" :key="category.label">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>{{ category.label }}</span>
            </div>
          </template>
          <el-collapse v-model="activeName[category.id]" accordion>
            <el-collapse-item
              :title="group.label"
              :name="groupIdex"
              v-for="(group, groupIdex) in category.children"
              :key="group.label">
              <el-tag type="primary" v-for="tag in group.children" :key="tag.label">{{ tag.label }}</el-tag>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </el-col>
    </el-row>

    <el-empty v-if="tagList.length == 0" description="暂无标签" />
  </div>
</template>
<script>
export default {
  props: {
    tagList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeName: {}
    }
  }
}
</script>
<style lang="scss" scoped>
.tags-view {
  width: 100%;
  .el-card {
    margin-bottom: 20px;
  }
  :deep(.el-collapse) {
    border: 1px solid var(--el-fill-color-light);
  }
  :deep(.el-collapse-item__header) {
    background-color: var(--el-fill-color-light);
    .el-collapse-item__title {
      padding-left: 10px;
    }
  }
  :deep(.el-collapse-item__content) {
    padding: 10px;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    font-size: 14px;
  }
}
</style>
