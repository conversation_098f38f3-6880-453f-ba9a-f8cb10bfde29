<template>
  <el-container>
    <el-main>
      <lw-search
        :options="searchOptions"
        v-model="searchParams"
        :columnNumber="layout.grid"
        :expandNumber="4"
        :labelWidth="layout.labelWidth + 'px'"
        :hideLabel="layout.hideLabel"
        :labelAlign="layout.labelAlign"
        @search="search"
        @reset="reset" />
      <div class="table-block">
        <div class="btn-container">
          <el-button v-auth="['cdp.user_circle.create']" type="primary" icon="el-icon-plus" @click="edit"></el-button>
          <el-button v-auth="['cdp.user_circle_history']" type="primary" @click="batchSnapshot('*')">
            {{ $t('userCircle.quanLiangKuaiZhao') }}
          </el-button>
          <el-button
            v-auth="['cdp.user_circle_history']"
            type="primary"
            @click="batchSnapshot()"
            :disabled="selection.length == 0">
            {{ $t('userCircle.piLiangKuaiZhao') }}
          </el-button>
        </div>
        <lw-table
          :hideTool="false"
          :loading="loading"
          rowKey="id"
          :table-data="tableData"
          :tableColumns="tableHeaders"
          :search-params="searchParams"
          :isShowPagination="true"
          :rowSelection="true"
          :total-count="totalCount"
          @getTableData="getTableData"
          @multipleSelection="onSelection" />
      </div>
    </el-main>

    <!-- 导出弹窗 -->
    <el-dialog
      v-model="exportDialogVisible"
      :title="$t('userCircle.daoChu')"
      width="450px"
      :close-on-click-modal="false"
      custom-class="export-dialog">
      <div style="margin-bottom: 20px">{{ $t('userCircle.QXZDCMX') }}</div>
      <el-select v-model="exportModelName" :placeholder="$t('userCircle.QXZDCMX')" style="width: 100%" clearable>
        <el-option v-for="option in exportModel" :key="option.name" :label="option.aliasName" :value="option.name" />
      </el-select>
      <template #footer>
        <el-button @click="cancelExport">{{ $t('userCircle.quXiao') }}</el-button>
        <el-button type="primary" @click="confirmExport">{{ $t('userCircle.queDing') }}</el-button>
      </template>
    </el-dialog>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
