import config from '@/config'
import request from '@/utils/request'

export default {
  // 分页
  page: async (params) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task_period_config/{collectionId}`
    return await request.get(url, params)
  },
  // 列表
  list: async (params) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task_period_config/{collectionId}/list`
    return await request.get(url, params)
  },
  // 详情
  info: async (params) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task_period_config/{collectionId}/${params.id}`
    return await request.get(url)
  },
  // 修改
  edit: async (data = {}) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task_period_config/{collectionId}`
    return await request.put(url, data)
  },
  // 新增
  add: async (data = {}) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task_period_config/{collectionId}`
    return await request.post(url, data)
  },
  // 删除
  delete: async (id) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task_period_config/{collectionId}/${id}`
    return await request.delete(url)
  },
  // 路由
  route: async (params) => {
    let url = `${config.API_URL}/cdp-portal/data_routing/list`
    return await request.get(url, params)
  },
  // 订阅
  routing: async () => {
    let url = `${config.API_URL}/cdp-portal/data_subscribe_end_point/{collectionId}/list`
    return await request.get(url)
  },
  // 数据重放
  editReplay: async (data = {}, name) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task_period_config/{collectionId}/${name}`
    return await request.put(url, data)
  },
  addReplay: async (data = {}, name) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task_period_config/{collectionId}/${name}`
    return await request.post(url, data)
  }
}
