import userRoutes from '../router/route'
import menuZh from '@/locales/lang/zh-cn'
import menuEn from '@/locales/lang/en-us'
import config from '@/config'

// 合并函数
function mergeI18nToMenu(menuData, menuZh, menuEn) {
  return menuData.map((menuItem) => {
    const titleKey = menuItem.meta.title
    const i18nValueZh = getI18nValue(titleKey, menuZh)
    const i18nValueEn = getI18nValue(titleKey, menuEn)

    menuItem.meta['zh-cn'] = i18nValueZh
    menuItem.meta['en-us'] = i18nValueEn
    menuItem.meta['code'] = config.APP_NAME.toLowerCase()

    if (menuItem.children) {
      menuItem.children = mergeI18nToMenu(menuItem.children, menuZh, menuEn)
    }

    return menuItem
  })
}

// 获取国际化值
function getI18nValue(key, i18nData) {
  const keys = key.split('.')
  let value = i18nData

  for (const k of keys) {
    if (value && value.hasOwnProperty(k)) {
      value = value[k]
    } else {
      return null
    }
  }
  return value
}



window.dowMenu = () => {
  // 执行合并
  const mergedMenuData = mergeI18nToMenu(userRoutes, menuZh, menuEn)
  // 将合并后的菜单数据转为 JSON 字符串
  return JSON.stringify(mergedMenuData)
}
