{"name": "cdp", "version": "3.1.9", "scripts": {"dev": "vite", "build:uat": "cross-env NODE_ENV=uat vite build", "build": "cross-env NODE_ENV=prod vite build", "preview": "vite preview", "release": "npm version patch && node release.js"}, "files": ["dist"], "dependencies": {"@element-plus/icons": "^0.0.11", "ace-builds": "^1.41.0", "axios": "^1.3.6", "cropperjs": "^1.6.2", "dayjs": "^1.11.13", "element-plus": "^2.8.0", "file-saver": "^2.0.5", "lw-cdp-ui": "latest", "nprogress": "^0.2.0", "vue": "^3.5.16", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vue3-ace-editor": "^2.2.4", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^2.0.0", "cross-env": "^7.0.3", "fast-glob": "^3.2.12", "sass": "1.62.1", "sass-loader": "13.2.2", "vite": "^4.5.14", "vite-plugin-pwa": "^0.11.13", "vite-plugin-svg-icons": "^2.0.1"}}