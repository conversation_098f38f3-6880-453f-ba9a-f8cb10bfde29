import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'modelPersistent',
  setup() {
    const router = useRouter()
    const store = useStore()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        delete_ne: true
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    let persistentType = {
      consumer: t('modelPersistent.keHu'),
      behavior: t('modelPersistent.xingWei'),
      normal: t('modelPersistent.puTong')
    }

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('modelPersistent.moXingMingCheng'),
          prop: 'aliasName_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('modelPersistent.mingCheng'), dataIndex: 'aliasName', minWidth: '120', tooltip: true },
        { title: t('modelPersistent.bianMa'), dataIndex: 'name', minWidth: '120', tooltip: true },
        {
          title: t('modelPersistent.leiXing'),
          dataIndex: 'dataPersistentInfo.persistentType',
          width: '120',
          eleRender: (scope) => {
            let color =
              scope.dataPersistentInfo.persistentType == 'consumer'
                ? 'success'
                : scope.dataPersistentInfo.persistentType == 'behavior'
                ? 'warning'
                : 'info'
            return `<span class="el-tag el-tag--${color} el-tag--light">${
              persistentType?.[scope.dataPersistentInfo.persistentType] || '--'
            }</span>`
          }
        },
        {
          title: t('modelPersistent.yongYuBiaoQian'),
          dataIndex: 'useForTag',
          width: '90',
          eleRender: (scope) => {
            return scope?.useForTag ? t('modelPersistent.yunXu') : t('modelPersistent.jinZhi')
          }
        }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '180',
        fixed: 'right',
        ellipsis: true,
        operation: [
          {
            clickFun: (item) => edit(item, 'view'),
            label: t('btn.view'),
            isShow: () => store.state.user?.status,
            auth: ['cdp.model_persistent_edit']
          },
          {
            clickFun: edit,
            label: t('btn.edit'),
            isShow: () => !store.state.user?.status,
            auth: ['cdp.model_persistent_edit']
          },
          {
            clickFun: del,
            label: t('btn.delete'),
            auth: ['cdp.model_persistent.delete']
          }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = async (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        sort: 'createTime,DESC',
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      let res = await $api.modelPersistent.page(params)

      state.tableData = res.content
      state.currentPage = page
      state.totalCount = res.totalElements
      state.loading = false
    }

    const reset = () => {
      state.searchParams = { delete_ne: true }
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    const edit = (item, type) => {
      let query = { id: item.id }
      if (type == 'view') {
        query.isView = true
      }
      router.push({ path: '/model/persistentEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.modelPersistent.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    const add = () => {
      router.push({ path: '/model/persistentEdit' })
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      add
    }
  }
}
