<template>
  <div class="lw-fields-edit">
    <el-card class="card-left" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>{{ $t('modelPortraitEdit.moXingLieBiao') }}</span>
          <div class="card-header-right">
            <el-input
              :placeholder="$t('modelPortraitEdit.SRGJZJXGL')"
              v-model="menuFilterText"
              clearable
              size="small"></el-input>
            <el-button v-if="!isView" type="primary" size="small" @click="editJson">JSON</el-button>
          </div>
        </div>
      </template>
      <div class="tree-list" :class="{ 'is-view': isView }">
        <el-tree
          ref="fieldMenuRef"
          class="menu"
          node-key="fieldName"
          :data="treeFields"
          :props="menuProps"
          highlight-current
          :check-on-click-node="false"
          check-strictly
          show-checkbox
          :default-checked-keys="fieldCheckedKeys"
          :filter-node-method="fieldFilterNode"
          @node-click="fieldClick"
          @check-change="selectField">
        </el-tree>
      </div>

      <template v-if="!isView" #footer>
        <div class="footer-btn-list">
          <el-button type="primary" @click="allField(treeFields)">{{ $t('modelPortraitEdit.quanXuan') }}</el-button>
          <el-button type="danger" plain @click="delFields">{{ $t('modelPortraitEdit.quXiaoQuanXuan') }}</el-button>
        </div>
      </template>
    </el-card>
    <el-card class="card-right" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>{{ $t('modelPortraitEdit.ziDuanPeiZhi') }}</span>
          <div v-if="fieldItem?.id && !isView" class="card-header-right">
            <el-button type="primary" size="small" @click="editJson('field')">JSON</el-button>
          </div>
        </div>
      </template>

      <!-- 字段编辑 -->
      <FieldEdit ref="fieldEditRef" v-model="fieldItem" />
    </el-card>
  </div>

  <!-- json -->
  <el-dialog v-model="dialogVisible" :title="$t('modelPortraitEdit.bianJi')" width="50%">
    <div style="width: 100%; height: 500px">
      <lwCodeEdit v-model="textareaJson" placeholder="{{$t('modelPortraitEdit.QJSXSBNTZCCZ')}}" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('btn.cancel') }}</el-button>
        <el-button @click="confirmJson" type="primary">{{ $t('btn.confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
import lwCodeEdit from '@/components/lwCodeEdit.vue'
import FieldEdit from './edit.vue'
export default {
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    consumerList: {
      type: Array,
      default: () => []
    }
  },
  components: {
    lwCodeEdit,
    FieldEdit
  },
  data() {
    return {
      dialogVisible: false,
      treeFields: [],
      fieldItem: {},
      jsonType: '',
      menuFilterText: '',
      textareaJson: '',
      isView: !!this.$route.query.isView,
      menuProps: {
        label: (data) => {
          return data.fieldAliasName
        },
        children: 'fields'
      }
    }
  },
  watch: {
    menuFilterText(val) {
      this.$refs.fieldMenuRef.filter(val)
    },
    consumerList: {
      handler(val) {
        this.treeFields = this.initKey(JSON.parse(JSON.stringify(val)))
      },
      immediate: true
    },
    fieldItem: {
      handler(val) {
        this.changeFieldItem(val)
      },
      deep: true
    }
  },
  computed: {
    fieldCheckedKeys() {
      return this.modelValue.map((x) => x.fieldName)
    }
  },
  methods: {
    initKey(fields, identifyName) {
      return fields.map((item) => {
        let itemCopy = {}
        itemCopy.fieldName = item.name
        itemCopy.fieldAliasName = item.aliasName
        itemCopy.fieldType = item.type
        itemCopy.viewFieldName = item.name
        itemCopy.viewFieldNameCn = item.aliasName
        itemCopy.viewFieldNameEn = ''
        itemCopy.visualSetting = item.visualSetting
        itemCopy.piiData = item.piiData
        itemCopy.piiDataSetting = item.piiDataSetting
        itemCopy.enableMappingDict = item.enableMappingDict
        itemCopy.mappingDictId = item.mappingDictId
        if (item.fields && item.fields.length > 0) {
          itemCopy.fields = this.initKey(item.fields, item.identifyName)
        }
        return itemCopy
      })
    },
    editJson(type = '') {
      if (type == 'field') {
        this.textareaJson = JSON.stringify(this.fieldItem, null, 2)
      } else {
        this.textareaJson = JSON.stringify(this.treeFields, null, 2)
      }
      this.jsonType = type
      this.dialogVisible = true
    },
    confirmJson() {
      if (this.jsonType == 'field') {
        let fieldItem = JSON.parse(this.textareaJson)
        this.fieldClick(fieldItem)
        this.changeFieldItem(fieldItem)
      } else {
        this.treeFields = JSON.parse(this.textareaJson)
      }

      this.dialogVisible = false
      this.jsonType = ''
    },
    fieldFilterNode(value, data) {
      if (!value) return true
      let targetText = data.name
      return targetText.indexOf(value) !== -1
    },
    // 全选
    allField(treeFields) {
      const setAllChecked = (data) => {
        data.forEach((node) => {
          this.$refs.fieldMenuRef.setChecked(node.fieldName, true)
          if (node.fields && node.fields.length > 0) {
            setAllChecked(node.fields)
          }
        })
      }
      setAllChecked(treeFields)
    },
    //树点击
    async fieldClick(data, node) {
      this.fieldItem = data
      this.$nextTick(() => {
        this.$refs.fieldEditRef.init()
      })
    },

    // 取消全选
    async delFields() {
      const setAllUnchecked = (data) => {
        data.forEach((node) => {
          this.$refs.fieldMenuRef.setChecked(node.fieldName, false)
          if (node.fields && node.fields.length > 0) {
            setAllUnchecked(node.fields)
          }
        })
      }
      setAllUnchecked(this.treeFields)
    },
    // 编辑字段
    replaceNodeById(tree, newItem) {
      return tree.map((item) => {
        if (item.fieldName === newItem.fieldName) {
          return newItem // 完全替换
        }

        if (Array.isArray(item.fields)) {
          return {
            ...item,
            fields: this.replaceNodeById(item.fields, newItem)
          }
        }

        return item
      })
    },
    changeFieldItem(item) {
      const checkedNodes = this.$refs.fieldMenuRef?.getCheckedNodes()
      this.treeFields = this.replaceNodeById(this.treeFields, item)
      this.$nextTick(() => {
        this.$refs.fieldMenuRef.setCheckedNodes(checkedNodes)
      })
    },
    // 选中数据
    selectField() {
      this.$nextTick(async () => {
        const checkedNodes = await this.$refs.fieldMenuRef?.getCheckedNodes()
        this.$emit('update:modelValue', [...checkedNodes])
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.lw-fields-edit {
  display: flex;
  gap: 10px;
  justify-content: space-between;
  width: 100%;
  :deep(.el-card__body) {
    padding: 0;
  }
  .card-left {
    width: 300px;
  }
  .card-right {
    width: calc(100% - 310px);
  }
  .tree-list {
    height: calc(100vh - 520px);
    overflow: auto;
    &.is-view {
      height: calc(100vh - 470px);
    }
  }
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    .card-header-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 200px;
    }
  }
  .footer-btn-list {
    display: flex;
    align-items: center;
    button {
      flex: 1;
    }
  }
  .custom-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    .tree-icon-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }
  }
}
</style>
