import lwMappingEdit from '@/components/lwMappingEdit/index.vue'
import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'collectApiEdit',
  components: {
    lwMappingEdit
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const dataForm = ref({
      name: '',
      aliasName: '',
      mappingList: []
    })
    const dataFormRef = ref(null)
    const mappingEditRef = ref(null)
    const loading = ref(false)

    // 存储模型
    const persistentlList = ref([])
    const getPersistentList = async () => {
      let res = await $api.modelPersistent.list()
      persistentlList.value = res.map((item) => ({ label: item.aliasName, value: item.id }))
    }

    // 传输模型
    const transmissionList = ref([])
    const getTransmissionList = async () => {
      let res = await $api.modelTransmission.list()
      transmissionList.value = res.map((item) => ({ label: item.aliasName, value: item.id }))
    }

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('collectApiEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('collectApiEdit.jieKouMingCheng'),
            name: 'aliasName',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('collectApiEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectApiEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('collectApiEdit.jieKouBianMa'),
            name: 'name',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('collectApiEdit.qingShuRu')
            },
            tips: t('collectApiEdit.ZNWXXZMSZHXH'),
            rules: [
              { required: true, message: t('collectApiEdit.qingShuRu'), trigger: 'blur' },
              { pattern: /^[a-z0-9_]+$/, message: t('collectApiEdit.ZNWXXZMSZHXH'), trigger: 'blur' },
              { max: 20, message: t('collectApiEdit.CDBNCGGZF'), trigger: 'blur' }
            ]
          },
          {
            label: t('collectApiEdit.jieKouShiQu'),
            name: 'defaultTimezone',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('collectApiEdit.qingXuanZe'),
              items: timeList.value
            },
            rules: [{ required: true, message: t('collectApiEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('collectApiEdit.chuanShuMoXing'),
            name: 'transferModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('collectApiEdit.qingXuanZe'),
              items: transmissionList.value
            },
            tips: t('collectApiEdit.KXZCSMXZYJLD'),
            rules: [{ required: true, message: t('collectApiEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('collectApiEdit.shiFouJiLuRiZhi'),
            name: 'needLogging',
            value: true,
            span: 6,
            component: 'switch',
            options: {
              activeText: t('collectApiEdit.jiLu'),
              inactiveText: t('collectApiEdit.buJiLu')
            },
            tips: t('collectApiEdit.SFXYJLRZKQHJ')
          },
          {
            label: t('collectApiEdit.shuJuShiFouCunChu'),
            name: 'needPersistent',
            value: false,
            span: 6,
            component: 'switch',
            options: {
              activeText: t('collectApiEdit.cunChu'),
              inactiveText: t('collectApiEdit.buCunChu')
            },
            tips: t('collectApiEdit.KQHXXZCCMXHZ')
          },
          {
            label: t('collectApiEdit.cunChuMoXing'),
            name: 'persistentModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('collectApiEdit.qingXuanZe'),
              items: persistentlList.value
            },
            tips: t('collectApiEdit.KXZCCMXZYJLD'),
            rules: [{ required: true, message: t('collectApiEdit.qingXuanZe'), trigger: 'blur' }],
            hideHandle: '!$.needPersistent'
          },
          {
            label: t('collectApiEdit.beiZhu'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              type: 'textarea',
              placeholder: t('collectApiEdit.qingShuRu')
            }
          },
          { label: t('collectApiEdit.yingShePeiZhi'), component: 'divider' },
          { component: 'mappingEdit' }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.collectApi.info({ id: id.value })
      dataForm.value = res
    }

    // 获取时区
    const timeList = ref([])
    const getTime = async () => {
      let res = await $api.collectApi.time({ id: id.value })
      timeList.value = res.map((item) => ({ label: item, value: item }))
    }

    onMounted(() => {
      loading.value = true
      Promise.all([getPersistentList(), getTransmissionList(), getTime()])
        .then(() => {
          if (id.value) {
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          await $api.collectApi[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('collectApiEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/collect/api', 'collectApi')
    }

    return {
      dataForm,
      mappingEditRef,
      dataFormRef,
      config,
      loading,
      isView,
      save,
      close
    }
  }
}
