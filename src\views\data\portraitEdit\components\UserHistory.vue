<template>
  <lw-search
    :options="searchOptions"
    v-model="searchParams"
    :columnNumber="layout.grid"
    :expandNumber="4"
    :labelWidth="layout.labelWidth + 'px'"
    :hideLabel="layout.hideLabel"
    :labelAlign="layout.labelAlign"
    @search="search"
    @reset="reset" />
  <div class="lw-table-header" v-loading="loading">
    <div class="table-body">
      <el-timeline>
        <template v-for="item in tableData" :key="item.id">
          <el-timeline-item center :timestamp="item.date" placement="top">
            <el-card shadow="hover">
              <h4>{{ item.event }}</h4>
              <p>{{ item.detail }}</p>
            </el-card>
          </el-timeline-item>
        </template>
      </el-timeline>
    </div>
    <el-pagination
      v-if="tableData.length > 0"
      size="small"
      :page-sizes="[10, 20, 50, 100]"
      background
      v-model:current-page="searchParams.page"
      v-model:page-size="searchParams.size"
      layout="total, sizes, prev, pager, next"
      :total="totalCount"
      @size-change="onCurrentPage"
      @current-change="onCurrentPage" />

    <el-empty v-if="tableData.length == 0" :description="$t('userPortraitEdit.zanWuShuJu')" />
  </div>
</template>

<script>
import { getCurrentInstance, onMounted, onBeforeMount, reactive, computed, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'

export default {
  name: 'TabTable',
  props: {
    id: {
      type: String,
      required: true
    },
    fields: {
      type: Array,
      default: () => []
    },
    identity: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        cdp_identity_Q_eq: props.identity
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      currentPage: 1,
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 获取事件名称
    const getFieldList = () => {
      return $api.userPortrait.history(props.id, [{ field: 'event', name: 'event', type: 'terms' }]).then((res) => {
        let list = res.tables?.[0].rows.map((item) => {
          return {
            label: item.name,
            value: item.name
          }
        })
        state.searchOptions.push({
          label: t('userPortraitEdit.QXZSJLX'),
          prop: 'event_eq',
          renderType: 'select',
          options: list
        })
        state.searchOptions.push({
          label: t('userPortraitEdit.paiXu'),
          name: 'sort',
          value: 'desc',
          span: 6,
          component: 'radio',
          options: {
            items: [
              { label: t('userPortraitEdit.zhengXu'), value: 'asc' },
              { label: t('userPortraitEdit.daoXu'), value: 'desc' }
            ]
          }
        })
      })
    }

    onBeforeMount(() => {
      state.searchOptions = []
    })

    onMounted(() => {
      state.loading = true
      Promise.all([getFieldList()])
        .then(() => {
          getTableData()
        })
        .finally(() => {
          state.loading = false
        })
    })

    const onCurrentPage = () => {
      state.loading = true
      console.log(state.searchParams)
      getTableData()
    }

    const getTableData = () => {
      state.loading = true
      let params = {
        pageIndex: state.searchParams.page - 1,
        pageSize: state.searchParams.size,
        pageSort: `date,${state.searchParams.sort}`,
        expression: $expression(state.searchParams)
      }

      $api.userPortrait
        .tabTable(props.id, params)
        .then((res) => {
          state.tableData = res.content.map((item) => {
            return {
              ...item,
              date: item.date ? dayjs(item.date).format('YYYY-MM-DD HH:mm:ss') : '--'
            }
          })
          state.totalCount = res.totalElements
        })
        .finally(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = { page: 1, size: 10, cdp_identity_Q_eq: props.identity }
      getTableData()
    }

    const search = () => {
      state.searchParams.page = 1
      getTableData()
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.cdp_id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/user/portraitEdit', query })
    }

    const height = computed(() => {
      let haveSearch = state.searchOptions.length > 0 ? 40 : 0
      return `calc(100vh - ${305 + haveSearch}px)`
    })

    return {
      ...toRefs(state),
      getTableData,
      reset,
      search,
      onCurrentPage,
      height,
      edit
    }
  }
}
</script>
<style lang="scss" scoped>
.lw-table-header {
  width: 100%;
  .table-body {
    width: 100%;
  }
  :deep(.el-pagination) {
    float: right;
    display: flex;
    align-items: center;
  }
  :deep(.el-pagination__sizes) {
    line-height: 20px;
  }
}
</style>
