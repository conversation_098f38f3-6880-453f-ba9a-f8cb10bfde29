<template>
  <div class="lw-mapping-edit">
    <lwFormMini ref="dataFormRef" :config="config" v-model="dataForm"></lwFormMini>
    <lwFieldsEdit v-model="dataForm.fields" :consumerList="consumerList" />
  </div>
</template>
<script>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue'
import lwFieldsEdit from '../lwFieldsEdit/index.vue'
export default {
  components: {
    lwFieldsEdit
  },
  props: {
    modelValue: {
      type: Array,
      default: []
    },
    behaviorId: {
      type: String,
      default: ''
    },
    behaviorList: {
      type: Array,
      default: () => []
    }
  },
  setup(props, { emit }) {
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()
    const dataForm = ref({
      persistentModelId: props.behaviorId,
      viewFieldName: '',
      viewFieldNameCn: '',
      viewFieldNameEn: '',
      fields: []
    })

    const itemIndex = computed(() => {
      return props.modelValue.findIndex((item) => item.persistentModelId === props.behaviorId)
    })

    const consumerList = computed(() => {
      return props.behaviorList.find((item) => item.id === props.behaviorId)?.fields || []
    })
    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          {
            label: t('modelPortraitEdit.ziDuan'),
            name: 'viewFieldName',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('modelPortraitEdit.ziDuan')
            },
            rules: [{ required: true, message: t('modelPortraitEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPortraitEdit.xianShiZhongWen'),
            name: 'viewFieldNameCn',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('modelPortraitEdit.zhongWenMing')
            },
            rules: [{ required: true, message: t('modelPortraitEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('modelPortraitEdit.xianShiYingWen'),
            name: 'viewFieldNameEn',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('modelPortraitEdit.yingWenMing')
            },
            rules: [{ required: true, message: t('modelPortraitEdit.qingShuRu'), trigger: 'blur' }]
          }
        ]
      }
    })

    watch(
      () => dataForm.value,
      (val) => {
        if (!val.viewFieldName) {
          return false
        }
        if (itemIndex.value === -1) {
          props.modelValue.push(val)
        } else {
          props.modelValue[itemIndex.value] = val
        }
        emit('update:modelValue', props.modelValue)
      },
      { deep: true }
    )

    onMounted(() => {
      if (itemIndex.value !== -1) {
        dataForm.value = { ...props.modelValue[itemIndex.value] }
      }
    })

    return {
      dataForm,
      consumerList,
      config
    }
  }
}
</script>
<style lang="scss" scoped>
.lw-mapping-edit {
  width: 100%;
}
</style>
