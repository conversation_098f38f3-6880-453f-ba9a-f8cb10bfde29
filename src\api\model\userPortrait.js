import config from '@/config'
import request from '@/utils/request'

export default {
  // 分页
  page: async (params) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionId}/consumer/page`
    return await request.get(url, params)
  },
  // 分页
  personPage: async (params, id) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionId}/cdp_audience/${id}`
    return await request.get(url, params)
  },
  // 列表
  list: async (params) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionId}/list`
    return await request.get(url, params)
  },
  // 详情
  info: async (params) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionId}/${params.id}`
    return await request.get(url)
  },
  // 用户详情
  customer: async (params) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionName}/${params.name}/${params.id}`
    return await request.get(url)
  },
  // 修改
  edit: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionId}`
    return await request.put(url, data)
  },
  // 新增
  add: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionId}`
    return await request.post(url, data)
  },
  // 删除
  delete: async (id) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionId}/${id}`
    return await request.delete(url)
  },
  // 显示字段
  fields: async (params) => {
    let url = `${config.API_URL}/cdp-portal/data_persistent_model/{collectionId}/visual-fields/list/${params.id}`
    return await request.get(url)
  },
  // 用户标签
  tags: async (params) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionId}/${params.id}/tag_view`
    return await request.get(url)
  },
  // 用户详情
  tabTable: async (id, data) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionName}/${id}`
    return await request.post(url, data)
  },
  // 获取事件类型
  history: async (id, data) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionName}/${id}/aggregation`
    return await request.post(url, data)
  }
}
