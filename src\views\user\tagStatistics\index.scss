.footer-body {
  text-align: right;
}
.card {
  margin-bottom: 15px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  padding: 20px;
  &.last {
    margin-bottom: 0;
    height: calc(100vh - 365px);
  }
  .title {
    font-size: 18px;
    color: #474747;
    line-height: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 20px;
    a {
      cursor: pointer;
      color: #b2b3b8;
      font-size: 14px;
    }
  }
}
.top-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .input-time {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 5px;
    gap: 10px;
  }
  .title {
    font-size: 18px;
    color: #474747;
    line-height: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

:deep(.el-statistic__head) {
  font-size: 18px;
  color: #474747;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
  padding: 10px 0 0 0;
}
:deep(.el-statistic__content) {
  text-align: center;
  font-size: 36px;
  padding: 0 0 10px 0;
}
