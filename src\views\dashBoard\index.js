import dayjs from 'dayjs'
import { getCurrentInstance, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import CardItem from './components/card-item.vue'
import TagCloud from './components/tag-cloud.vue'
export default {
  name: 'dashboard',
  components: {
    CardItem,
    TagCloud
  },
  props: {
    showTime: {
      type: Boolean,
      default: false
    },
    height: {
      // 图表高度
      type: String,
      default: 'calc(100vh - 280px)'
    }
  },
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t }
    } = getCurrentInstance()

    const statistics = ref([
      { label: t('dashboard.shuJuMoXingZongShu'), value: 0, percent: -1 },
      { label: t('dashboard.cunChuMoXingZongLiang'), value: 0, percent: -1 },
      { label: t('dashboard.chuanShuMoXingZongLiang'), value: 0, percent: -1 },
      { label: t('dashboard.renQunZongShu'), value: 0, percent: 0 },
      { label: t('dashboard.zongShuJuLiang'), value: 0, percent: 0 },
      { label: t('dashboard.xingWeiShuJuZongLiang'), value: 0, percent: 0 },
      { label: t('dashboard.keHuShuJuZongLiang'), value: 0, percent: 0 },
      { label: t('dashboard.daTongKeHuZongLiang'), value: 0, percent: 0 }
    ])
    const loading = ref(false)

    // 获取数据
    const getCardData = async () => {
      let res = await $api.dashboard.getTopData()
      statistics.value[0].value = res.dataModelNum
      statistics.value[1].value = res.persistentModelNum
      statistics.value[2].value = res.transformModelNum
      statistics.value[3].value = res.audienceDataNum
      statistics.value[3].percent = res.lastWeekAudienceDataPercent
      statistics.value[4].value = res.totalNum
      statistics.value[4].percent = res.lastWeekTotalPercent
      statistics.value[5].value = res.behaviorNum
      statistics.value[5].percent = res.lastWeekBehaviorPercent
      statistics.value[6].value = res.consumerNum
      statistics.value[6].percent = res.lastWeekConsumerPercent
      statistics.value[7].value = res.activeConsumerNum
      statistics.value[7].percent = res.lastWeekActiveConsumerPercent
    }

    // 总资产
    const optionEchart = ref({
      type: 'Line',
      option: {}
    })
    const getAssetData = async () => {
      let res = await $api.dashboard.assetData()

      const categories = res.map((item) => {
        const date = dayjs(item.createDate)
        return date.format('YYYY-MM-DD')
      })

      const values = res.map((item) => item.dataNum)

      optionEchart.value.option = {
        title: {
          text: t('dashboard.shuJuZiChanQuShi')
        },
        tooltip: {
          trigger: 'axis',
          formatter: `{b}<br/>${t('dashboard.shuJuLiang')}: {c}`
        },
        grid: {
          left: '0',
          right: '0',
          bottom: '0',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            rotate: 45 // 如果标签太长可以旋转45度
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: t('dashboard.shuJuLiang'),
            data: values,
            type: 'line',
            smooth: true,
            areaStyle: {}
          }
        ]
      }
    }

    // 任务概览
    const taskMap = ref({})
    const getTaskData = async () => {
      taskMap.value = await $api.dashboard.task()
    }

    // 标签概览
    const tagList = ref([])
    const tagMap = ref({})
    const getTagData = async () => {
      tagMap.value = await $api.dashboard.tag()

      // 过滤掉数值为0的数据（可选，根据需求）
      const filteredData = tagMap.value.top10TagGroupList.filter((item) => item.dataNum > 0)

      // 转换为饼图需要的格式
      const seriesData = filteredData.map((item) => ({
        name: item.dataName,
        value: item.dataNum
      }))
      tagList.value = seriesData

      // 按值从大到小排序（可选，使饼图更美观）
      seriesData.sort((a, b) => b.value - a.value)

      tagMap.value.chart = {
        type: 'Bar',
        option: {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            type: 'scroll',
            top: '0%',
            right: 10,
            left: 15,
            data: seriesData.map((item) => item.name)
          },
          series: [
            {
              name: t('dashboard.shuJuLiang'),
              type: 'pie',
              radius: ['20%', '60%'], // 环形饼图
              center: ['50%', '55%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                formatter: '{b}:\n{c} ({d}%)'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '18',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: true
              },
              data: seriesData
            }
          ]
        }
      }
    }

    // 人群
    const audienceMap = ref([])
    const getAudienceData = async () => {
      let res = await $api.dashboard.audience()

      // 过滤掉数值为0的数据（可选，根据需求）
      const filteredData = res.filter((item) => item.dataNum > 0)

      // 转换为饼图需要的格式
      const seriesData = filteredData.map((item) => ({
        name: item.dataName,
        value: item.dataNum
      }))

      // 按值从大到小排序（可选，使饼图更美观）
      seriesData.sort((a, b) => b.value - a.value)

      audienceMap.value = {
        type: 'Bar',
        option: {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            type: 'scroll',
            top: '0%',
            right: 10,
            left: 15,
            data: seriesData.map((item) => item.name)
          },
          series: [
            {
              name: t('dashboard.shuJuLiang'),
              type: 'pie',
              radius: ['20%', '60%'], // 环形饼图
              center: ['50%', '55%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                formatter: '{b}:\n{c} ({d}%)'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '18',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: true
              },
              data: seriesData
            }
          ]
        }
      }
    }

    const tagAudience = ref('tag')
    const changeTagAudience = (type) => {
      tagAudience.value = type
    }

    onMounted(() => {
      loading.value = true
      // 同步执行所有接口
      Promise.all([getCardData(), getAssetData(), getTaskData(), getTagData(), getAudienceData()])
        .then(() => {
          loading.value = false
        })
        .catch((error) => {
          console.error('Error fetching data:', error)
          loading.value = false
        })
    })

    return {
      loading,
      statistics,
      optionEchart,
      tagMap,
      audienceMap,
      tagAudience,
      tagList,
      taskMap
    }
  }
}
