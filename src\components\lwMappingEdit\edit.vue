<template>
  <el-dialog v-model="dialogVisible" append-to-body title="映射配置" width="800px" :close-on-click-modal="false">
    <lwFormMini ref="formRef" v-model="dataForm" :config="formConfig">
      <template #dictionaryManage>
        <lwDictEdit v-model="dataForm.dictionaryId" />
      </template>
      <template #conditionList>
        <lwTableForm :config="configTags" v-model="dataForm.dataConditionExpression.conditionExpDetailList" />
      </template>
    </lwFormMini>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import lwDictEdit from '@/components/lwDictEdit/index.vue'

export default {
  name: 'MappingEdit',
  components: {
    lwDictEdit
  },
  props: {
    mappingTypes: {
      type: Object,
      default: () => ({})
    },
    transferList: {
      type: Array,
      default: () => []
    },
    persistentList: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update'],
  setup(props, { emit }) {
    const dialogVisible = ref(false)
    const formRef = ref(null)
    const dataForm = ref({})

    const init = (item) => {
      // 初始化表单数据
      dataForm.value = JSON.parse(JSON.stringify(item))
      if (!dataForm.value.dataConditionExpression) {
        dataForm.value.dataConditionExpression = {
          conditionExpDetailList: [{ formValue: '', toValue: '', targetValue: '' }],
          expFieldType: 'DOUBLE'
        }
      }
      if (dataForm.value.mappingType == 'from_multi_sourceField') {
        dataForm.value.sourceFieldName = dataForm.value.sourceFieldName.split(',')
      }
      dialogVisible.value = true
    }

    // 主表单配置
    const formConfig = computed(() => ({
      labelWidth: '120px',
      labelPosition: 'top',
      size: 'default',
      formItems: [
        {
          label: '存储模型字段',
          name: 'sourceFieldName',
          value: '',
          span: 8,
          component: 'treeSelect',
          options: {
            placeholder: '请选择',
            props: {
              children: 'children',
              label: 'aliasName',
              value: 'fieldName'
            },
            items: props.persistentList
          },
          hideHandle:
            '!($.mappingType == "from_sourceField" || $.mappingType == "from_dictionary"  || $.mappingType == "condition_exp")',
          rules: [{ required: true, message: '请选择存储模型字段' }]
        },
        {
          label: '存储模型字段',
          name: 'sourceFieldName',
          value: [],
          span: 8,
          component: 'treeSelect',
          options: {
            placeholder: '请选择',
            props: {
              children: 'children',
              label: 'aliasName',
              value: 'fieldName'
            },
            multiple: true,
            items: props.persistentList
          },
          hideHandle: '!($.mappingType == "from_multi_sourceField")',
          rules: [{ required: true, message: '请选择存储模型字段' }]
        },
        {
          label: '固定值',
          name: 'fixedValue',
          value: '',
          span: 8,
          component: 'input',
          options: {
            placeholder: '请输入'
          },
          hideHandle: '!($.mappingType == "fixed_value")',
          rules: [{ required: true, message: '请输入' }]
        },
        {
          label: '存储模型字段',
          name: 'noMappingFieldName',
          value: '',
          span: 8,
          component: 'input',
          options: {
            disabled: true,
            placeholder: '无需配置存储模型字段'
          },
          hideHandle: '!($.mappingType == "no_mapping")',
          rules: [{ required: true, message: '请输入' }]
        },
        {
          label: '映射方式',
          name: 'mappingType',
          value: 'no_mapping',
          span: 8,
          component: 'select',
          options: {
            items: [
              { label: '直接映射', value: 'from_sourceField' },
              { label: '多个字段映射', value: 'from_multi_sourceField' },
              { label: '字典映射', value: 'from_dictionary' },
              { label: '固定值', value: 'fixed_value' },
              { label: '区间映射', value: 'condition_exp' },
              { label: '不映射', value: 'no_mapping' }
            ],
            placeholder: '请选择'
          },
          tips: '当前字段的映射方式，根据不同的映射方式需填写对应内容',
          rules: [{ required: true, message: '请选择映射方式' }]
        },
        {
          label: '传输字段',
          name: 'destFieldName',
          value: '',
          span: 8,
          component: 'treeSelect',
          options: {
            placeholder: '请选择',
            props: {
              children: 'children',
              label: 'aliasName',
              value: 'fieldName'
            },
            disabled: true,
            items: props.transferList
          },
          rules: [{ required: true, message: '请选择存储模型字段' }]
        },
        {
          label: '字典映射',
          name: 'dictionaryId',
          value: '',
          component: 'dictionaryManage',
          span: 24,
          hideHandle: '$.mappingType !== "from_dictionary"',
          rules: [{ required: true, message: '请选择字典' }]
        },
        {
          label: '区间类型',
          name: 'dataConditionExpression.expFieldType',
          value: 'DOUBLE',
          component: 'radio',
          span: 12,
          options: {
            items: [
              { label: '数值类型', value: 'DOUBLE' },
              { label: '日期类型', value: 'DATE' }
            ]
          },
          hideHandle: '$.mappingType !== "condition_exp"'
        },
        {
          label: '区间映射',
          name: 'dataConditionExpression.conditionExpDetailList',
          value: [
            {
              formValue: '',
              toValue: '',
              targetValue: ''
            }
          ],
          component: 'conditionList',
          span: 24,
          hideHandle: '$.mappingType !== "condition_exp"'
        },
        {
          label: '加密/解密方式',
          name: 'cryptoOperation',
          value: 'NONE',
          component: 'select',
          span: 8,
          options: {
            items: [
              { label: '无需加密', value: 'NONE' },
              { label: '加密', value: 'ENCRYPT' },
              { label: '解密', value: 'DECRYPT' }
            ]
          }
        },
        {
          label: '加密/解密类型',
          name: 'cryptoType',
          value: '',
          component: 'select',
          span: 8,
          options: {
            items: [
              { label: 'MD5', value: 'MD5' },
              { label: 'AES', value: 'AES' },
              { label: 'SHA', value: 'SHA' }
            ]
          },
          hideHandle: '!$.cryptoOperation || $.cryptoOperation === "NONE"'
        },
        {
          label: '权重',
          name: 'weight',
          value: 0,
          component: 'number',
          span: 8,
          options: {
            min: 0
          },
          tips: '权重值越小，优先级越高'
        }
      ]
    }))

    // 区间映射
    const configConditionExpression = computed(() => {
      if (dataForm.value.dataConditionExpression.expFieldType == 'DOUBLE') {
        return [
          {
            label: '范围起始值',
            name: 'formValue',
            value: '',
            minWidth: '160',
            component: 'input',
            options: {
              placeholder: '请输入'
            },
            rules: [{ required: true, message: '不能为空', trigger: 'blur' }]
          },
          {
            label: '范围结束值',
            name: 'toValue',
            value: '',
            minWidth: '160',
            component: 'input',
            options: {
              placeholder: '请输入'
            },
            rules: [{ required: true, message: '不能为空', trigger: 'blur' }]
          }
        ]
      } else {
        return [
          {
            label: '起始日期',
            name: 'formValue',
            value: '',
            minWidth: '160',
            component: 'date',
            options: {
              type: 'datetime',
              placeholder: '请选择',
              valueFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ'
            },
            rules: [{ required: true, message: '不能为空', trigger: 'blur' }]
          },
          {
            label: '结束日期',
            name: 'toValue',
            value: '',
            minWidth: '160',
            component: 'date',
            options: {
              type: 'datetime',
              placeholder: '请选择',
              valueFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ'
            },
            rules: [{ required: true, message: '不能为空', trigger: 'blur' }]
          }
        ]
      }
    })

    const configTags = computed(() => {
      return {
        treeProps: {
          children: 'fields'
        },
        rowKey: 'id',
        formItems: [
          ...configConditionExpression.value,
          {
            label: '目标结果值',
            name: 'targetValue',
            value: '',
            minWidth: '160',
            component: 'input',
            options: {
              placeholder: '请输入'
            },
            rules: [{ required: true, message: '不能为空', trigger: 'blur' }]
          },
          {
            label: '操作',
            component: 'operation',
            width: '80',
            fixed: 'right',
            options: {
              addDelete: [
                {
                  icon: '',
                  type: 'delete',
                  label: '删除'
                }
              ]
            }
          }
        ]
      }
    })

    // 提交表单
    const handleConfirm = async () => {
      const valid = await formRef.value.validate()
      if (!valid) return
      let item = JSON.parse(JSON.stringify(dataForm.value))
      if (item.mappingType == 'from_multi_sourceField') {
        item.sourceFieldName = item.sourceFieldName.join(',')
      }
      emit('update', item)
      dialogVisible.value = false
    }

    return {
      dialogVisible,
      dataForm,
      formRef,
      formConfig,
      configTags,
      init,
      handleConfirm
    }
  }
}
</script>

<style lang="scss" scoped>
.condition-list {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

.dict-manage {
  display: flex;
  gap: 10px;
  align-items: center;

  .el-select {
    width: 200px;
  }
}

.tip-info {
  margin-top: 20px;
  font-size: 12px;
  color: #999;
}
</style>
