<template>
  <div class="consumer-ist">
    <div class="top-title">
      <strong>筛选条件</strong>
      <el-button v-if="!isView" type="primary" plain @click="add" size="small">新增</el-button>
    </div>
    <el-form class="condition-list" :disabled="isView">
      <div class="top-header">
        <div class="name">字段名称</div>
        <div class="name">操作符</div>
        <div class="name">字段值</div>
        <div class="name">操作</div>
      </div>
      <div class="table-body">
        <template v-for="(item, index) in dataList" :key="item?.id">
          <ConsumerItem v-model="dataList[index]" :isTop="true" />
        </template>
      </div>

      <el-alert v-if="dataList.length == 0" center title="暂无数据" type="info" class="info-tips" :closable="false" />
    </el-form>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, inject } from 'vue'
import ConsumerItem from './item.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const {
  proxy: { $api, $tool, $bus, t }
} = getCurrentInstance()

const isView = computed(() => !!route.query.isView)

// 定义响应式数据模型
const dataList = defineModel({
  type: Array,
  default: []
})

// 添加条件
const add = () => {
  dataList.value.push({
    fieldName: '',
    fieldType: '',
    operator: '',
    fieldValue: ''
  })
}
</script>

<style lang="scss" scoped>
.consumer-ist {
  width: 100%;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
    strong {
      font-size: 14px;
    }
  }
  .info-tips {
    margin-top: 5px;
    padding: 5px;
  }

  .condition-list {
    :deep(.logic-tree) {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .filter-item {
        padding: 16px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
      }
    }
    .top-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
      background: var(--color-table);
      .name {
        padding: 3px 10px;
        font-size: 12px;
        color: #909399;
        font-weight: bold;
        width: 100%;
        &:nth-child(1) {
          width: 240px;
          min-width: 240px;
        }
        &:nth-child(2) {
          width: 140px;
          min-width: 140px;
        }
        &:nth-child(4) {
          width: 45px;
          min-width: 45px;
        }
      }
    }
    .table-body {
      overflow-x: auto;
    }
  }
}
</style>
