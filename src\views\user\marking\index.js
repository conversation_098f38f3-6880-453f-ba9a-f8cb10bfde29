import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'userMarking',
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 标签值分类
    const tagList = ref([])
    const getTagValueType = async () => {
      let res = await $api.userTag.list()
      tagList.value = res.map((x) => ({ label: x.name, value: x.code, tagGroup: x.tagGroup }))
      state.tableHeaders[5].options = tagList.value
    }

    // 标签组
    const tagGroup = ref([])
    const getTagGroup = async () => {
      let res = await $api.userTag.listGroup()
      tagGroup.value = res.map((x) => ({ label: x.name, value: x.id }))
    }

    // 导出
    const confirmExport = async (item, name) => {
      let res = item[name]
      let nameMap = {
        path: t('userMarking.yuanShuJu'),
        successFilePath: t('userMarking.chengGongJiLu'),
        failFilePath: t('userMarking.shiBaiJiLu')
      }
      if (res) {
        const link = document.createElement('a')
        link.href = res
        link.download = `${nameMap[name]}.csv`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        exportDialogVisible.value = false
      } else {
        ElMessage({ type: 'error', message: t('userMarking.DCSBQSHZS') })
      }
    }

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('userMarking.shangChuanYongHu'),
          prop: 'user_like',
          renderType: 'input'
        },
        {
          label: 'skuCode',
          prop: 'skuCode_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('userMarking.shangChuanShiJian'), dataIndex: 'updateTime', width: '180', date: true },
        { title: t('userMarking.shangChuanYongHu'), dataIndex: 'user', width: '180' },
        { title: t('userMarking.wenJianShuLiang'), dataIndex: 'totalCount', width: '100' },
        { title: t('userMarking.daBiaoShuJuLiang'), dataIndex: 'matchCount', width: '110' },
        {
          title: t('userMarking.biaoQianMingCheng'),
          dataIndex: 'name',
          width: '160',
          eleRender: (item) => {
            let tagItem = tagList.value.find((x) => x.value === item.tagCode)
            let tag = tagGroup.value.find((x) => tagItem?.tagGroup === x.value)
            return tag?.label || '--'
          }
        },
        { title: t('userMarking.biaoQianZhiFenLei'), dataIndex: 'tagCode', width: '160' },
        { title: t('userMarking.keHuBiaoShi'), dataIndex: 'matchField', minWidth: '160', tooltip: true }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '320',
        fixed: 'right',
        ellipsis: true,
        operation: [
          {
            clickFun: (item) => confirmExport(item, 'path'),
            label: t('userMarking.yuanShuJuXiaZai'),
            auth: ['cdp.user_marking.download']
          },
          {
            clickFun: (item) => confirmExport(item, 'successFilePath'),
            label: t('userMarking.chengGongJiLuXiaZai'),
            auth: ['cdp.user_marking.download'],
            isShow: (item) => !!item?.successFilePath
          },
          {
            clickFun: (item) => confirmExport(item, 'failFilePath'),
            label: t('userMarking.shiBaiJiLuXiaZai'),
            auth: ['cdp.user_marking.download'],
            isShow: (item) => !!item?.failFilePath
          }
        ]
      })
    })

    onMounted(() => {
      getTagValueType()
      getTagGroup()
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.userMarking
        .page(params)
        .then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = {}
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 修改状态
    const changeStatus = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.userMarking.edit({
        ...item,
        status: value
      })
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/user/markingEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.userMarking.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      edit
    }
  }
}
