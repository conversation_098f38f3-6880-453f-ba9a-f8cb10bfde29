import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'userTag',
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()
    const BAR_DATA = $tool.data.get('BAR_DATA') || {}
    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        delete_ne: true,
        dataRoles_in: BAR_DATA.dataRoleIds
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      selection: [],
      visible: false,
      loading: false
    })

    // 获取类型列表
    const typeList = ref([])
    function buildTree(data, parentId = '0') {
      return data
        .filter((item) => item.parent === parentId)
        .map((item) => ({
          ...item,
          children: buildTree(data, item.id)
        }))
    }
    const getTypeList = async () => {
      let res = await $api.tags.list({ expression: 'delete ne true' })
      typeList.value = res.map((item) => {
        return {
          label: item.name,
          value: item.id,
          ...item
        }
      })

      state.searchOptions[1].options.items = buildTree(typeList.value)
      state.tableHeaders[1].options = typeList.value
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}

      if (type === 'view') {
        query = { id: item.groupId, tagId: item.id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/user/tagEdit', query })
    }

    // 快照统计
    const snapshotStatistics = (item) => {
      router.push({ path: '/user/tagStatistics', query: { id: item.id, tagName: item.name } })
    }

    // 复制
    const copy = async (item, type = 'group') => {
      let query = { ...item, id: $tool.getUUID(), name: `${item.name} - ${t('userTag.fuZhi')}` }
      if (type === 'tag') {
        query.code = $tool.getUUID('', 9)
      }

      if (type === 'group') {
        await $api.userTag.addGroup(query)
        item.children.forEach((child) => {
          child.id = $tool.getUUID()
          child.tagGroup = query.id
          child.code = $tool.getUUID('', 9)
          $api.userTag.add(child)
        })
      }

      if (type === 'tag') {
        await $api.userTag.add(query)
      }
      ElMessage({ type: 'success', message: t('userTag.fuZhiChengGong') })
      getTableData()
    }

    // 用户清单
    const toUserPage = (item) => {
      router.push({ path: '/user/portrait', query: { id: item.code } })
    }

    // 快照历史
    const toHistoryPage = (item) => {
      router.push({ path: '/user/tagHistory', query: { id: item.id, tagName: item.name } })
    }

    const del = async (item, type = 'group') => {
      await ElMessageBox.confirm(
        type == 'group' ? t('userTag.NQDSCDQBQYJE') : t('userTag.NQDSCDQBQM'),
        t('userTag.tiShi'),
        {
          confirmButtonText: t('btn.confirm'),
          cancelButtonText: t('btn.cancel'),
          type: 'warning'
        }
      )
      await $api.userTag[type == 'group' ? 'deleteGroup' : 'delete'](item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    // 导出弹窗相关
    const exportDialogVisible = ref(false)
    const exportModel = ref([])
    const exportModelName = ref('')
    const exportTargetItem = ref(null)
    const getExportModel = async () => {
      exportModel.value = await $api.modelExport.list()
    }
    // 打开导出弹窗
    const openExportDialog = (item) => {
      exportTargetItem.value = item
      exportModelName.value = ''
      exportDialogVisible.value = true
    }
    // 确认导出
    const confirmExport = async () => {
      if (!exportModelName.value) {
        ElMessage({ type: 'warning', message: t('userTag.QXZDCMX') })
        return
      }
      let res = await $api.userTag.export(exportTargetItem.value.id, exportModelName.value)
      if (res) {
        const link = document.createElement('a')
        link.href = res
        link.download = `${exportTargetItem.value.name}.csv`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        exportDialogVisible.value = false
      } else {
        ElMessage({ type: 'error', message: t('userTag.DCSBQSHZS') })
      }
    }
    // 取消导出
    const cancelExport = () => {
      exportDialogVisible.value = false
    }

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('userTag.biaoQianLeiXing'),
          prop: 'type_eq',
          renderType: 'select',
          options: [
            { label: t('userTag.quanBu'), value: 'all' },
            { label: t('userTag.guiZe'), value: 'enumeration' },
            { label: t('userTag.xingWei'), value: 'numerical' },
            { label: t('userTag.shouGong'), value: 'hand' }
          ]
        },
        {
          label: t('userTag.biaoQianFenLei'),
          name: 'tagCategory_eq',
          value: '',
          span: 6,
          component: 'treeSelect',
          options: {
            placeholder: t('userTag.qingXuanZeFenLei'),
            props: { value: 'id', label: 'name' },
            items: []
          }
        },
        {
          label: t('userTag.biaoQianMingCheng'),
          prop: 'name_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        {
          title: `${t('userTag.biaoQianMingCheng')}/${t('userTag.zhi')}`,
          dataIndex: 'name',
          minWidth: '240',
          align: 'left',
          tooltip: true
        },
        { title: t('userTag.biaoQianFenLei'), dataIndex: 'tagCategory', width: '160', align: 'left' },
        { title: t('userTag.biaoQianBianMa'), dataIndex: 'code', width: '120', tooltip: true },
        {
          title: t('userTag.biaoQianLeiXing'),
          dataIndex: 'type',
          width: '160',
          options: [
            { label: t('userTag.quanBu'), value: 'all' },
            { label: t('userTag.guiZe'), value: 'enumeration' },
            { label: t('userTag.xingWei'), value: 'numerical' },
            { label: t('userTag.shouGong'), value: 'hand' }
          ]
        },
        { title: t('userTag.fuGaiRenShu'), dataIndex: 'matchCount', width: '160' },
        { title: t('userTag.biaoQianMiaoShu'), dataIndex: 'description', width: '160' }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '190',
        fixed: 'right',
        ellipsis: true,
        operation: [
          {
            clickFun: (item) => edit(item, 'view'),
            label: t('btn.view'),
            isShow: (item) => !!item?.fullName,
            auth: ['cdp.user_tag.locate']
          },
          {
            clickFun: (item) => edit(item, 'edit'),
            label: t('btn.edit'),
            isShow: (item) => !item?.fullName,
            auth: ['cdp.user_tag_edit']
          },
          {
            clickFun: (item) => copy(item, 'tag'),
            label: t('btn.copy'),
            isShow: (item) => !!item?.fullName,
            auth: ['cdp.user_tag.copy']
          },
          {
            clickFun: (item) => copy(item, 'group'),
            label: t('btn.copy'),
            isShow: (item) => !item?.fullName,
            auth: ['cdp.user_tag.copy']
          },
          {
            clickFun: snapshotStatistics,
            label: t('userTag.kuaiZhaoTongJi'),
            isShow: (item) => !!item?.fullName,
            auth: ['cdp.user_tag_statistics']
          },
          {
            clickFun: toUserPage,
            label: t('userTag.yongHuQingDan'),
            isShow: (item) => !!item?.fullName,
            auth: ['cdp.user_tag.user_list']
          },
          {
            clickFun: toHistoryPage,
            label: t('userTag.kuaiZhaoLiShi'),
            isShow: (item) => !!item?.fullName,
            auth: ['cdp.user_tag_history']
          },
          {
            clickFun: (item) => batchSnapshot(item.id),
            label: t('userTag.zhiXingKuaiZhao'),
            isShow: (item) => !!item?.fullName,
            auth: ['cdp.user_tag_history']
          },
          {
            clickFun: (item) => openExportDialog(item),
            label: t('userTag.daoChu'),
            isShow: (item) => !!item?.fullName,
            auth: ['cdp.user_tag.export']
          },
          {
            clickFun: (item) => del(item, 'tag'),
            label: t('btn.delete'),
            isShow: (item) => !!item?.fullName,
            auth: ['cdp.user_tag.delete']
          },
          {
            clickFun: (item) => del(item, 'group'),
            label: t('btn.delete'),
            isShow: (item) => !item?.fullName,
            auth: ['cdp.user_tag.delete']
          }
        ]
      })
    })

    onMounted(() => {
      getTypeList()
      getExportModel()
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        sort: 'updateTime,DESC',
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.userTag
        .page(params)
        .then(async (res) => {
          const tagListPromises = res.content.map((item) => {
            return $api.userTag
              .tagType({ expression: `delete ne true AND tagGroup eq ${item.id}` })
              .then((tags) => ({ success: true, data: tags }))
              .catch((error) => ({ success: false, data: [], error }))
          })

          const tagListResults = await Promise.all(tagListPromises)

          // 按顺序组合数据
          state.tableData = res.content.map((item, index) => {
            const result = tagListResults[index]
            const list = result.success ? result.data : []

            return {
              ...item,
              matchCount: list.reduce((sum, tag) => sum + (tag.matchCount || 0), 0),
              children: list.map((tag) => ({
                ...tag,
                groupId: item.id,
                tagCategory: item.tagCategory,
                type: item.type
              }))
            }
          })

          state.currentPage = page
          state.totalCount = res.totalElements
        })
        .catch((error) => {
          console.error('Failed to fetch page data:', error)
          // 可以根据需要在这里设置一些错误状态
        })
        .finally(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = { page: 1, size: 10, delete_ne: true, dataRoles_in: BAR_DATA.dataRoleIds }
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 修改状态
    const changeStatus = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.userTag.edit({
        ...item,
        status: value
      })
    }

    // 选中
    const onSelection = (selection) => {
      state.selection = selection.filter((item) => item.tagGroup).map((item) => item.id)
    }

    // 全量、批量快照
    const batchSnapshot = async (tagTarget) => {
      const BAR_DATA = $tool.data.get('BAR_DATA')
      const tenantId = $tool.data.get('tenantId')
      await $api.userTag.batchSnapshot({
        tagTarget: tagTarget || state.selection.toString(),
        collectionId: BAR_DATA.collectionId,
        description: '',
        executeType: 'manual',
        name: 'cdp_tag_snapshot_' + new Date().getTime(),
        taskType: 'cdp_tag_snapshot',
        tenantId: tenantId
      })
      ElMessage({ type: 'success', message: t('userTag.kuaiZhaoZhiXingChengGong') })
    }
    // 全量、批量计算
    const useFlinkEngine = ref(true)
    const batchCalculate = async (tagTarget) => {
      const BAR_DATA = $tool.data.get('BAR_DATA')
      const tenantId = $tool.data.get('tenantId')
      useFlinkEngine.value = true
      if (!tagTarget) {
        try {
          const result = await ElMessageBox.confirm(
            `${t('userTag.shiFouShiYong')} Flink Engine ${t('userTag.jinXingJiSuan')}`,
            t('userTag.jiSuanFangShiXuanZe'),
            {
              confirmButtonText: t('userTag.shi'),
              cancelButtonText: t('userTag.fou'),
              type: 'info'
            }
          )
          useFlinkEngine.value = result === 'confirm'
        } catch (error) {
          useFlinkEngine.value = false // 如果点击取消或关闭，设置为不使用 Flink Engine
        }
      }

      await $api.userTag.batchCalculate({
        tagTarget: tagTarget || state.selection.toString(),
        collectionId: BAR_DATA.collectionId,
        description: '',
        executeType: 'manual',
        name: t('userTag.SDCFBQJSRW'),
        taskType: 'cdp_tag_calculate',
        tenantId: tenantId,
        useFlinkEngine: useFlinkEngine.value
      })
      ElMessage({ type: 'success', message: t('userTag.jiSuanZhiXingChengGong') })
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      edit,
      search,
      onSelection,
      // 导出相关
      exportDialogVisible,
      exportModel,
      exportModelName,
      openExportDialog,
      confirmExport,
      cancelExport,
      //快照、计算
      batchSnapshot,
      batchCalculate
    }
  }
}
