/**
 * 完整解析Cron表达式为中文描述
 * @param {string} cronExpression - 标准Cron表达式
 * @returns {string} 中文描述
 */
function parseCronExpression(cronExpression) {
  // 标准化表达式，处理可能的多空格
  const normalized = cronExpression.trim().replace(/\s+/g, ' ')
  const parts = normalized.split(' ')

  if (parts.length < 5 || parts.length > 7) {
    return '无效的Cron表达式'
  }

  // 补全年份部分（可选）
  const [second, minute, hour, dayOfMonth, month, dayOfWeek, year] = parts.length === 5 ? ['0', ...parts, '*'] : parts

  // 解析各个字段
  const timeDesc = parseTime(second, minute, hour)
  const dateDesc = parseDate(dayOfMonth, month, dayOfWeek)
  const yearDesc = parseYear(year)

  // 组合结果
  let result = ''
  if (dateDesc) result += dateDesc
  if (timeDesc) result += timeDesc
  if (yearDesc) result += yearDesc

  // 特殊处理常见表达式
  const commonPatterns = {
    '0 0 0 * * *': '每天午夜12点整执行',
    '0 0 12 * * *': '每天中午12点整执行',
    '0 0 8-18 * * 1-5': '工作日每天上午8点到下午6点每小时执行',
    '0 0 9 * * 1': '每周一上午9点整执行',
    '0 0 12 1 * *': '每月1日中午12点整执行',
    '0 0 0 1 1 *': '每年1月1日午夜12点整执行'
  }

  return commonPatterns[normalized] || result || '无法解析的Cron表达式'

  // 解析时间部分
  function parseTime(sec, min, hr) {
    const secondDesc = parseField(sec, {
      '*': '每秒',
      0: '',
      default: (val) => `每${val}秒`
    })

    const minuteDesc = parseField(min, {
      '*': '每分钟',
      0: '整',
      default: (val) => `每${val}分`
    })

    const hourDesc = parseField(hr, {
      '*': '每小时',
      default: (val) => {
        const num = parseInt(val, 10)
        const period = num < 12 ? '上午' : '下午'
        const displayHour = num % 12 === 0 ? 12 : num % 12
        return `${period}${displayHour}点`
      }
    })

    // 处理范围
    if (hr.includes('-')) {
      const [start, end] = hr.split('-').map(Number)
      return `从${start}点到${end}点每小时执行`
    }

    // 组合时间描述
    if (secondDesc && minuteDesc && hourDesc) {
      return `${hourDesc}${minuteDesc}${secondDesc}执行`
    }

    return `${hourDesc}${minuteDesc}${secondDesc}`.trim() + '执行'
  }

  // 解析日期部分
  function parseDate(dom, mon, dow) {
    const dayOfMonthDesc = parseField(dom, {
      '*': '',
      '?': '',
      L: '最后一天',
      default: (val) => {
        if (val.includes(',')) {
          return `每月${val.split(',').join('日、')}日`
        }
        if (val.includes('-')) {
          const [start, end] = val.split('-')
          return `每月${start}日到${end}日`
        }
        if (val.includes('/')) {
          const [start, step] = val.split('/')
          return `从${start || '1'}日开始每${step}天`
        }
        return `每月${val}日`
      }
    })

    const monthDesc = parseField(mon, {
      '*': '',
      default: (val) => {
        const months = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二']
        if (val.includes(',')) {
          return `${val
            .split(',')
            .map((m) => `${months[parseInt(m, 10) - 1]}月`)
            .join('、')}`
        }
        if (val.includes('-')) {
          const [start, end] = val.split('-').map((m) => months[parseInt(m, 10) - 1])
          return `${start}月到${end}月`
        }
        return `${months[parseInt(val, 10) - 1]}月`
      }
    })

    const dayOfWeekDesc = parseField(dow, {
      '*': '',
      '?': '',
      '1-5': '工作日',
      '6,7': '周末',
      L: '最后一周',
      default: (val) => {
        const days = ['日', '一', '二', '三', '四', '五', '六']
        if (val.includes(',')) {
          return `每周${val
            .split(',')
            .map((d) => `周${days[parseInt(d, 10)]}`)
            .join('、')}`
        }
        if (val.includes('-')) {
          const [start, end] = val.split('-').map((d) => days[parseInt(d, 10)])
          return `每周${start}到周${end}`
        }
        if (val.includes('#')) {
          const [day, week] = val.split('#')
          return `每月第${week}个周${days[parseInt(day, 10)]}`
        }
        return `每周${days[parseInt(val, 10)]}`
      }
    })

    // 组合日期描述
    let result = ''
    if (monthDesc) result += monthDesc
    if (dayOfMonthDesc) result += dayOfMonthDesc
    if (dayOfWeekDesc) result += dayOfWeekDesc

    // 默认描述
    if (!monthDesc && !dayOfMonthDesc && !dayOfWeekDesc) {
      return '每天'
    }

    return result
  }

  // 解析年份部分
  function parseYear(yr) {
    return parseField(yr, {
      '*': '',
      default: (val) => {
        if (!val) return ''
        if (val?.includes('/')) {
          const [start, step] = val.split('/')
          return `从${start}年开始每${step}年`
        }
        return `${val}年`
      }
    })
  }

  // 通用字段解析
  function parseField(value, options) {
    if (value in options) {
      return options[value]
    }

    if (options.default) {
      if (typeof options.default === 'function') {
        return options.default(value)
      }
      return options.default
    }

    return ''
  }
}

export default parseCronExpression
