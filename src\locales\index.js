import sysConfig from '@/config'
import store from '@/store'
import tool from '@/utils/tool'
import el_en from 'element-plus/es/locale/lang/en'
import el_zh_cn from 'element-plus/es/locale/lang/zh-cn'
import { createI18n } from 'vue-i18n'

// 静态加载的基础翻译文件
import en_us from './lang/en-us.js'
import zh_cn from './lang/zh-cn.js'

// 挂载在线组件库国际化内容
import { uiI18n } from 'lw-cdp-ui'

// 获取本地语言并初始化 i18n 实例
const MESSAGE_LIST = tool.data.get(`MESSAGE_LIST`) || {}
const localMessages = MESSAGE_LIST[sysConfig.APP_NAME] || {}

// 初始化静态翻译内容
const messages = {
  'zh-cn': {
    el: el_zh_cn,
    ...localMessages['zh_cn'],
    ...zh_cn
  },
  'en-us': {
    el: el_en,
    ...localMessages['en_us'],
    ...en_us
  }
}

// 使用 import.meta.glob 进行静态导入
const locales = import.meta.glob('../views/**/locale/*.js', { eager: true })

// 遍历所有翻译文件并按语言合并内容
Object.keys(locales).forEach((path) => {
  const matched = path.match(/locale\/([a-z-]+)\.js$/)
  if (matched && matched[1]) {
    const locale = matched[1].toLowerCase()
    if (!messages[locale]) {
      messages[locale] = {}
    }
    Object.assign(messages[locale], locales[path].default)
  }
})

// 合并深层对象
const deepMerge = (target, source) => {
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (source[key] instanceof Object && target[key] instanceof Object) {
        target[key] = deepMerge({ ...target[key] }, source[key])
      } else {
        target[key] = source[key]
      }
    }
  }
  return target
}

const allMessages = deepMerge(messages, uiI18n)
MESSAGE_LIST[sysConfig.APP_NAME] = allMessages
tool.data.set('MESSAGE_LIST', MESSAGE_LIST)
store.commit('setRouteShow', false)

// 创建 i18n 实例
const i18n = createI18n({
  locale: tool.data.get('APP_LANG') || sysConfig.LANG, // 默认语言
  fallbackLocale: 'en', // 回退语言
  globalInjection: true, // 允许在全局使用 $t 函数
  messages: allMessages // 合并后的翻译内容
})

export default i18n
