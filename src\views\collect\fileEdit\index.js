import lwMappingEdit from '@/components/lwMappingEdit/index.vue'
import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'collectFileEdit',
  components: {
    lwMappingEdit
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const dataForm = ref({
      name: '',
      aliasName: '',
      mappingList: []
    })
    const dataFormRef = ref(null)
    const mappingEditRef = ref(null)
    const loading = ref(false)

    // 存储模型
    const persistentlList = ref([])
    const getPersistentList = async () => {
      let res = await $api.modelPersistent.list()
      persistentlList.value = res.map((item) => ({ label: item.aliasName, value: item.id }))
    }

    // 传输模型
    const transmissionList = ref([])
    const getTransmissionList = async () => {
      let res = await $api.modelTransmission.list()
      transmissionList.value = res.map((item) => ({ label: item.aliasName, value: item.id }))
    }

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('collectFileEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('collectFileEdit.jieKouMingCheng'),
            name: 'aliasName',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('collectFileEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('collectFileEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('collectFileEdit.jieKouBianMa'),
            name: 'name',
            value: '',
            span: 12,
            component: 'input',
            options: {
              placeholder: t('collectFileEdit.qingShuRu')
            },
            tips: t('collectFileEdit.ZNWXXZMSZHXH'),
            rules: [
              { required: true, message: t('collectFileEdit.qingShuRu'), trigger: 'blur' },
              { pattern: /^[a-z0-9_]+$/, message: t('collectFileEdit.ZNWXXZMSZHXH'), trigger: 'blur' },
              { max: 20, message: t('collectFileEdit.CDBNCGGZF'), trigger: 'blur' }
            ]
          },
          {
            label: t('collectFileEdit.jieKouShiQu'),
            name: 'defaultTimezone',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('collectFileEdit.qingXuanZe'),
              items: timeList.value
            },
            rules: [{ required: true, message: t('collectFileEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('collectFileEdit.chuanShuMoXing'),
            name: 'transferModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('collectFileEdit.qingXuanZe'),
              items: transmissionList.value
            },
            tips: t('collectFileEdit.KXZCSMXZYJLD'),
            rules: [{ required: true, message: t('collectFileEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('collectFileEdit.shiFouJiLuRiZhi'),
            name: 'needLogging',
            value: true,
            span: 6,
            component: 'switch',
            options: {
              activeText: t('collectFileEdit.jiLu'),
              inactiveText: t('collectFileEdit.buJiLu')
            },
            tips: t('collectFileEdit.SFXYJLRZKQHJ')
          },
          {
            label: t('collectFileEdit.shuJuShiFouCunChu'),
            name: 'needPersistent',
            value: false,
            span: 6,
            component: 'switch',
            options: {
              activeText: t('collectFileEdit.cunChu'),
              inactiveText: t('collectFileEdit.buCunChu')
            },
            tips: t('collectFileEdit.KQHXXZCCMXHZ')
          },
          {
            label: t('collectFileEdit.cunChuMoXing'),
            name: 'persistentModelId',
            value: '',
            span: 12,
            component: 'select',
            options: {
              placeholder: t('collectFileEdit.qingXuanZe'),
              items: persistentlList.value
            },
            tips: t('collectFileEdit.KXZCCMXZYJLD'),
            rules: [{ required: true, message: t('collectFileEdit.qingXuanZe'), trigger: 'blur' }],
            hideHandle: '!$.needPersistent'
          },
          {
            label: t('collectFileEdit.beiZhu'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              type: 'textarea',
              placeholder: t('collectFileEdit.qingShuRu')
            }
          },
          { label: t('collectFileEdit.wenJian'), component: 'divider' },
          {
            label: t('collectFileEdit.wenJianQianZhui'),
            name: 'uploadFileModel.startPrefix',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('collectFileEdit.qingShuRu')
            },
            tips: t('collectFileEdit.wenJianDeQianZhui')
          },
          {
            label: t('collectFileEdit.wenJianHouZhui'),
            name: 'uploadFileModel.endPrefix',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('collectFileEdit.qingShuRu')
            },
            tips: t('collectFileEdit.wenJianDeHouZhui')
          },
          {
            label: t('collectFileEdit.fenGeFu'),
            name: 'uploadFileModel.separator',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('collectFileEdit.qingShuRu')
            },
            tips: t('collectFileEdit.WJZDGZDDFGF')
          },
          { label: t('collectFileEdit.yingShePeiZhi'), component: 'divider' },
          { component: 'mappingEdit' }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.collectFile.info({ id: id.value })
      dataForm.value = res
    }

    // 获取时区
    const timeList = ref([])
    const getTime = async () => {
      let res = await $api.collectApi.time({ id: id.value })
      timeList.value = res.map((item) => ({ label: item, value: item }))
    }

    onMounted(() => {
      loading.value = true
      Promise.all([getPersistentList(), getTransmissionList(), getTime()])
        .then(() => {
          if (id.value) {
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          await $api.collectFile[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('collectFileEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/collect/file', 'collectFile')
    }

    return {
      dataForm,
      mappingEditRef,
      dataFormRef,
      config,
      loading,
      isView,
      save,
      close
    }
  }
}
