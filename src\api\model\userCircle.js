import config from '@/config'
import request from '@/utils/request'

export default {
  // 分页
  page: async (params) => {
    let url = `${config.API_URL}/cdp-portal/audience/{collectionId}`
    return await request.get(url, params)
  },
  // 列表
  list: async (params) => {
    let url = `${config.API_URL}/cdp-portal/audience/{collectionId}/list`
    return await request.get(url, params)
  },
  // 详情
  info: async (params) => {
    let url = `${config.API_URL}/cdp-portal/audience/{collectionId}/${params.id}`
    return await request.get(url)
  },
  // 修改
  edit: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/audience/{collectionId}`
    return await request.put(url, data)
  },
  // 新增
  add: async (data = {}) => {
    let url = `${config.API_URL}/cdp-portal/audience/{collectionId}`
    return await request.post(url, data)
  },
  // 删除
  delete: async (id) => {
    let url = `${config.API_URL}/cdp-portal/audience/{collectionId}/${id}`
    return await request.delete(url)
  },
  // 批量快照
  batchSnapshot: async (data = {}) => {
    let url = `${config.API_URL}/cdp-scheduler/cdp_task/{collectionId}/audience_snapshot`
    return await request.post(url, data)
  },
  // 导出
  export: async (id, name) => {
    let url = `${config.API_URL}/cdp-portal/export/{collectionId}/${name}/${id}/audience`
    return await request.get(url)
  },
  // 快照统计
  snapshotStatistics: async (params) => {
    let url = `${config.API_URL}/cdp-portal/external/collections/{collectionId}/audience_snapshot`
    return await request.get(url, params)
  },
  // 导出快照
  exportSnapshot: async (params) => {
    let url = `${config.API_URL}/cdp-portal/export/es/{collectionId}/audience_customer_snapshot`
    return await request.get(url, params)
  }
}
