/**
 * 搜索类型sql拼接
 * @param obj
 * @return expressionStr
 *  ne 不等于
 *  eq 等于
 *  lt 小于
 *  le 小于等于
 *  ge 大于等于
 *  gt 大于
 *  or 或者
 *  like 模糊查询
 */
export function expression(obj) {
  let expressionStr = ''
  let expressionList = []
  for (let item in obj) {
    if (obj[item] !== '' && obj[item]?.length !== 0 && item.includes('_')) {
      let field, condition

      if (item.includes('_Q_')) {
        // 如果包含 _Q_，则将其拆分成字段名和条件部分
        ;[field, condition] = item.split('_Q_')
      } else {
        // 否则正常处理
        ;[field, condition] = item.split('_', 2)
      }

      let list = condition ? [field, ...condition.split('_')] : [field]

      if (list.length === 1) {
        // 处理非or和非区间的情况
        list.push(obj[item])
        expressionList = [...expressionList, 'AND', `${list[0]} = ${list[1]}`]
      } else if (list.length === 2 && list[1] !== 'or') {
        // 处理非or和非区间的情况
        expressionList = [...expressionList, 'AND', `${list[0]} ${list[1]} ${obj[item]}`]
      } else if (list[1] === 'or') {
        // 处理or
        if (list.indexOf('eq') === -1 && list.indexOf('like') === -1) {
          list.splice(2, 0, 'eq')
        }
        let sqlList = []
        let sqlKey = list[2]
        let keyList = list.slice(3)
        keyList.forEach((it) => {
          sqlList.push(`${it} ${sqlKey} '${obj[item]}'`)
        })
        expressionList = [...expressionList, 'AND', `(${sqlList.join(' OR ')})`]
      } else {
        // 处理多重关系并存情况，例如ge/le
        let sqlList = []
        let key = list[0]
        list.slice(1).forEach((it, index) => {
          sqlList.push(`${key} ${it} '${Array.isArray(obj[item]) ? obj[item][index] : obj[item]}'`)
        })
        expressionList = [...expressionList, 'AND', `${sqlList.join(' AND ')}`]
      }
    }
  }
  // 处理expressionList第一个是AND的问题
  if (expressionList[0] === 'AND') {
    expressionList.shift()
  }
  expressionStr = expressionList.length > 0 ? expressionList.join(' ') : ''
  return expressionStr
}
