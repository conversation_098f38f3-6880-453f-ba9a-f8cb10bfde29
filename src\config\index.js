// 本地配置
const DEFAULT_CONFIG = {
  //标题
  APP_NAME: 'CDP',
  //首页地址
  DASHBOARD_URL: '/dashboard',
  //初始化地址
  INIT_URL: '/initialization',
  //接口地址
  API_MOCK: 'https://apifoxmock.com/m1/5291868-0-default',
  API_URL: '/api',
  //请求超时
  TIMEOUT: 30000,
  //TokenName
  TOKEN_NAME: 'Authorization',
  //追加其他头
  HEADERS: {},
  //请求是否开启缓存
  REQUEST_CACHE: false,
  //布局 默认：default | 通栏：header | 经典：menu | 功能坞：dock | 新拟态：soft
  //dock将关闭标签和面包屑栏
  LAYOUT: 'menu',
  //菜单是否折叠
  MENU_IS_COLLAPSE: false,
  //菜单是否启用手风琴效果
  MENU_UNIQUE_OPENED: true,
  //是否开启多标签
  LAYOUT_TAGS: true,
  //语言
  LANG: 'zh-cn',
  //主题颜色
  COLOR: '#D51B28',
  //是否开启动态菜单
  MENU_DYNAMIC: false
}

// ----------------------------- UAT环境配置
const UAT_CONFIG = {
  API_URL: '/api',
  MENU_DYNAMIC: true
}

// ----------------------------- 正式环境配置
const PROD_CONFIG = {
  API_URL: '/api',
  MENU_DYNAMIC: true
}

// UAT产模式，就合并动态的
if (process.env.NODE_ENV === 'uat') {
  Object.assign(DEFAULT_CONFIG, UAT_CONFIG)
}

// 生产模式，就合并动态的
if (process.env.NODE_ENV === 'prod') {
  Object.assign(DEFAULT_CONFIG, PROD_CONFIG)
}

export default DEFAULT_CONFIG
