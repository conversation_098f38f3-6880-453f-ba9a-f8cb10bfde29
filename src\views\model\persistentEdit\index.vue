<template>
  <el-container>
    <el-main>
      <lwFormMini ref="dataFormRef" :config="config" :isView="isView" v-model="dataForm" :loading="loading">
        <template #optionsTable>
          <lwTableForm :config="configTags" v-model="dataForm.options" :isView="isView" />
        </template>
        <template #lwFieldsEdit>
          <lwFieldsEdit
            v-model="dataForm.fields"
            :dataIdentifyFieldName="dataForm.dataPersistentInfo.dataIdentifyFieldName"
            :timestampFieldName="dataForm.dataPersistentInfo.timestampFieldName"
            :persistentType="dataForm.dataPersistentInfo.persistentType"
            :identifyValueFieldNames="identifyValueFieldNames"
            @onFieldDataFlag="setFieldDataFlag" />
        </template>
        <template #lwIdentityEdit>
          <lwIdentityEdit
            v-if="dataForm.dataPersistentInfo.persistentType == 'behavior'"
            v-model="dataForm.dataPersistentInfo.consumerBehaviorIdentifies"
            :fields="dataForm.fields" />
          <lwIdentityEdit
            v-else
            v-model="dataForm.dataPersistentInfo.consumerMainBodyIdentifies"
            :fields="dataForm.fields" />
        </template>
      </lwFormMini>
    </el-main>
    <el-footer class="footer-body">
      <el-button @click="close">{{ $t('btn.close') }}</el-button>
      <el-button v-if="!isView" type="primary" @click="save">{{ $t('btn.save') }}</el-button>
    </el-footer>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
