export default {
  name: 'arrange',
  path: '/arrange',
  sort: 5,
  meta: {
    icon: 'el-icon-set-up',
    type: 'menu',
    title: 'menu.arrange.name',
    roles: ['cdp.arrange']
  },
  redirect: '/arrange/redirect',
  children: [
    {
      name: 'arrangeRedirect',
      path: '/arrange/redirect',
      component: 'arrange/redirect',
      sort: 0,
      meta: {
        icon: 'el-icon-operation',
        type: 'menu',
        title: 'menu.arrange.redirect',
        roles: ['cdp.arrange_redirect']
      },
      children: [
        {
          name: 'arrangeRedirectEdit',
          path: '/arrange/redirectEdit',
          component: 'arrange/redirectEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.arrange.redirectEdit',
            active: '/arrange/redirect',
            hidden: true
          }
        }
      ]
    }
  ]
}
