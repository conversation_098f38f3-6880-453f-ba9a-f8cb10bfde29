export default {
  taskRecord: {
    shuJuZhongFang: 'Data Replay',
    shuJuDingYue: 'Data Subscription',
    biaoQianJiSuan: 'Tag Calculation',
    shouDongBiaoQian: 'Manual Tagging',
    shuJuChouQu: 'Data Extraction',
    biao<PERSON><PERSON><PERSON><PERSON><PERSON>: 'Tag Snapshot',
    ren<PERSON><PERSON><PERSON><PERSON><PERSON>: 'Crowd Snapshot',
    shouDongRenQunQuan<PERSON>uan: 'Manual Crowd Selection',
    quanBu: 'All',
    jin<PERSON><PERSON><PERSON><PERSON>: 'In Progress',
    cheng<PERSON>ong: 'Success',
    quXiao: 'Cancel',
    shi<PERSON>ai: 'Failed',
    mingCheng: 'Name',
    zhuang<PERSON><PERSON>: 'Status',
    leiXing: 'Type',
    zhiXingFangShi: 'Execution Mode',
    zhou<PERSON><PERSON>: 'Cycle',
    shouDong: 'Manual',
    jinDu: 'Progress',
    shuLiang: 'Quantity',
    kai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Start Time',
    jie<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'End Time',
    shuoMing: 'Description',
    zhongZhiRenWu: 'Terminate Task',
    QDQXRWM: 'Stop task?',
    qu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Stopped'
  }
}
