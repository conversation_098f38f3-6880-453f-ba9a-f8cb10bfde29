import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import locale from 'element-plus/es/locale/lang/zh-cn'
import i18n from '@/locales'
import router from '@/router'
import store from '@/store'
import App from '@/App.vue'
import LWUI from '@/lwui'
import '@/assets/scss/index'

const app = createApp(App)

app.config.globalProperties.t = i18n.global.t
app.use(i18n)
app.use(store)
app.use(router)
app.use(ElementPlus, { locale })
app.use(LWUI)
app.mount('#app')

