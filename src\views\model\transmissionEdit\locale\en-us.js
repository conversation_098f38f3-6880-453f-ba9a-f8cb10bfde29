export default {
  modelTransmissionEdit: {
    jiBenXinXi: 'Basic Information',
    moXingMingCheng: 'Model Name',
    qingShuRu: 'Please Enter',
    moXingZhongWen: 'Model Chinese Name',
    miaoShuXinXi: 'Description',
    gouJianMoXing: 'Build Model',
    baoCunChengGong: 'Save Successful',
    QXZYBJDZD: 'Please select the field to edit',
    mingCheng: 'Name',
    ziDuanMingCheng: 'Field Name',
    zhongWenMing: 'Chinese Name',
    leiXing: 'Type',
    qingXuanZe: 'Please Select',
    zhu<PERSON><PERSON>: 'Primary Key',
    qing<PERSON>uan<PERSON>e<PERSON>hu<PERSON>ian: 'Please select primary key',
    ziDuanJiaoYan<PERSON>ang<PERSON>hi: 'Field Validation Method',
    wuXuJiaoYan: 'No Validation Required',
    shou<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Phone Validation',
    zhengZeJiaoYan: 'Regex Validation',
    zhengZeBiaoDaShi: 'Regular Expression',
    yunXu<PERSON><PERSON><PERSON><PERSON>: 'Allow Empty',
    shi<PERSON>ou<PERSON>hu<PERSON>u: 'Is Array',
    SRGJZJXGL: 'Enter keywords to filter',
    bian<PERSON><PERSON>: 'JSON Editor',
    moXingLieBiao: 'Model List',
    ziDuanPeiZhi: 'Field Configuration',
    QJSXSBNTZCCZ: 'Please paste the JSON to be identified here. Note: No punctuation at the end, such as commas',
    QXZXYSCDX: 'Please select items to delete',
    QRSCYXZDCSM: 'Confirm to delete selected parameters?',
    tiShi: 'Notification',
    shanChu: 'Delete',
    shanChuChengGong: 'Successfully Deleted'
  }
}
