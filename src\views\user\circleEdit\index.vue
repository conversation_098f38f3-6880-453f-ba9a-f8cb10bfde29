<template>
  <el-container>
    <el-main v-loading="loading">
      <lwFormMini ref="dataFormRef" :config="config" :isView="isView" v-model="dataForm">
        <template #ConsumerListBtn>
          <el-button v-if="!isView" type="primary" plain @click="add" size="small">{{$t('userCircleEdit.xinZeng')}}</el-button>
        </template>
        <template #ConsumerList>
          <ConsumerItem v-model="dataForm.queryCondition" :isTop="true" />
          <el-alert
            v-if="dataForm.queryCondition.conditions.length == 0"
            center
            :title="$t('userCircleEdit.QTJYHSXTJ')"
            type="info"
            class="info-tips" />
        </template>
        <template #OtherList>
          <OtherFilter v-model="dataForm" />
        </template>
      </lwFormMini>
    </el-main>
    <el-footer class="footer-body">
      <el-button @click="close">{{ $t('btn.close') }}</el-button>
      <el-button v-if="!isView" type="primary" @click="save">{{ $t('btn.save') }}</el-button>
    </el-footer>
  </el-container>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import './index.scss';
</style>
