export default {
  name: 'collect',
  path: '/collect',
  sort: 4,
  meta: {
    icon: 'el-icon-cpu',
    type: 'menu',
    title: 'menu.collect.name',
    roles: ['cdp.collect']
  },
  redirect: '/collect/api',
  children: [
    {
      name: 'collectApi',
      path: '/collect/api',
      component: 'collect/api',
      sort: 0,
      meta: {
        icon: 'el-icon-sort',
        type: 'menu',
        title: 'menu.collect.api',
        roles: ['cdp.collect_api']
      },
      children: [
        {
          name: 'collectApiEdit',
          path: '/collect/apiEdit',
          component: 'collect/apiEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.collect.apiEdit',
            active: '/collect/api',
            hidden: true
          }
        }
      ]
    },
    {
      name: 'collectDatabase',
      path: '/collect/database',
      component: 'collect/database',
      sort: 1,
      meta: {
        icon: 'el-icon-coin',
        type: 'menu',
        title: 'menu.collect.database',
        roles: ['cdp.collect_database']
      },
      children: [
        {
          name: 'collectDatabaseEdit',
          path: '/collect/databaseEdit',
          component: 'collect/databaseEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.collect.databaseEdit',
            active: '/collect/database',
            hidden: true
          }
        }
      ]
    },
    {
      name: 'collectFile',
      path: '/collect/file',
      component: 'collect/file',
      sort: 2,
      meta: {
        icon: 'el-icon-document',
        type: 'menu',
        title: 'menu.collect.file',
        roles: ['cdp.collect_file']
      },
      children: [
        {
          name: 'collectFileEdit',
          path: '/collect/fileEdit',
          component: 'collect/fileEdit',
          sort: 0,
          meta: {
            icon: 'el-icon-edit',
            type: 'menu',
            title: 'menu.collect.fileEdit',
            active: '/collect/file',
            hidden: true
          }
        }
      ]
    }
  ]
}
