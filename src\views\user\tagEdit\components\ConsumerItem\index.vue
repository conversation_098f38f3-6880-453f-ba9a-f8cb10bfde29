<template>
  <div class="consumer-item">
    <div class="relation-left" v-if="dataForm?.conditions?.length > 1">
      <div class="line">
        <el-button class="relation" type="primary" plain size="small" :disabled="isView" @click="toggleRelation">
          {{ dataForm.relation === 'or' ? $t('userTagEdit.huo') : $t('userTagEdit.qie') }}
        </el-button>
        <div class="line-horizontal-t"></div>
        <div class="line-horizontal-b"></div>
      </div>
    </div>
    <el-form class="consumer-body" :disabled="isView">
      <template v-for="(item, index) in dataForm.conditions" :key="item?.id">
        <template v-if="item.conditions.length > 0">
          <ConsumerItem v-model="dataForm.conditions[index]" />
        </template>
        <template v-else>
          <FieldSelect
            v-model="dataForm.conditions[index]"
            :isAdd="index == 0 && !isTop"
            @add="handleAdd(index)"
            @remove="handleRemove(index)" />
        </template>
      </template>
    </el-form>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, inject, onBeforeMount, onBeforeUnmount } from 'vue'
import FieldSelect from './FieldSelect.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const isView = computed(() => !!route.query.isView)
defineOptions({
  name: 'ConsumerItem'
})
const {
  proxy: { $api, $tool, $bus, t }
} = getCurrentInstance()

// 定义响应式数据模型
const dataForm = defineModel({
  type: Object,
  default: { conditions: [] }
})
const props = defineProps({
  isTop: {
    type: Boolean,
    default: false
  }
})

const toggleRelation = (item) => {
  dataForm.value.relation = dataForm.value.relation === 'or' ? 'and' : 'or'
}

// 处理添加条件
const handleAdd = (index) => {
  if (index > -1) {
    dataForm.value.conditions.splice(index + 1, 0, {
      dataPersistentModelId: dataForm.value.conditions[index].dataPersistentModelId || '',
      fieldName: '',
      fieldType: '',
      aggregation: '',
      aggregationList: [],
      operator: '',
      value: '',
      valueSource: 'fixed_value',
      consumerFieldName: '',
      targetFieldName: '',
      rangeType: 'default',
      rangeFieldName: '',
      relation: 'and',
      timeStr: '',
      conditions: [],
      id: $tool.getUUID()
    })
  }
}

// 处理删除条件
const handleRemove = (index) => {
  if (index > -1) {
    dataForm.value.conditions.splice(index, 1)

    // 判断是不是剩下最后一个
    if (dataForm.value.conditions.length === 1 && !props.isTop) {
      // 获取要删除的条件
      const condition = dataForm.value.conditions[0]

      // 如果该条件有字段配置，就将其移动到上一级
      if (condition.fieldName || condition.operator || condition.value !== undefined) {
        // 将当前条件的值复制到父级
        Object.keys(condition).forEach((key) => {
          if (key !== 'conditions') {
            dataForm.value[key] = condition[key]
          }
        })
        // 清空conditions数组
        dataForm.value.conditions = []
      } else {
        // 如果没有字段配置，直接删除
        dataForm.value.conditions.splice(index, 1)
      }
    } else {
    }
  }
}

</script>
<style lang="scss" scoped>
.consumer-item {
  display: flex;
  width: 100%;
  .relation-left {
    position: relative;
    width: 20px;
    min-width: 20px;
    margin: 0 10px 0 15px;

    .line {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 2px;
      background-color: var(--el-color-primary-light-5);
      display: flex;
      align-items: center;
      justify-content: center;

      .relation {
        position: absolute;
        z-index: 1;
      }

      .line-horizontal-t,
      .line-horizontal-b {
        position: absolute;
        width: 20px;
        height: 2px;
        background-color: var(--el-color-primary-light-5);
        left: 0;
      }

      .line-horizontal-t {
        top: 0;
      }

      .line-horizontal-b {
        bottom: 0;
      }
    }
  }
  .consumer-body {
    width: 100%;
  }
}
</style>
