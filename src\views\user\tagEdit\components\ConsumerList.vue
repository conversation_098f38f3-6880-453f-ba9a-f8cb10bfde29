<template>
  <div class="consumer-ist">
    <div class="top-title">
      <strong>{{ $t('userTagEdit.yongHuShuXingManZu') }}</strong>
      <el-button v-if="!isView" type="primary" plain @click="add" size="small">{{
        $t('userTagEdit.xinZeng')
      }}</el-button>
    </div>
    <div class="condition-list">
      <ConsumerItem v-model="dataForm" :isTop="true" />
      <el-alert
        v-if="dataForm.conditions.length == 0"
        center
        :title="$t('userTagEdit.zanWuShuJu')"
        type="info"
        class="info-tips"
        :closable="false" />
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, inject } from 'vue'
import ConsumerItem from './ConsumerItem/index.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const {
  proxy: { $api, $tool, $bus, t }
} = getCurrentInstance()

const isView = computed(() => !!route.query.isView)

// 定义响应式数据模型
const dataForm = defineModel({
  type: Object,
  default: () => ({
    conditions: []
  })
})

// 添加条件
const options = inject('optionsPersistent')
const add = () => {
  if (!dataForm.value.conditions) {
    dataForm.value.conditions = []
  }
  dataForm.value.conditions.push({
    dataPersistentModelId: options.value.consumerMap.id || '',
    fieldName: '',
    fieldType: '',
    aggregation: '',
    aggregationList: [],
    operator: '',
    value: '',
    valueSource: 'fixed_value',
    consumerFieldName: '',
    targetFieldName: '',
    rangeType: 'default',
    rangeFieldName: '',
    relation: 'and',
    timeStr: '',
    conditions: []
  })
}
</script>

<style lang="scss" scoped>
.consumer-ist {
  width: 100%;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 16px;
    strong {
      font-size: 14px;
    }
  }
  .info-tips {
    margin-top: 5px;
    padding: 5px;
  }
  .condition-list {
    :deep(.logic-tree) {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .filter-item {
        padding: 16px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
      }
    }
  }
}
</style>
