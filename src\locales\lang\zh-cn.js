export default {
  btn: {
    all: '全选',
    operation: '操作',
    submit: '提交',
    cancel: '取消',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    confirmDelete: '确定删除吗？',
    deleteSuccess: '删除成功',
    confirm: '确认',
    tips: '提示',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    search: '搜索',
    refresh: '刷新',
    upload: '上传',
    download: '下载',
    close: '关闭',
    apply: '应用',
    reset: '重置',
    add: '添加',
    view: '查看',
    more: '更多',
    copy: '复制',
    filter: '筛选',
    sort: '排序',
    export: '导出',
    import: '导入',
    query: '查询'
  },
  layout: {
    topbar: '所在位置',
    userData: '个人中心',
    outLogin: '退出登录',
    online: '在线',
    configuring: '配置中',
    clearCache: '清除缓存'
  },

  user: {
    settings: '设置',
    nightmode: '黑夜模式',
    color: '主题颜色',
    layout: '框架布局',
    menu: '折叠菜单',
    tag: '标签栏',
    lang: '国际化',
    nightmode_msg: '适合光线较弱的环境，当前黑暗模式为beta版本',
    language: '语言',
    language_msg: '翻译进行中，暂翻译了本视图的文本'
  },
  menu: {
    dashboard: '工作台',
    dataWarehouse: '数据仓库',
    dataRouter: {
      list: '数据路由',
      edit: '数据路由编辑'
    },
    task: {
      list: '任务中心',
      edit: '任务编辑'
    },
    model: {
      name: '数据建模',
      persistent: '存储模型',
      persistentEdit: '存储模型编辑',
      transmission: '传输模型',
      transmissionEdit: '传输模型编辑',
      export: '导出模型',
      exportEdit: '导出模型编辑',
      portrait: '画像模型',
      portraitEdit: '画像模型编辑'
    },
    user: {
      name: '用户洞察',
      portrait: '用户画像',
      portraitEdit: '用户画像详情',
      tag: '标签管理',
      tagEdit: '标签编辑',
      tagStatistics: '标签统计',
      tagHistory: '标签快照',
      marking: '手动打标',
      markingEdit: '手动打标编辑',
      circle: '人群圈选',
      circleStatistics: '人群统计',
      circleHistory: '人群快照',
      circleEdit: '人群圈选编辑',
      userOther: '洞察数据'
    },
    dataInsight: {
      name: '数据洞察'
    },
    collect: {
      name: '数据采集',
      api: 'API',
      apiEdit: 'API编辑',
      database: '数据库',
      databaseEdit: '数据库编辑',
      file: '文件',
      fileEdit: '文件编辑'
    },
    arrange: {
      name: '数据编排',
      redirect: '数据转储',
      redirectEdit: '数据转储编辑'
    },
    subscribe: {
      name: '数据订阅',
      api: 'API',
      apiEdit: 'API编辑',
      database: '数据库',
      databaseEdit: '数据库编辑'
    },
    task: {
      name: '任务中心',
      deploy: '任务配置',
      deployEdit: '任务配置编辑',
      record: '执行记录',
      recordEdit: '执行记录编辑'
    },
    search: {
      name: '数据查询',
      edit: '数据查询编辑'
    },
    tags: '标签分类'
  }
}
