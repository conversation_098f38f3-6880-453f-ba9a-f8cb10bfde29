<template>
  <div class="lw-mapping-edit">
    <el-table
      ref="table"
      row-key="fieldName"
      :data="mappingList"
      v-if="mappingList.length > 0"
      v-loading="!transferList.length || !persistentList.length">
      <el-table-column label="存储数据" align="left">
        <template #default="scope">
          <div v-if="scope.row.sourceFieldName && scope.row.mappingType != 'fixed_value'" class="field-item left">
            <div class="title">
              <span>名称</span>
              {{ mapping.persistent?.[scope.row.sourceFieldName]?.aliasName || '--' }}
            </div>
            <div class="code">
              <span>编码</span>
              {{ scope.row.sourceFieldName || '--' }}
            </div>
          </div>
          <div v-else-if="scope.row.fixedValue && scope.row.mappingType == 'fixed_value'" class="field-item left">
            <div class="title">
              <span>固定值</span>
              {{ scope.row.fixedValue || '--' }}
            </div>
          </div>
          <div v-else class="field-item no-mapping">{{ !scope.row.children ? '未配置' : '当前字段不可映射' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="映射方式" align="center" width="200">
        <template #default="scope">
          <div class="mapping-type" :class="[scope.row.mappingType]">
            <div v-if="!scope.row.children" class="title">
              <span>{{ mappingTypeMap[scope.row.mappingType] || '--' }}</span>
              <el-button
                v-if="!($store.state.user?.status || isView)"
                type="primary"
                link
                icon="el-icon-edit"
                @click="handleEdit(scope.$index, scope.row)"></el-button>
            </div>
            <div v-else class="title"><span>当前字段不可配置</span></div>
            <span class="left-right"></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="传输数据" align="left">
        <template #default="scope">
          <div class="field-item right">
            <div class="title"><span>名称</span>{{ scope.row.fieldName || '--' }}</div>
            <div class="code">
              <span>编码</span>
              {{ scope.row.destFieldName || '--' }}
              <el-divider direction="vertical" />
              <span>权重</span>
              {{ scope.row.weight }}
              <el-divider direction="vertical" />
              <span>允许映射</span>
              {{ !scope.row.children ? '是' : '否' }}
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-empty v-else description="请选择存储模型&传输模型" />

    <!-- 编辑映射 -->
    <MappingEdit
      ref="mappingEditRef"
      :transferList="transferList"
      :persistentList="persistentList"
      :mappingTypes="mappingTypeMap"
      @update="handleMappingUpdate" />
  </div>
</template>
<script>
import MappingEdit from './edit.vue'
import { watch, getCurrentInstance, ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
export default {
  components: {
    MappingEdit
  },
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    transferId: {
      type: String,
      default: ''
    },
    persistentId: {
      type: String,
      default: ''
    },
    height: {
      type: [String, Number],
      default: ''
    },
    isTransfer: {
      type: Boolean,
      default: false
    },
    isSubscribe: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const mappingList = ref(props.modelValue || [])
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()
    const isView = computed(() => !!route.query.isView)

    watch(
      () => mappingList.value,
      (newVal) => {
        emit('update:modelValue', newVal)
      },
      { deep: true }
    )

    const mapping = ref({ transfer: {}, persistent: {} })
    const transferList = ref([])
    const persistentList = ref([])
    const mappingTypeMap = {
      from_sourceField: '直接映射',
      from_multi_sourceField: '多个字段映射',
      from_dictionary: '字典映射',
      fixed_value: '固定值',
      condition_exp: '区间映射',
      no_mapping: '不映射'
    }

    const convertToTree = (data) => {
      // 创建根节点映射和结果数组
      const result = []
      const map = {}

      // 第一次遍历,创建所有节点
      data.forEach((item) => {
        // 处理字段路径
        const paths = item.fieldName.split('.')
        const node = {
          ...item,
          children: []
        }

        // 如果是根节点(没有.)
        if (paths.length === 1) {
          map[item.fieldName] = node
          result.push(node)
        }
      })

      // 第二次遍历,建立父子关系
      data.forEach((item) => {
        const paths = item.fieldName.split('.')
        if (paths.length > 1) {
          // 找到父节点
          const parentPath = paths[0]
          const parentNode = map[parentPath]

          if (parentNode) {
            // 创建子节点
            const childNode = {
              ...item,
              children: []
            }
            parentNode.children.push(childNode)
          }
        }
      })

      return result
    }

    // 添加构建树形数据的方法
    const buildTreeData = (list, existingMappings = []) => {
      // 创建现有映射的查找Map
      const existingMap = existingMappings.reduce((acc, curr) => {
        acc[curr.destFieldName] = curr
        return acc
      }, {})

      // 按level分组
      const groupByLevel = list.reduce((acc, curr) => {
        const level = curr.level
        if (!acc[level]) {
          acc[level] = []
        }
        acc[level].push(curr)
        return acc
      }, {})

      // 构建树形结构
      const result = []

      // 处理level 0的数据
      groupByLevel[0].forEach((item) => {
        // 创建基础映射对象
        const mappingItem = {
          fieldName: item.fieldName,
          destFieldName: item.fieldName,
          sourceFieldName: '',
          mappingType: 'no_mapping',
          weight: 0,
          multiSourceFieldModel: {
            separator: '',
            sourceFieldNames: []
          },
          cryptoOperation: 'NONE',
          needCrypto: false,
          ...existingMap[item.fieldName]
        }

        // 如果是嵌套类型，需要处理子项
        if (item.fieldType === 'nested') {
          mappingItem.children = groupByLevel[1]
            .filter((child) => child.fieldName.startsWith(`${item.fieldName}.`))
            .map((child) => ({
              fieldName: child.fieldName,
              destFieldName: child.fieldName,
              sourceFieldName: '',
              mappingType: 'no_mapping',
              weight: 0,
              multiSourceFieldModel: {
                separator: '',
                sourceFieldNames: []
              },
              cryptoOperation: 'NONE',
              needCrypto: false,
              ...existingMap[child.fieldName]
            }))
        }

        result.push(mappingItem)
      })

      return result
    }

    watch(
      () => props.transferId,
      async (newVal) => {
        if (newVal) {
          // 获取传输数据列表
          $api[props.isTransfer ? 'modelPersistent' : 'modelTransmission'].mapping(newVal).then((res) => {
            mapping.value.transfer = res.reduce((acc, curr) => {
              acc[curr.fieldName] = curr
              return acc
            }, {})
            // 构建树形数据结构
            mappingList.value = buildTreeData(res, props.modelValue)
            transferList.value = convertToTree(res)
          })
        }
      },
      { immediate: true, deep: true }
    )
    watch(
      () => props.persistentId,
      (newVal) => {
        if (newVal) {
          $api[props.isSubscribe ? 'modelTransmission' : 'modelPersistent'].mapping(newVal).then((res) => {
            mapping.value.persistent = res.reduce((acc, curr) => {
              acc[curr.fieldName] = curr
              return acc
            }, {})

            persistentList.value = convertToTree(res)
          })
        }
      },
      { immediate: true, deep: true }
    )

    // 编辑映射
    const mappingEditRef = ref(null)
    const handleEdit = (index, row) => {
      if (row.children) {
        ElMessage.warning('当前字段不可配置')
        return
      }
      mappingEditRef.value.init(row)
    }

    // 更新映射
    const handleMappingUpdate = (updatedRow) => {
      // 在 mappingList 中找到对应项并更新
      const index = mappingList.value.findIndex((item) => item.destFieldName === updatedRow.destFieldName)
      if (index > -1) {
        mappingList.value[index] = { ...mappingList.value[index], ...updatedRow }
      }

      // 如果是嵌套字段，需要处理子字段的更新
      if (mappingList.value[index]?.children?.length) {
        const childIndex = mappingList.value[index].children.findIndex(
          (child) => child.destFieldName === updatedRow.destFieldName
        )
        if (childIndex > -1) {
          mappingList.value[index].children[childIndex] = {
            ...mappingList.value[index].children[childIndex],
            ...updatedRow
          }
        }
      }
    }

    return {
      mappingList,
      transferList,
      mappingTypeMap,
      mapping,
      persistentList,
      mappingEditRef,
      isView,
      handleEdit,
      handleMappingUpdate
    }
  }
}
</script>
<style lang="scss" scoped>
.lw-mapping-edit {
  width: 100%;
  .field-item {
    padding: 5px 10px;
    border: 1px dashed #dcdfe6;
    width: 100%;
    height: 60px;
    &.right {
      border-color: var(--el-color-primary-light-3);
    }
    .title {
      font-size: 14px;
      color: #333333;
      display: flex;
      align-items: center;
      gap: 5px;
      font-weight: bolder;

      span {
        color: #999999;
        min-width: 30px;
        font-weight: normal;
      }
    }
    .code {
      font-size: 12px;
      color: #909399;
      display: flex;
      align-items: center;
      gap: 5px;
      font-weight: bolder;

      span {
        color: #909399;
        min-width: 30px;
        font-weight: normal;
      }
    }
  }
  .no-mapping {
    width: 100%;
    font-size: 12px;
    color: #909399;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  :deep(.cell) {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .mapping-type {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    &.no_mapping {
      .title {
        color: #909399;
      }
      .left-right {
        background: #dcdfe6;
        &:before {
          display: none;
        }
      }
    }
    .title {
      display: flex;
      align-items: center;
      line-height: 20px;
      color: #333333;
    }
    .left-right {
      width: 100%;
      height: 2px;
      background: var(--el-color-primary);
      position: relative;
      &:before {
        content: '';
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -9px;
        border: 8px solid transparent;
        border-left: 15px solid var(--el-color-primary);
        width: 0;
        height: 0;
      }
    }
  }
}
</style>
