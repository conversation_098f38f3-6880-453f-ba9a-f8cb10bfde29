import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'taskRecord',
  setup() {
    const router = useRouter()
    const store = useStore()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    // 获取类型列表
    const typeObject = [
      { label: t('taskRecord.shuJuZhongFang'), value: 'cdp_data_replay' },
      { label: t('taskRecord.shuJuDingYue'), value: 'cdp_data_dispatch' },
      { label: t('taskRecord.biaoQianJiSuan'), value: 'cdp_tag_calculate' },
      { label: t('taskRecord.shouDongBiaoQian'), value: 'cdp_hand_tag_assign' },
      { label: t('taskRecord.shuJuChouQu'), value: 'cdp_data_extractor' },
      { label: t('taskRecord.biaoQianKuaiZhao'), value: 'cdp_tag_snapshot' },
      { label: t('taskRecord.renQunKuaiZhao'), value: 'cdp_audience_snapshot' },
      { label: t('taskRecord.shouDongRenQunQuanXuan'), value: 'cdp_audience_snapshot_assign' }
    ]

    const statues = [
      {
        label: t('taskRecord.quanBu'),
        value: 'all'
      },
      {
        label: t('taskRecord.jinXingZhong'),
        value: 'running'
      },
      {
        label: t('taskRecord.chengGong'),
        value: 'finish'
      },
      {
        label: t('taskRecord.quXiao'),
        value: 'cancel'
      },
      {
        label: t('taskRecord.shiBai'),
        value: 'error'
      }
    ]

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('taskRecord.mingCheng'),
          prop: 'name_like',
          renderType: 'input'
        },
        {
          label: t('taskRecord.zhuangTai'),
          prop: 'status_like',
          renderType: 'select',
          options: statues
        }
      ]

      state.tableHeaders = [
        { title: t('taskRecord.mingCheng'), dataIndex: 'name', minWidth: '220', tooltip: true },
        {
          title: t('taskRecord.leiXing'),
          dataIndex: 'taskType',
          width: '120',
          options: typeObject
        },
        {
          title: t('taskRecord.zhiXingFangShi'),
          dataIndex: 'executeType',
          width: '90',
          options: [
            { label: t('taskRecord.zhouQi'), value: 'period' },
            { label: t('taskRecord.shouDong'), value: 'manual' }
          ]
        },
        { title: t('taskRecord.zhuangTai'), dataIndex: 'status', width: '70', options: statues },
        {
          title: t('taskRecord.jinDu'),
          dataIndex: 'progress',
          width: '160',
          eleRender: (scope) => {
            return `<div class="el-progress-bar"><div class="el-progress-bar__outer" style="height: 18px;"><div class="el-progress-bar__inner" style="width: ${scope.progress}%; animation-duration: 3s;"><div class="el-progress-bar__innerText"><span>${scope.progress}%</span></div></div></div></div>`
          }
        },
        { title: t('taskRecord.shuLiang'), dataIndex: 'jobCount', width: '90' },
        { title: t('taskRecord.kaiShiShiJian'), dataIndex: 'beginTime', width: '180', date: true },
        { title: t('taskRecord.jieShuShiJian'), dataIndex: 'endTime', width: '180', date: true },
        { title: t('taskRecord.shuoMing'), dataIndex: 'description', minWidth: '180', tooltip: true }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '150',
        fixed: 'right',
        ellipsis: true,
        operation: [
          { clickFun: (item) => edit(item, 'view'), label: t('btn.view'), auth: ['cdp.task_record.view'] },
          {
            clickFun: del,
            label: t('taskRecord.zhongZhiRenWu'),
            isShow: (item) => store.state.user?.status && item.status == 'running',
            auth: ['cdp.task_record.pause']
          }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        sort: 'beginTime,desc',
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.taskRecord
        .page(params)
        .then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = {}
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 修改状态
    const changeStatus = (value, item) => {
      if (!item?.id) {
        return false
      }
      $api.taskRecord.edit({
        ...item,
        status: value
      })
    }

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}
      if (type === 'view') {
        query = { id: item.id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/task/deployEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('taskRecord.QDQXRWM'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.taskRecord.cancel(item.id)
      ElMessage({ type: 'success', message: t('taskRecord.quXiaoChengGong') })
      getTableData()
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      edit
    }
  }
}
