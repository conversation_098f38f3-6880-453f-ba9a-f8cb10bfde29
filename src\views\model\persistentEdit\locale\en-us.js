export default {
  modelPersistentEdit: {
    keHu: 'Customer',
    xing<PERSON><PERSON>: 'Behavior',
    puTong: 'General',
    jiBenXinXi: 'Basic Information',
    moXingLeiXing: 'Model Type',
    qingXuanZe: 'Please Select',
    yongYu<PERSON><PERSON>o<PERSON>ian: 'For Labeling',
    yunXu: 'Allowed',
    jin<PERSON><PERSON>: 'Prohibited',
    yongYuShiTu: 'For 360 View',
    shiFouWeiWaiBuBiao: 'Is External Table',
    shi: 'Yes',
    fou: 'No',
    moXingMingCheng: 'Model Name',
    qingShuRu: 'Please Enter',
    MXDYWMCJZCYW: 'Model English Name (English Only)',
    moXingZhongWen: 'Model Chinese Name',
    MXDZWMC: 'Model Chinese Name',
    shuJuJueSe: 'Data Role',
    kuoZhanXinXi: 'Extended Information',
    gouJianMoXing: 'Build Model',
    shenFenBiaoShi: 'Identity Identifier',
    mingCheng: 'Name',
    buNengWeiKong: 'Cannot Be Empty',
    pei<PERSON>hi: 'Configuration',
    cao<PERSON>uo: 'Operation',
    shan<PERSON>hu: 'Delete',
    bao<PERSON>un<PERSON>heng<PERSON>ong: 'Save Successful',
    shuJuBiaoShi: 'Data Identifier',
    sheZhiChengGong: 'Setup Successful',
    shiJianChuo: 'Timestamp',
    QXZYBJDZD: 'Please Select Field to Edit',
    ziDuanMingCheng: 'Field Name',
    zhongWenMing: 'Chinese Name',
    leiXing: 'Type',
    zhuJian: 'Primary Key',
    qingXuanZeZhuJian: 'Please Select Primary Key',
    ziDuanJiaoYanFangShi: 'Field Validation Method',
    wuXuJiaoYan: 'No Validation',
    shouJiJiaoYan: 'Phone Validation',
    zhengZeJiaoYan: 'Regex Validation',
    zhengZeBiaoDaShi: 'Regular Expression',
    yunXuWeiKong: 'Allow Empty',
    shiFouShuZu: 'Is Array',
    SZYSSFHB: 'Merge Array Elements',
    jinCunChu: 'Storage Only',
    ziDianYingShe: 'Dictionary Mapping',
    yongYuShouGongBiaoQian: 'For Manual Labeling',
    yanMaLeiXing: 'Mask Type',
    shouJiYanMa: 'Phone Mask',
    zuoJiYanMa: 'Landline Mask',
    diZhiYanMa: 'Address Mask',
    ziDingYiYanMa: 'Custom Mask',
    yanMaQiShiWeiZhi: 'Mask Start Position',
    yanMaWeiShu: 'Mask Digits',
    xiangQingXianShi: 'Detail Display',
    lieBiaoXianShi: 'List Display',
    zhanShi: 'SHA Display',
    zhiJieZhanShi: 'Direct Display',
    buZhanShi: 'No Display',
    yanMaZhanShi: 'Mask Display',
    ziDianJi: 'Dictionary Set',
    biaoQianXuanZeFangShi: 'Label Selection Method',
    shouDongTianXie: 'Manual Entry',
    danXuanShuRu: 'Single Selection',
    duoXuanShuRu: 'Multiple Selection',
    xuanZeShuJu: 'Select Data',
    yongYuSouSuo: 'For Search',
    yunXuSouSuo: 'Allow Search',
    jinZhiSouSuo: 'Prohibit Search',
    souSuoFangShi: 'Search Method',
    wanQuanPiPei: 'Exact Match',
    moHuSouSuo: 'Fuzzy Search',
    xiaoYuShuRuZhi: 'Less Than Input',
    daYuShuRuZhi: 'Greater Than Input',
    quJian: 'Range',
    xianShiLeiXing: 'Display Type',
    shiFouHuanHang: 'Line Break',
    yunXuHuanHang: 'Allow Line Break',
    jinZhiHuanHang: 'Prohibit Line Break',
    riQiGeShi: 'Date Format',
    SRGJZJXGL: 'Enter Keywords to Filter',
    CZDWSJBS: 'This Field is a Data Identifier',
    bianJi: 'JSON Edit',
    moXingLieBiao: 'Model List',
    ziDuanPeiZhi: 'Field Configuration',
    sheWeiShiJianChuo: 'Set as Timestamp',
    QJSXSBNTZCCZ: 'Paste the JSON to be identified here. Note: No punctuation at the end, such as commas.',
    xinZiDuan: 'New Field',
    QXZXYSCDX: 'Please Select Items to Delete',
    QRSCYXZDCSM: 'Confirm Deletion of Selected Parameters?',
    tiShi: 'Prompt',
    shanChuChengGong: 'Delete Successful',
    QXZYBJDBS: 'Please Select Identifier to Edit',
    ziDuan: 'Field',
    qianZhuiLeiXing: 'Prefix Type',
    guDingZhi: 'Fixed Value',
    yiYouZiDuan: 'Existing Field',
    qianZhui: 'Prefix',
    zhuYaoBiaoJi: 'Primary Marker',
    yunXuXiuGai: 'Allow Modification',
    biaoShiLieBiao: 'Identifier List',
    biaoShiPeiZhi: 'Identifier Configuration'
  }
}
