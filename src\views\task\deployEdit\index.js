import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'taskDeployEdit',
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const collectionId = $tool.data.get('collectionId')
    const tenantId = $tool.data.get('tenantId')
    const dataForm = ref({ taskType: 'cdp_data_replay', collectionId, tenantId })
    const dataFormRef = ref(null)
    const loading = ref(false)

    // 获取路由
    const routeList = ref([])
    const getRouteList = async () => {
      let tenantId = $tool.data.get('tenantId')
      let res = await $api.taskDeploy.route({ expression: `tenantId eq ${tenantId}` })
      routeList.value = res.map((x) => ({ label: x.name, value: x.id }))
    }
    // 获取订阅
    const routingList = ref([])
    const getRoutingList = async () => {
      let res = await $api.taskDeploy.routing()
      routingList.value = res.map((x) => ({ label: x.name, value: x.id }))
    }
    // 数据抽取
    const databaseList = ref([])
    const getDatabaseList = async () => {
      let res = await $api.collectDatabase.list()
      databaseList.value = res.map((x) => ({ label: x.name, value: x.id }))
    }
    // 数据抽取
    const circleList = ref([])
    const getCircleList = async () => {
      let res = await $api.userCircle.list()
      let list = res.map((x) => ({ label: x.name, value: x.id }))
      circleList.value = [{ label: t('taskDeployEdit.quanBu'), value: '*' }, ...list]
    }

    // 标签
    const tagList = ref([])
    const getTagList = async () => {
      let res = await $api.userTag.list({ expression: 'delete ne true' })
      tagList.value = res.map((x) => ({ label: x.name, value: x.id, targetType: x.targetType, tagType: x.tagType }))
    }
    const tagFilter = (name, value) => {
      let list = tagList.value.filter((x) => x[name] === value)
      return [{ label: t('taskDeployEdit.quanBu'), value: '*' }, ...list]
    }

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('taskDeployEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('taskDeployEdit.renWuMingCheng'),
            name: 'name',
            value: '',
            span: 8,
            component: 'input',
            options: {
              maxlength: 50,
              placeholder: t('taskDeployEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('taskDeployEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('taskDeployEdit.renWuShiJianFanWei'),
            name: 'time',
            value: [],
            component: 'date',
            span: 8,
            options: {
              type: 'datetimerange',
              startPlaceholder: t('taskDeployEdit.kaiShiShiJian'),
              endPlaceholder: t('taskDeployEdit.jieShuShiJian'),
              valueFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ'
            },
            rules: [{ required: true, message: t('taskDeployEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('taskDeployEdit.pinLü'),
            name: 'timingWheelPeriodModel.expression',
            value: '',
            span: 8,
            component: 'cronSelect',
            rules: [{ required: true, message: t('taskDeployEdit.qingShuRu'), trigger: 'blur' }]
          },
          { label: t('taskDeployEdit.renWuLeiXing'), component: 'divider' },
          {
            name: 'taskType',
            value: 'cdp_data_replay',
            span: 24,
            component: 'taskTypeSelect'
          },
          {
            label: t('taskDeployEdit.zhongFangLeiXing'),
            component: 'divider',
            hideHandle: '$.taskType != "cdp_data_replay"'
          },
          {
            name: 'replayType',
            value: 'routing',
            span: 24,
            component: 'updateUserSelect',
            hideHandle: '$.taskType != "cdp_data_replay"'
          },
          {
            label: t('taskDeployEdit.zhongFangDuiXiang'),
            component: 'divider',
            hideHandle: '$.taskType != "cdp_data_replay"'
          },
          {
            name: 'replayTarget',
            value: '',
            span: 24,
            component: 'select',
            options: {
              placeholder: t('taskDeployEdit.qingXuanZe'),
              items: routeList.value
            },
            hideHandle: '!($.taskType == "cdp_data_replay" && $.replayType == "routing")'
          },
          {
            name: 'replayTarget',
            value: '',
            span: 24,
            component: 'select',
            options: {
              placeholder: t('taskDeployEdit.qingXuanZe'),
              items: routingList.value
            },
            hideHandle: '!($.taskType == "cdp_data_replay" && $.replayType == "subscribe")'
          },
          {
            label: t('taskDeployEdit.biaoQianBiaoShi'),
            component: 'divider',
            hideHandle: '$.taskType != "cdp_tag_calculate"'
          },
          {
            name: 'tagTarget',
            value: [],
            span: 24,
            component: 'select',
            options: {
              multiple: true,
              placeholder: t('taskDeployEdit.qingXuanZe'),
              items: tagFilter('tagType', 'auto')
            },
            hideHandle: '$.taskType != "cdp_tag_calculate"'
          },
          {
            label: t('taskDeployEdit.chouQuDuiXiang'),
            component: 'divider',
            hideHandle: '$.taskType != "cdp_data_extractor"'
          },
          {
            name: 'dataExtractorId',
            value: '',
            span: 24,
            component: 'select',
            options: {
              placeholder: t('taskDeployEdit.qingXuanZe'),
              items: databaseList.value
            },
            hideHandle: '$.taskType != "cdp_data_extractor"'
          },
          {
            label: t('taskDeployEdit.biaoQianKuaiZhao'),
            component: 'divider',
            hideHandle: '$.taskType != "cdp_tag_snapshot"'
          },
          {
            name: 'tagTarget',
            value: [],
            span: 24,
            component: 'select',
            options: {
              multiple: true,
              placeholder: t('taskDeployEdit.qingXuanZe'),
              items: tagFilter('tagType', 'auto')
            },
            hideHandle: '$.taskType != "cdp_tag_snapshot"'
          },
          {
            label: t('taskDeployEdit.renQunKuaiZhao'),
            component: 'divider',
            hideHandle: '$.taskType != "cdp_audience_snapshot"'
          },
          {
            name: 'audienceTarget',
            value: [],
            span: 24,
            component: 'select',
            options: {
              multiple: true,
              placeholder: t('taskDeployEdit.qingXuanZe'),
              items: circleList.value
            },
            hideHandle: '$.taskType != "cdp_audience_snapshot"'
          },
          {
            label: t('taskDeployEdit.renQunKuaiZhaoXiangQing'),
            name: 'enableDetail',
            value: false,
            span: 24,
            component: 'switch',
            options: {
              activeText: t('taskDeployEdit.kaiQi'),
              inactiveText: t('taskDeployEdit.guanBi')
            },
            message: t('taskDeployEdit.QYKZXQKNHDZS'),
            hideHandle: '$.taskType != "cdp_audience_snapshot"'
          }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api[isView.value ? 'taskRecord' : 'taskDeploy'].info({ id: id.value })
      if (res.taskJson) {
        let tasjJson = JSON.parse(res.taskJson)
        res = {
          ...res,
          ...tasjJson
        }
      }
      if (res?.timingWheelPeriodModel?.startTime) {
        res.time = [res.timingWheelPeriodModel.startTime, res.timingWheelPeriodModel.endTime]
      }

      if (res.audienceTarget) {
        res.audienceTarget = res.audienceTarget.split(',')
      }
      if (res.tagTarget) {
        res.tagTarget = res.tagTarget.split(',')
      }

      dataForm.value = res
    }

    onMounted(() => {
      loading.value = true
      Promise.all([getRouteList(), getDatabaseList(), getCircleList(), getRoutingList(), getTagList()])
        .then(() => {
          if (id.value) {
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { time, ...params } = JSON.parse(JSON.stringify(dataForm.value))

          if (time?.length) {
            params.timingWheelPeriodModel.startTime = time[0]
            params.timingWheelPeriodModel.endTime = time[1]
          }

          params.useFlinkEngine = true
          params.executeType = 'period'

          let apiName = ''
          switch (params.taskType) {
            case 'cdp_data_replay':
              delete params.tagTarget
              apiName = 'data_replay'
              break
            case 'cdp_tag_calculate':
              delete params.replayTarget
              apiName = 'tag_calculate'
              break
            case 'cdp_data_extractor':
              delete params.replayTarget
              apiName = 'data_extractor'
              break
            case 'cdp_tag_snapshot':
              params.tagTarget = params.tagTarget.toString()
              apiName = 'tag_snapshot'
              break
            case 'cdp_audience_snapshot':
              params.audienceTarget = params.audienceTarget.toString()
              apiName = 'audience_snapshot'
              break
          }

          params.taskJson = JSON.stringify(params)

          await $api.taskDeploy[id.value && !copy.value ? 'editReplay' : 'addReplay'](params, apiName)
          ElMessage({ type: 'success', message: t('taskDeployEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump(
        isView.value ? '/task/record' : '/task/deploy',
        isView.value ? 'taskRecord' : 'taskDeploy'
      )
    }

    // 监听taskType切换
    watch(
      () => dataForm.value.replayType,
      (val, old) => {
        if (val && old && val != old) {
          dataForm.value.replayTarget = ''
        }
      },
      { immediate: true }
    )

    return {
      dataForm,
      dataFormRef,
      config,
      loading,
      isView,
      id,
      save,
      close
    }
  }
}
