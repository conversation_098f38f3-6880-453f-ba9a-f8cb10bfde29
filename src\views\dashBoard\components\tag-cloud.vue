<template>
  <div class="cloud" ref="cloudContainer" :style="{ width: '100%', height: height + 'px' }" @touchmove="listener">
    <div
      class="cloud-item"
      v-for="cloud of clouds"
      :key="cloud.text"
      :style="{
        transform: `translate3d(${cloud.x}px,${cloud.y}px,${cloud.z}px) scale(${cloud.scale})`,
        opacity: cloud.opacity,
        color: cloud.color,
        fontSize: cloud.fontSize + 'px'
      }">
      {{ cloud.text }}
    </div>
  </div>
</template>

<script>
import { computed, onMounted, onUnmounted, ref } from 'vue'

export default {
  props: {
    tags: {
      type: Array,
      default: () => []
    },
    width: {
      type: String,
      default: '100'
    },
    height: {
      type: String,
      default: '170'
    },
    radius: {
      type: String,
      default: '90'
    }
  },

  setup(props) {
    let speedX = Math.PI / 360
    let speedY = Math.PI / 360
    const clouds = ref([])
    const cloudContainer = ref(null)

    // 计算最小和最大值用于大小比例
    const minValue = computed(() => {
      return Math.min(...props.tags.map((tag) => tag.value))
    })

    const maxValue = computed(() => {
      return Math.max(...props.tags.map((tag) => tag.value))
    })

    // 球心坐标
    const CX = computed(() => {
      return parseInt(props.width) === 100
        ? (cloudContainer.value?.offsetWidth || window.innerWidth || document.documentElement.clientWidth) / 3.5
        : parseInt(props.width) / 3.5
    })

    const CY = computed(() => {
      return parseInt(props.height) / 3.5
    })

    // 创建标签
    const createTags = () => {
      const newClouds = []
      const minSize = 12 // 最小字体大小
      const maxSize = 30 // 最大字体大小

      for (let i = 0, len = props.tags.length; i < len; i++) {
        let k = -1 + (2 * (i + 1) - 1) / len
        let a = Math.acos(k)
        let b = a * Math.sqrt(len * Math.PI)

        // 计算字体大小比例 (0-1)
        const valueRatio = (props.tags[i].value - minValue.value) / (maxValue.value - minValue.value)
        // 计算实际字体大小
        const fontSize = minSize + valueRatio * (maxSize - minSize)

        const cloud = {
          x: CX.value + parseInt(props.radius) * Math.sin(a) * Math.cos(b),
          y: CY.value + parseInt(props.radius) * Math.sin(a) * Math.sin(b),
          z: parseInt(props.radius) * Math.cos(a),
          text: props.tags[i].name,
          color: props.tags[i].color || getRandomColor(),
          fontSize: fontSize,
          scale: (400 + parseInt(props.radius) * Math.cos(a)) / 600,
          opacity: (400 + parseInt(props.radius) * Math.cos(a)) / 600
        }
        newClouds.push(cloud)
      }
      return newClouds
    }

    // 生成随机颜色
    const getRandomColor = () => {
      const letters = '0123456789ABCDEF'
      let color = '#'
      for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)]
      }
      return color
    }

    // 云球旋转函数-X
    const rotateX = (angleX) => {
      let cos = Math.cos(angleX)
      let sin = Math.sin(angleX)
      for (let cloud of clouds.value) {
        let _y = (cloud.y - CY.value) * cos - cloud.z * sin + CY.value
        let _z = cloud.z * cos + (cloud.y - CY.value) * sin
        cloud.y = _y
        cloud.z = _z
        cloud.scale = (400 + cloud.z) / 600
        cloud.opacity = (400 + cloud.z) / 600
      }
    }

    // 云球旋转函数-Y
    const rotateY = (angleY) => {
      let cos = Math.cos(angleY)
      let sin = Math.sin(angleY)
      for (let cloud of clouds.value) {
        let _x = (cloud.x - CX.value) * cos - cloud.z * sin + CX.value
        let _z = cloud.z * cos + (cloud.x - CX.value) * sin
        cloud.x = _x
        cloud.z = _z
        cloud.scale = (400 + cloud.z) / 600
        cloud.opacity = (400 + cloud.z) / 600
      }
    }

    // 定时器
    let timer
    onMounted(() => {
      clouds.value = []
      // 初始化云标签
      clouds.value = createTags()

      timer = setInterval(() => {
        rotateX(speedX)
        rotateY(speedY)
      }, 20)
    })

    onUnmounted(() => {
      clearInterval(timer)
    })

    // 手指滑动事件
    const listener = (event) => {
      let x = event.touches[0].pageX - CX.value
      let y = event.touches[0].pageX - CY.value
      speedX =
        x * 0.0001 > 0
          ? Math.min(parseInt(props.radius) * 0.00002, x * 0.0001)
          : Math.max(-parseInt(props.radius) * 0.00002, x * 0.0001)
      speedY =
        y * 0.0001 > 0
          ? Math.min(parseInt(props.radius) * 0.00002, y * 0.0001)
          : Math.max(-parseInt(props.radius) * 0.00002, y * 0.0001)
    }

    return {
      clouds,
      width: props.width,
      height: props.height,
      cloudContainer,
      listener
    }
  }
}
</script>

<style lang="scss" scoped>
.cloud {
  position: relative;
  width: 100%;
  height: 170px;
  .cloud-item {
    position: absolute;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: bold;
    white-space: nowrap;
    transition: all 2s linear;
  }
}
</style>
