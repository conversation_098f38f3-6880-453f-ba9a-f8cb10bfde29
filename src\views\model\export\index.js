import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'modelExport',
  setup() {
    const router = useRouter()
    const store = useStore()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      visible: false,
      loading: false
    })

    let persistentType = {
      consumer: t('modelExport.keHu'),
      behavior: t('modelExport.xingWei'),
      normal: t('modelExport.puTong')
    }

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('modelExport.jieKouMingCheng'),
          prop: 'aliasName_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('modelExport.jieKouMingCheng'), dataIndex: 'aliasName', minWidth: '120', tooltip: true },
        { title: t('modelExport.jieKouBianMa'), dataIndex: 'name', width: '220', tooltip: true },
        { title: t('modelExport.chuanShuMoXing'), dataIndex: 'transferModelName', width: '220', tooltip: true },
        { title: t('modelExport.cunChuMoXing'), dataIndex: 'persistentModelName', width: '220', tooltip: true }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '180',
        fixed: 'right',
        ellipsis: true,
        operation: [
          {
            clickFun: (item) => edit(item, 'view'),
            label: t('btn.view'),
            isShow: () => store.state.user?.status,
            auth: ['cdp.model_export_edit']
          },
          {
            clickFun: edit,
            label: t('btn.edit'),
            isShow: () => !store.state.user?.status,
            auth: ['cdp.model_export_edit']
          },
          {
            clickFun: del,
            label: t('btn.delete'),
            auth: ['cdp.model_export.delete']
          }
        ]
      })
    })

    onMounted(() => {
      getTableData()
    })

    const getTableData = async (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        sort: 'createTime,DESC',
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      let res = await $api.modelExport.page(params)

      state.tableData = res.content
      state.currentPage = page
      state.totalCount = res.totalElements
      state.loading = false
    }

    const reset = () => {
      state.searchParams = {
        page: 1,
        size: 10
      }
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    const edit = (item, type) => {
      let query = { id: item.id }
      if (type == 'view') {
        query.isView = true
      }
      router.push({ path: '/model/exportEdit', query })
    }

    const del = async (item) => {
      await ElMessageBox.confirm(t('btn.confirmDelete'), t('btn.tips'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.modelExport.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    const add = () => {
      router.push({ path: '/model/exportEdit' })
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      add
    }
  }
}
