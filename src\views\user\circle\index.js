import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'userCircle',
  setup() {
    const router = useRouter()
    const {
      proxy: { $api, $expression, t, $tool, $bus }
    } = getCurrentInstance()

    const state = reactive({
      searchOptions: [],
      searchParams: {
        page: 1,
        size: 10,
        delete_ne: true
      },
      layout: { grid: 4, labelWidth: '0', hideLabel: true, labelAlign: 'right' },
      tableData: [],
      totalCount: 0,
      tableHeaders: [],
      selection: [],
      visible: false,
      loading: false
    })

    // 查看 新增 编辑
    const edit = (item, type = 'add') => {
      let query = {}

      if (type === 'view') {
        query = { id: item.id, tagId: item.id, isView: true }
      }
      if (type === 'edit') {
        query = { id: item.id }
      }
      router.push({ path: '/user/circleEdit', query })
    }

    // 快照统计
    const snapshotStatistics = (item) => {
      router.push({ path: '/user/circleStatistics', query: { id: item.id, tagName: item.name } })
    }

    // 用户清单
    const toUserPage = (item) => {
      router.push({ path: '/user/portrait', query: { id: item.id, type: 'cdp_audience' } })
    }

    // 快照历史
    const toHistoryPage = (item) => {
      router.push({ path: '/user/circleHistory', query: { id: item.id, tagName: item.name } })
    }

    const del = async (item, type = 'group') => {
      await ElMessageBox.confirm(t('userCircle.QDSCGRQM'), t('userCircle.tiShi'), {
        confirmButtonText: t('btn.confirm'),
        cancelButtonText: t('btn.cancel'),
        type: 'warning'
      })
      await $api.userCircle.delete(item.id)
      ElMessage({ type: 'success', message: t('btn.deleteSuccess') })
      getTableData()
    }

    // 导出弹窗相关
    const exportDialogVisible = ref(false)
    const exportModel = ref([])
    const exportModelName = ref('')
    const exportTargetItem = ref(null)
    const getExportModel = async () => {
      exportModel.value = await $api.modelExport.list()
    }
    // 打开导出弹窗
    const openExportDialog = (item) => {
      exportTargetItem.value = item
      exportModelName.value = ''
      exportDialogVisible.value = true
    }
    // 确认导出
    const confirmExport = async () => {
      if (!exportModelName.value) {
        ElMessage({ type: 'warning', message: t('userCircle.QXZDCMX') })
        return
      }
      let res = await $api.userCircle.export(exportTargetItem.value.id, exportModelName.value)
      if (res) {
        const link = document.createElement('a')
        link.href = res
        link.download = `${exportTargetItem.value.name}.csv`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        exportDialogVisible.value = false
      } else {
        ElMessage({ type: 'error', message: t('userCircle.DCSBQSHZS') })
      }
    }
    // 取消导出
    const cancelExport = () => {
      exportDialogVisible.value = false
    }

    // 复制
    const copy = async (item, type = 'group') => {
      let query = { ...item, id: $tool.getUUID(), name: `${item.name} - ${t('userTag.fuZhi')}` }
      delete query.createTime
      delete query.updateTime
      delete query.updateUser
      delete query.createUser
      await $api.userCircle.add(query)
      ElMessage({ type: 'success', message: t('userTag.fuZhiChengGong') })
      getTableData()
    }

    onBeforeMount(() => {
      state.searchOptions = [
        {
          label: t('userCircle.liWuMingCheng'),
          prop: 'name_like',
          renderType: 'input'
        },
        {
          label: 'skuCode',
          prop: 'skuCode_like',
          renderType: 'input'
        }
      ]

      state.tableHeaders = [
        { title: t('userCircle.renQunMingCheng'), dataIndex: 'name', minWidth: '120', tooltip: true },
        { title: t('userCircle.renQunMiaoShu'), dataIndex: 'description', tooltip: true },
        {
          title: t('userCircle.gengXinXinXi'),
          dataIndex: 'createTime',
          width: '180',
          date: true
        },
        {
          title: t('userCircle.gengXinShiJian'),
          dataIndex: 'updateTime',
          width: '180',
          date: true
        }
      ]

      state.tableHeaders.push({
        title: t('btn.operation'),
        width: '180',
        fixed: 'right',
        ellipsis: true,
        operation: [
          { clickFun: (item) => edit(item, 'view'), label: t('btn.view'), auth: ['cdp.user_circle.locate'] },
          { clickFun: (item) => edit(item, 'edit'), label: t('btn.edit'), auth: ['cdp.user_circle_edit'] },
          { clickFun: copy, label: t('btn.copy'), auth: ['cdp.user_circle.copy'] },
          { clickFun: snapshotStatistics, label: t('userCircle.kuaiZhaoTongJi'), auth: ['cdp.user_circle_statistics'] },
          { clickFun: toUserPage, label: t('userCircle.yongHuQingDan'), auth: ['cdp.user_circle.user_list'] },
          { clickFun: toHistoryPage, label: t('userCircle.kuaiZhaoLiShi'), auth: ['cdp.user_circle_history'] },
          {
            clickFun: (item) => batchSnapshot(item.id),
            label: t('userCircle.zhiXingKuaiZhao'),
            auth: ['cdp.user_circle_history']
          },
          {
            clickFun: (item) => openExportDialog(item),
            label: t('userCircle.daoChu'),
            auth: ['cdp.user_circle.export']
          },
          { clickFun: (item) => del(item, 'tag'), label: t('btn.delete'), auth: ['cdp.user_circle.delete'] }
        ]
      })
    })

    onMounted(() => {
      getExportModel()
      getTableData()
    })

    const getTableData = (page = 0) => {
      state.loading = true
      let params = {
        page: page,
        size: state.searchParams.size,
        sort: 'createTime,desc',
        expression: $expression(state.searchParams)
      }
      state.searchParams.page = page + 1

      $api.userCircle
        .page(params)
        .then((res) => {
          state.tableData = res.content
          state.currentPage = page
          state.totalCount = res.totalElements
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }

    const reset = () => {
      state.searchParams = {}
      getTableData()
    }

    const search = () => {
      getTableData()
    }

    // 选中
    const onSelection = (selection) => {
      state.selection = selection.map((item) => item.id)
    }

    // 全量、批量快照
    const batchSnapshot = async (tagTarget) => {
      const BAR_DATA = $tool.data.get('BAR_DATA')
      const tenantId = $tool.data.get('tenantId')
      await $api.userCircle.batchSnapshot({
        audienceTarget: tagTarget || state.selection.toString(),
        collectionId: BAR_DATA.collectionId,
        description: '',
        enableDetail: true,
        executeType: 'manual',
        name: 'cdp_audience_snapshot_' + new Date().getTime(),
        taskType: 'cdp_audience_snapshot',
        tenantId: tenantId
      })
      ElMessage({ type: 'success', message: t('userCircle.kuaiZhaoZhiXingChengGong') })
    }

    return {
      ...toRefs(state),
      getTableData,
      reset,
      del,
      search,
      batchSnapshot,
      onSelection,
      edit,
      exportDialogVisible,
      exportModel,
      exportModelName,
      exportTargetItem,
      getExportModel,
      openExportDialog,
      confirmExport,
      cancelExport
    }
  }
}
