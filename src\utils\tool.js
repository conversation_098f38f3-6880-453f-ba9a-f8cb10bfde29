const tool = {}

/* localStorage */
tool.data = {
	set(table, settings) {
		let _set = JSON.stringify(settings)
		return localStorage.setItem(table, _set)
	},
	get(table) {
		let data = localStorage.getItem(table)
		try {
			data = JSON.parse(data)
		} catch (err) {
			return null
		}
		return data
	},
	remove(table) {
		return localStorage.removeItem(table)
	},
	clear() {
		return localStorage.clear()
	}
}

/*sessionStorage*/
tool.session = {
	set(table, settings) {
		let _set = JSON.stringify(settings)
		return sessionStorage.setItem(table, _set)
	},
	get(table) {
		let data = sessionStorage.getItem(table)
		try {
			data = JSON.parse(data)
		} catch (err) {
			return null
		}
		return data
	},
	remove(table) {
		return sessionStorage.removeItem(table)
	},
	clear() {
		return sessionStorage.clear()
	}
}

/* Fullscreen */
tool.screen = function (element) {
	let isFull = !!(document.webkitIsFullScreen || document.mozFullScreen || document.msFullscreenElement || document.fullscreenElement)
	if (isFull) {
		if (document.exitFullscreen) {
			document.exitFullscreen()
		} else if (document.msExitFullscreen) {
			document.msExitFullscreen()
		} else if (document.mozCancelFullScreen) {
			document.mozCancelFullScreen()
		} else if (document.webkitExitFullscreen) {
			document.webkitExitFullscreen()
		}
	} else {
		if (element.requestFullscreen) {
			element.requestFullscreen()
		} else if (element.msRequestFullscreen) {
			element.msRequestFullscreen()
		} else if (element.mozRequestFullScreen) {
			element.mozRequestFullScreen()
		} else if (element.webkitRequestFullscreen) {
			element.webkitRequestFullscreen()
		}
	}
}

/* 复制对象 */
tool.objCopy = function (obj) {
	return JSON.parse(JSON.stringify(obj))
}

/* 日期格式化 */
tool.dateFormat = function (date, fmt = 'yyyy-MM-dd hh:mm:ss') {
	date = new Date(date)
	let o = {
		'M+': date.getMonth() + 1, //月份
		'd+': date.getDate(), //日
		'h+': date.getHours(), //小时
		'm+': date.getMinutes(), //分
		's+': date.getSeconds(), //秒
		'q+': Math.floor((date.getMonth() + 3) / 3), //季度
		S: date.getMilliseconds() //毫秒
	}
	if (/(y+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
	}
	for (let k in o) {
		if (new RegExp('(' + k + ')').test(fmt)) {
			fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
		}
	}
	return fmt
}

/* 千分符 */
tool.groupSeparator = function (num) {
	num = num + ''
	if (!num.includes('.')) {
		num += '.'
	}
	return num
		.replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
			return $1 + ','
		})
		.replace(/\.$/, '')
}

/**
 * 生成编号
 */
tool.getUUID = function (name = '', num = 16) {
	if (num < 1) throw new Error('Length must be at least 1')

	const randomSegment = () => Math.random().toString(36).slice(2)
	let uuid = ''
	while (uuid.length < num) {
		uuid += randomSegment()
	}
	uuid = uuid.slice(0, num).toUpperCase()
	return name ? `${name}-${uuid}` : uuid
}

// 生成屏幕指纹
tool.getCustomerUuid = () => {
	const canvas = document.createElement('canvas')
	canvas.width = 200
	canvas.height = 200
	const ctx = canvas.getContext('2d')
	ctx.fillStyle = 'red'
	ctx.fillRect(25, 25, 100, 100)
	let fingerprint = canvas.toDataURL()
	fingerprint = fingerprint.replace(/[^0-9]/g, '')
	fingerprint = fingerprint.substring(0, 11)
	return fingerprint
}

// base64 转换为 blob
tool.dataToBlob = function (data_base64) {
	let arr = data_base64.split(','),
		mime = arr[0].match(/:(.*?);/)[1],
		bstr = atob(arr[1]),
		n = bstr.length,
		u8arr = new Uint8Array(n)
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n)
	}
	return new Blob([u8arr], { type: mime })
}

tool.downloadFile = function (fileUrl, fileName) {
	getBlob(fileUrl).then((blob) => {
		save(blob, fileName)
	})
}

tool.getMaterial = function (materialInfo, fileItem) {
	return {
		...materialInfo,
		materialName: fileItem.materialName || fileItem.name,
		materialUrl: fileItem.materialUrl,
		materialFormat: fileItem.materialFormat,
		materialType: fileItem.materialType,
		materialWidth: fileItem.materialWidth,
		materialHeight: fileItem.materialHeight,
		materialProportion: '',
		materialSize: fileItem.materialSize,
		materialDuration: fileItem.videoTime || 0
	}
}

tool.getMaterialPreview = function (material, width, v_second) {
	width = width || 400
	v_second = v_second || 1
	switch (material.materialFormat) {
		case 'jpg':
		case 'jpeg':
		case 'png':
		case 'bmp':
		case 'gif':
		case 'webp':
		case 'tiff':
			return `${material.materialUrl}?x-oss-process=image/resize,w_${width},m_lfit`
		case 'mp4':
			return `${material.materialUrl}?x-oss-process=video/snapshot,t_${v_second * 1000},f_jpg,w_${width}`
	}
}

function getBlob(fileUrl) {
	return new Promise((resolve) => {
		const xhr = new XMLHttpRequest()
		xhr.open('GET', fileUrl, true)
		xhr.responseType = 'blob'
		xhr.onload = () => {
			if (xhr.status === 200) {
				resolve(xhr.response)
			}
		}
		xhr.send()
	})
}

function save(blob, filename) {
	if (window.navigator.msSaveOrOpenBlob) {
		navigator.msSaveBlob(blob, filename)
	} else {
		const link = document.createElement('a')
		const body = document.querySelector('body')

		let binaryData = []
		binaryData.push(blob)
		link.href = window.URL.createObjectURL(new Blob(binaryData))
		link.download = filename

		// fix Firefox
		link.style.display = 'none'
		body.appendChild(link)

		link.click()
		body.removeChild(link)

		window.URL.revokeObjectURL(link.href)
	}
}

// 装换 cron 表达式

tool.parseCronToChinese = (cronExpression) => {
	const parts = cronExpression.split(' ')
	const minutes = parts[0]
	const hours = parts[1]
	const daysOfMonth = parts[2]
	const months = parts[3]
	const daysOfWeek = parts[4]

	let translation = ''

	// Minutes
	if (minutes === '*') {
		translation += '每分钟'
	} else if (minutes.includes(',')) {
		const minuteValues = minutes.split(',')
		translation += '每'
		for (let i = 0; i < minuteValues.length; i++) {
			translation += `${minuteValues[i]}分钟`
			if (i < minuteValues.length - 1) {
				translation += '和'
			}
		}
	} else if (minutes.includes('-')) {
		const minuteRange = minutes.split('-')
		translation += `从${minuteRange[0]}分钟到${minuteRange[1]}分钟`
	} else {
		translation += `每${minutes}分钟`
	}

	// Hours
	if (hours === '*') {
		translation += '每小时'
	} else if (hours.includes(',')) {
		const hourValues = hours.split(',')
		translation += '每'
		for (let i = 0; i < hourValues.length; i++) {
			translation += `${hourValues[i]}点`
			if (i < hourValues.length - 1) {
				translation += '和'
			}
		}
	} else if (hours.includes('-')) {
		const hourRange = hours.split('-')
		translation += `从${hourRange[0]}点到${hourRange[1]}点`
	} else {
		translation += `每${hours}点`
	}

	// Days of Month
	if (daysOfMonth === '*') {
		translation += '每天'
	} else if (daysOfMonth.includes(',')) {
		const dayValues = daysOfMonth.split(',')
		translation += '每'
		for (let i = 0; i < dayValues.length; i++) {
			translation += `第${dayValues[i]}天`
			if (i < dayValues.length - 1) {
				translation += '和'
			}
		}
	} else if (daysOfMonth.includes('-')) {
		const dayRange = daysOfMonth.split('-')
		translation += `从第${dayRange[0]}天到第${dayRange[1]}天`
	} else {
		translation += `每第${daysOfMonth}天`
	}

	// Months
	if (months === '*') {
		translation += '每月'
	} else if (months.includes(',')) {
		const monthValues = months.split(',')
		translation += '每'
		for (let i = 0; i < monthValues.length; i++) {
			const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
			translation += `${monthNames[parseInt(monthValues[i]) - 1]}`
			if (i < monthValues.length - 1) {
				translation += '和'
			}
		}
	} else if (months.includes('-')) {
		const monthRange = months.split('-')
		const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
		translation += `从${monthNames[parseInt(monthRange[0]) - 1]}到${monthNames[parseInt(monthRange[1]) - 1]}`
	} else {
		const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
		translation += `每${monthNames[parseInt(months) - 1]}`
	}

	// Days of Week
	if (daysOfWeek === '*') {
		translation += '每天'
	} else if (daysOfWeek.includes(',')) {
		const dayOfWeekValues = daysOfWeek.split(',')
		translation += '每'
		for (let i = 0; i < dayOfWeekValues.length; i++) {
			const dayOfWeekNames = ['星期天', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
			translation += `${dayOfWeekNames[parseInt(dayOfWeekValues[i]) - 1]}`
			if (i < dayOfWeekValues.length - 1) {
				translation += '和'
			}
		}
	} else if (daysOfWeek.includes('-')) {
		const dayOfWeekRange = daysOfWeek.split('-')
		const dayOfWeekNames = ['星期天', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
		translation += `从${dayOfWeekNames[parseInt(dayOfWeekRange[0]) - 1]}到${dayOfWeekNames[parseInt(dayOfWeekRange[1]) - 1]}`
	} else {
		const dayOfWeekNames = ['星期天', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
		translation += `每${dayOfWeekNames[parseInt(daysOfWeek) - 1]}`
	}

	return translation
}

/* 防抖函数 */
tool.debounce = function (func, delay) {
	let timer
	return function () {
		clearTimeout(timer)
		timer = setTimeout(() => {
			func.apply(this, arguments)
		}, delay)
	}
}

/* 节流函数 */
tool.throttle = function (func, delay) {
	let previous = 0
	return function () {
		const now = Date.now()
		if (now - previous > delay) {
			func.apply(this, arguments)
			previous = now
		}
	}
}

export default tool
