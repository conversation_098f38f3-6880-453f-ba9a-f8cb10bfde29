import lwFilterList from '@/components/lwFilterList/index.vue'
import lwMappingEdit from '@/components/lwMappingEdit/index.vue'
import { ElMessage } from 'element-plus'
import { computed, getCurrentInstance, onMounted, provide, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'subscribeDatabaseEdit',
  components: {
    lwMappingEdit,
    lwFilterList
  },
  setup(props, { attrs, slots, emit }) {
    const store = useStore()
    const route = useRoute()
    const {
      proxy: { $api, $tool, $bus, t }
    } = getCurrentInstance()

    const id = computed(() => route.query.id)
    const isView = computed(() => !!route.query.isView)
    const copy = computed(() => !!route.query.copy)

    const dataForm = ref({
      name: '',
      aliasName: '',
      targetType: 'database',
      mappingList: []
    })
    const dataFormRef = ref(null)
    const mappingEditRef = ref(null)
    const loading = ref(false)

    // 存储模型
    const persistentlList = ref([])
    const getPersistentList = async () => {
      let res = await $api.modelPersistent.list()
      persistentlList.value = res.map((item) => ({ label: item.aliasName, value: item.id, fields: item.fields }))
    }

    // 传输模型
    const transmissionList = ref([])
    const getTransmissionList = async () => {
      let res = await $api.modelTransmission.list()
      transmissionList.value = res.map((item) => ({ label: item.aliasName, value: item.id, fields: item.fields }))
    }

    const subscribeList = computed(() => {
      return dataForm.value.subscribeModelType == 'transfer' ? transmissionList.value : persistentlList.value
    })

    const config = computed(() => {
      return {
        labelWidth: '130px',
        labelPosition: 'top',
        size: 'default',
        formItems: [
          { label: t('subscribeDatabaseEdit.jiBenXinXi'), component: 'divider' },
          {
            label: t('subscribeDatabaseEdit.mingCheng'),
            name: 'aliasName',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('subscribeDatabaseEdit.bianMa'),
            name: 'name',
            value: '',
            span: 8,
            component: 'input',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            },
            tips: t('subscribeDatabaseEdit.ZNWXXZMSZHXH'),
            rules: [
              { required: true, message: t('subscribeDatabaseEdit.qingShuRu'), trigger: 'blur' },
              { pattern: /^[a-z0-9_]+$/, message: t('subscribeDatabaseEdit.ZNWXXZMSZHXH'), trigger: 'blur' },
              { max: 20, message: t('subscribeDatabaseEdit.CDBNCGGZF'), trigger: 'blur' }
            ]
          },
          {
            label: t('subscribeDatabaseEdit.chuanShuMoXing'),
            name: 'transferModelId',
            value: '',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingXuanZe'),
              items: transmissionList.value
            },
            tips: t('subscribeDatabaseEdit.KXZCSMXZYJLD'),
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingXuanZe'), trigger: 'blur' }]
          },

          {
            label: t('subscribeDatabaseEdit.shuJuTuiSongMoXing'),
            name: 'subscribeModelType',
            value: 'persistent',
            span: 8,
            component: 'radio',
            options: {
              items: [
                { label: t('subscribeDatabaseEdit.chuanShuMoXing'), value: 'transfer' },
                { label: t('subscribeDatabaseEdit.cunChuMoXing'), value: 'persistent' }
              ]
            }
          },

          {
            label: t('subscribeDatabaseEdit.dingYueMoXing'),
            name: 'subscribeModelId',
            value: '',
            span: 8,
            component: 'select',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingXuanZe'),
              items: subscribeList.value
            },
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('subscribeDatabaseEdit.shiFouJiLuRiZhi'),
            name: 'needLogging',
            value: true,
            span: 8,
            component: 'switch',
            options: {
              activeText: t('subscribeDatabaseEdit.jiLu'),
              inactiveText: t('subscribeDatabaseEdit.buJiLu')
            },
            tips: t('subscribeDatabaseEdit.SFXYJLRZKQHJ')
          },
          {
            name: 'dataFilter.dataFilterConditionList',
            value: [],
            span: 24,
            component: 'optionsTable'
          },
          {
            label: t('subscribeDatabaseEdit.beiZhu'),
            name: 'description',
            value: '',
            span: 24,
            component: 'input',
            options: {
              type: 'textarea',
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            }
          },
          { label: t('subscribeDatabaseEdit.shuJuKu'), component: 'divider' },
          {
            label: t('subscribeDatabaseEdit.shuJuKuMingCheng'),
            name: 'databaseConfigModel.database',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('subscribeDatabaseEdit.shuJuKuLeiXing'),
            name: 'databaseConfigModel.databaseType',
            value: '',
            span: 6,
            component: 'select',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingXuanZe'),
              items: [
                { label: 'mysql', value: 'mysql' },
                { label: 'sqlserver', value: 'sqlserver' },
                { label: 'oracle', value: 'oracle' }
              ]
            },
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingXuanZe'), trigger: 'blur' }]
          },
          {
            label: t('subscribeDatabaseEdit.biaoMing'),
            name: 'databaseConfigModel.tableName',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('subscribeDatabaseEdit.zhuJian'),
            name: 'databaseConfigModel.primaryKey',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('subscribeDatabaseEdit.yongHuMing'),
            name: 'databaseConfigModel.userName',
            value: '',
            span: 6,
            component: 'input',
            options: {
              autocomplete: 'new-passwords',
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('subscribeDatabaseEdit.miMa'),
            name: 'databaseConfigModel.password',
            value: '',
            span: 6,
            component: 'input',
            options: {
              autocomplete: 'new-passwords',
              type: 'password',
              showPassword: true,
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('subscribeDatabaseEdit.guoLüTiaoJian'),
            name: 'databaseConfigModel.expression',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            }
          },
          {
            label: t('subscribeDatabaseEdit.paiXu'),
            name: 'databaseConfigModel.orderby',
            value: 0,
            span: 6,
            component: 'number',
            options: {
              min: 0
            }
          },
          {
            label: t('subscribeDatabaseEdit.zhuJi'),
            name: 'databaseConfigModel.host',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          {
            label: t('subscribeDatabaseEdit.duanKou'),
            name: 'databaseConfigModel.port',
            value: '',
            span: 6,
            component: 'input',
            options: {
              placeholder: t('subscribeDatabaseEdit.qingShuRu')
            },
            rules: [{ required: true, message: t('subscribeDatabaseEdit.qingShuRu'), trigger: 'blur' }]
          },
          { label: t('subscribeDatabaseEdit.yingShePeiZhi'), component: 'divider' },
          { component: 'mappingEdit' }
        ]
      }
    })

    // 查询详情
    const fetchDetail = async () => {
      let res = await $api.subscribeApi.info({ id: id.value })
      dataForm.value = res
    }

    // 用户模型
    const optionsRedirect = ref({
      consumerMap: {},
      typeGroup: '',
      behaviorList: []
    })

    watch(
      () => dataForm.value.subscribeModelId,
      (val) => {
        optionsRedirect.value.consumerMap = subscribeList.value.find((x) => x.value == val) || []
      }
    )

    // 传参
    provide('optionsRedirect', optionsRedirect)

    onMounted(() => {
      loading.value = true
      Promise.all([getPersistentList(), getTransmissionList()])
        .then(() => {
          if (id.value) {
            fetchDetail()
          }
        })
        .finally(() => {
          loading.value = false
        })
    })

    // 保存
    const save = () => {
      dataFormRef.value.validate(async (valid, obj) => {
        if (valid) {
          let { ...params } = JSON.parse(JSON.stringify(dataForm.value))

          await $api.subscribeApi[id.value && !copy.value ? 'edit' : 'add'](params)
          ElMessage({ type: 'success', message: t('subscribeDatabaseEdit.baoCunChengGong') })
          close()
        } else {
          dataFormRef.value.scrollToField(Object.keys(obj)[0])
          return false
        }
      })
    }

    // 关闭
    const close = () => {
      store.state.viewTags.closeTagAndJump('/subscribe/api', 'subscribeApi')
    }

    return {
      dataForm,
      mappingEditRef,
      dataFormRef,
      config,
      loading,
      isView,
      save,
      close
    }
  }
}
