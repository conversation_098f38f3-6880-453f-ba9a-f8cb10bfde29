# Vite Vue3 Backstage

## 项目简介

`vite-vue3-backstage` 是一个基于 Vite + Vue 3 + Element Plus 的后台管理系统模板，集成了 Vue Router、Vuex、Axios、国际化（Vue I18n）等常用库，并提供了 PWA 支持。

## 目录结构

```
├── src/                # 源码目录
│   ├── api/           # API 请求封装
│   ├── assets/        # 静态资源
│   ├── components/    # 组件库
│   ├── locales/       # 国际化文件
│   ├── router/        # 路由配置
│   ├── store/         # Vuex 状态管理
│   ├── utils/         # 工具函数
│   ├── views/         # 视图页面
│   ├── App.vue        # 根组件
│   ├── main.js        # 入口文件
│
├── public/            # 公共资源目录
├── vite.config.js     # Vite 配置文件
├── package.json       # 依赖管理
└── README.md          # 项目说明
```

## 安装依赖

```bash
npm install
```

## 安装依赖（老版本）
```bash
npm install --legacy-peer-deps
npm install cross-env --legacy-peer-deps
```

## 启动项目

```bash
npm run dev
```

## 构建项目

```bash
npm run build
```

- 生产环境构建：

  ```bash
  npm run build
  ```

- UAT 测试环境构建：

  ```bash
  npm run build:uat
  ```

## 预览构建结果

```bash
npm run preview
```

## 技术栈

- [Vue 3](https://vuejs.org/)
- [Vite](https://vitejs.dev/)
- [Element Plus](https://element-plus.org/)
- [Vue Router](https://router.vuejs.org/)
- [Vuex](https://vuex.vuejs.org/)
- [Axios](https://axios-http.com/)
- [Day.js](https://day.js.org/)
- [PWA 支持](https://vite-plugin-pwa.netlify.app/)

## 主要功能

- Vue 3 组合式 API 开发
- Element Plus 组件库
- Vue Router 路由管理
- Vuex 状态管理
- Axios 请求封装
- 国际化（Vue I18n）
- SVG 图标自动注册
- PWA 支持
- 进度条加载（NProgress）
